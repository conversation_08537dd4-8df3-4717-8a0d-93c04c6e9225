<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Leap Motion 连接测试</title>
    <script src="leap-1.1.1.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .status-connected { background-color: #4CAF50; }
        .status-disconnected { background-color: #f44336; }
        .status-warning { background-color: #ff9800; }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .data-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(5px);
        }
        
        .hand-visual {
            width: 200px;
            height: 200px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            margin: 10px auto;
            position: relative;
            background: rgba(0, 0, 0, 0.2);
        }
        
        .finger {
            position: absolute;
            background: #4CAF50;
            border-radius: 50%;
            width: 8px;
            height: 8px;
            transition: all 0.1s ease;
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-info { background: rgba(33, 150, 243, 0.2); }
        .log-success { background: rgba(76, 175, 80, 0.2); }
        .log-warning { background: rgba(255, 152, 0, 0.2); }
        .log-error { background: rgba(244, 67, 54, 0.2); }
        
        .stats-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .stats-table th,
        .stats-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .stats-table th {
            background: rgba(255, 255, 255, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖐️ Leap Motion 连接测试</h1>
            <p>检测 Leap Motion 设备连接状态和手部识别功能</p>
        </div>
        
        <!-- 连接状态面板 -->
        <div class="status-panel">
            <h3>连接状态</h3>
            <div id="connection-status">
                <span class="status-indicator status-disconnected"></span>
                <span>正在连接...</span>
            </div>
            <div id="device-info" style="margin-top: 10px; font-size: 14px; opacity: 0.8;"></div>
        </div>
        
        <!-- 控制按钮 -->
        <div class="controls">
            <button class="btn" onclick="connectLeap()">连接</button>
            <button class="btn" onclick="disconnectLeap()">断开连接</button>
            <button class="btn" onclick="clearLogs()">清除日志</button>
            <button class="btn" onclick="togglePause()">暂停/继续</button>
        </div>
        
        <!-- 数据显示区域 -->
        <div class="data-grid">
            <!-- 手部检测 -->
            <div class="data-card">
                <h4>手部检测</h4>
                <div id="hands-count">检测到的手: 0</div>
                <div id="hands-info"></div>
                <div class="hand-visual" id="hand-visual"></div>
            </div>
            
            <!-- 手势数据 -->
            <div class="data-card">
                <h4>手势数据</h4>
                <table class="stats-table">
                    <tr><th>属性</th><th>值</th></tr>
                    <tr><td>帧率 (FPS)</td><td id="fps">0</td></tr>
                    <tr><td>手掌位置</td><td id="palm-position">-</td></tr>
                    <tr><td>手掌法向量</td><td id="palm-normal">-</td></tr>
                    <tr><td>手掌方向</td><td id="palm-direction">-</td></tr>
                    <tr><td>抓握强度</td><td id="grab-strength">-</td></tr>
                    <tr><td>捏合强度</td><td id="pinch-strength">-</td></tr>
                </table>
            </div>
            
            <!-- 手指信息 -->
            <div class="data-card">
                <h4>手指信息</h4>
                <div id="fingers-info">
                    <div>将手放在 Leap Motion 上方查看手指数据</div>
                </div>
            </div>
            
            <!-- 系统日志 -->
            <div class="data-card">
                <h4>系统日志</h4>
                <div class="log-container" id="log-container"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let controller;
        let isPaused = false;
        let frameCount = 0;
        let lastTime = Date.now();
        let fps = 0;
        
        // 初始化 Leap Motion
        function initLeapMotion() {
            addLog('正在初始化 Leap Motion...', 'info');
            
            controller = new Leap.Controller({
                enableGestures: true,
                frameEventName: 'animationFrame'
            });
            
            // 设置事件监听器
            setupEventListeners();
            
            // 开始连接
            controller.connect();
            
            addLog('Leap Motion 控制器已创建', 'success');
        }
        
        // 设置事件监听器
        function setupEventListeners() {
            // 连接事件
            controller.on('connect', function() {
                addLog('已连接到 Leap Motion 服务', 'success');
                updateConnectionStatus('connected', '已连接到 Leap Motion 服务');
            });
            
            controller.on('ready', function() {
                addLog(`Leap Motion 服务就绪 - 版本: ${controller.connection.protocol.serviceVersion}`, 'success');
            });
            
            controller.on('disconnect', function() {
                addLog('与 Leap Motion 服务断开连接', 'warning');
                updateConnectionStatus('disconnected', '已断开连接');
            });
            
            // 设备事件
            controller.on('deviceAttached', function(device) {
                addLog(`设备已连接: ${device.type || 'Leap Motion'}`, 'success');
                updateDeviceInfo(device);
            });
            
            controller.on('deviceRemoved', function(device) {
                addLog(`设备已移除: ${device.type || 'Leap Motion'}`, 'warning');
            });
            
            controller.on('deviceStreaming', function(device) {
                addLog('设备开始数据流传输', 'success');
                updateConnectionStatus('streaming', '设备正在传输数据');
            });
            
            controller.on('deviceStopped', function(device) {
                addLog('设备停止数据流传输', 'warning');
            });
            
            // 帧数据处理
            controller.on('frame', function(frame) {
                if (!isPaused) {
                    processFrame(frame);
                }
            });
            
            // 焦点事件
            controller.on('focus', function() {
                addLog('应用获得焦点', 'info');
            });
            
            controller.on('blur', function() {
                addLog('应用失去焦点', 'info');
            });
        }
        
        // 处理帧数据
        function processFrame(frame) {
            // 计算帧率
            frameCount++;
            const currentTime = Date.now();
            if (currentTime - lastTime >= 1000) {
                fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                document.getElementById('fps').textContent = fps;
                frameCount = 0;
                lastTime = currentTime;
            }
            
            // 更新手部信息
            updateHandsInfo(frame);
            
            // 更新手势数据
            updateGestureData(frame);
            
            // 更新手指信息
            updateFingersInfo(frame);
            
            // 更新可视化
            updateHandVisualization(frame);
        }
        
        // 更新手部信息
        function updateHandsInfo(frame) {
            const handsCount = frame.hands.length;
            document.getElementById('hands-count').textContent = `检测到的手: ${handsCount}`;
            
            let handsInfo = '';
            frame.hands.forEach((hand, index) => {
                handsInfo += `<div>手 ${index + 1}: ${hand.type} (ID: ${hand.id})</div>`;
            });
            document.getElementById('hands-info').innerHTML = handsInfo || '<div>未检测到手</div>';
        }
        
        // 更新手势数据
        function updateGestureData(frame) {
            if (frame.hands.length > 0) {
                const hand = frame.hands[0];
                
                document.getElementById('palm-position').textContent = 
                    `(${hand.palmPosition[0].toFixed(1)}, ${hand.palmPosition[1].toFixed(1)}, ${hand.palmPosition[2].toFixed(1)})`;
                
                document.getElementById('palm-normal').textContent = 
                    `(${hand.palmNormal[0].toFixed(2)}, ${hand.palmNormal[1].toFixed(2)}, ${hand.palmNormal[2].toFixed(2)})`;
                
                document.getElementById('palm-direction').textContent = 
                    `(${hand.direction[0].toFixed(2)}, ${hand.direction[1].toFixed(2)}, ${hand.direction[2].toFixed(2)})`;
                
                document.getElementById('grab-strength').textContent = hand.grabStrength.toFixed(2);
                document.getElementById('pinch-strength').textContent = hand.pinchStrength.toFixed(2);
            } else {
                ['palm-position', 'palm-normal', 'palm-direction', 'grab-strength', 'pinch-strength'].forEach(id => {
                    document.getElementById(id).textContent = '-';
                });
            }
        }
        
        // 更新手指信息
        function updateFingersInfo(frame) {
            let fingersHtml = '';
            
            if (frame.hands.length > 0) {
                frame.hands.forEach((hand, handIndex) => {
                    fingersHtml += `<h5>手 ${handIndex + 1} (${hand.type})</h5>`;
                    
                    hand.fingers.forEach((finger, fingerIndex) => {
                        const fingerNames = ['拇指', '食指', '中指', '无名指', '小指'];
                        fingersHtml += `
                            <div style="margin: 5px 0; font-size: 12px;">
                                <strong>${fingerNames[finger.type]}:</strong> 
                                长度: ${finger.length.toFixed(1)}mm, 
                                ${finger.extended ? '伸展' : '弯曲'}
                            </div>
                        `;
                    });
                });
            } else {
                fingersHtml = '<div>将手放在 Leap Motion 上方查看手指数据</div>';
            }
            
            document.getElementById('fingers-info').innerHTML = fingersHtml;
        }
        
        // 更新手部可视化
        function updateHandVisualization(frame) {
            const visual = document.getElementById('hand-visual');
            visual.innerHTML = '';
            
            if (frame.hands.length > 0) {
                const hand = frame.hands[0];
                
                // 绘制手指位置
                hand.fingers.forEach((finger, index) => {
                    const tip = finger.tipPosition;
                    const x = (tip[0] + 100) / 200 * 180 + 10; // 映射到可视化区域
                    const y = (100 - tip[1]) / 200 * 180 + 10;
                    
                    const fingerElement = document.createElement('div');
                    fingerElement.className = 'finger';
                    fingerElement.style.left = x + 'px';
                    fingerElement.style.top = y + 'px';
                    fingerElement.style.backgroundColor = finger.extended ? '#4CAF50' : '#ff9800';
                    fingerElement.title = `手指 ${index + 1}`;
                    
                    visual.appendChild(fingerElement);
                });
            }
        }
        
        // 更新连接状态
        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connection-status');
            const indicator = statusElement.querySelector('.status-indicator');
            const text = statusElement.querySelector('span:last-child');
            
            indicator.className = `status-indicator status-${status === 'connected' || status === 'streaming' ? 'connected' : 
                                   status === 'disconnected' ? 'disconnected' : 'warning'}`;
            text.textContent = message;
        }
        
        // 更新设备信息
        function updateDeviceInfo(device) {
            const deviceInfo = document.getElementById('device-info');
            deviceInfo.innerHTML = `
                设备类型: ${device.type || 'Leap Motion'}<br>
                设备ID: ${device.id || 'N/A'}
            `;
        }
        
        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 限制日志条数
            if (logContainer.children.length > 100) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }
        
        // 控制函数
        function connectLeap() {
            if (controller) {
                controller.connect();
                addLog('尝试重新连接...', 'info');
            }
        }
        
        function disconnectLeap() {
            if (controller) {
                controller.disconnect();
                addLog('手动断开连接', 'info');
            }
        }
        
        function clearLogs() {
            document.getElementById('log-container').innerHTML = '';
        }
        
        function togglePause() {
            isPaused = !isPaused;
            addLog(isPaused ? '数据更新已暂停' : '数据更新已恢复', 'info');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成，正在初始化...', 'info');
            initLeapMotion();
        });
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (controller) {
                controller.disconnect();
            }
        });
    </script>
</body>
</html>
