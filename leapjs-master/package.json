{"name": "leapjs", "version": "1.1.1", "description": "JavaScript client for the Leap Motion Controller", "main": "lib", "engines": {"node": "~12.0.0"}, "scripts": {"test": "make test-node test-integration"}, "repository": {"type": "git", "url": "http://github.com/leapmotion/leapjs.git"}, "license": "Apache-2.0", "dependencies": {"gl-matrix": "^3.3.0", "ws": "^7.4.6"}, "devDependencies": {"browserify": "^16.5.0", "chai": "^4.2.0", "grunt": "^1.3.0", "grunt-banner": "^0.6.0", "grunt-browserify": "^6.0.0", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-uglify": "^4.0.1", "grunt-contrib-watch": "^1.1.0", "grunt-exec": "^3.0.0", "grunt-string-replace": "^1.3.1", "jsdoc": "^3.6.6", "load-grunt-tasks": "^5.1.0", "mocha": "^8.2.1", "mocha-headless-chrome": "^3.1.0", "nodemon": "^2.0.6", "uglify-js": "^3.11.5"}}