<!DOCTYPE html>
<html>
  <head>
    <title>Mocha Tests</title>
    <meta charset="utf-8"/>
    <link rel="stylesheet" href="../../node_modules/mocha/mocha.css" />
  </head>
  <body>
    <div id="mocha"></div>
    <script src="../../node_modules/mocha/mocha.js"></script>
    <script src="../../node_modules/chai/chai.js"></script>

    <script>
      mocha.setup({ ui: 'bdd', globals: ['top', '__flash_getTopLocation', '__flash_getWindowLocation'] });
      assert = chai.assert;
    </script>

    <script src="../assertUtil.js"></script>
    <script src="../../leap-1.1.1.js"></script>

    <script>
      // Allow standalone testing of private modules, such as Dialog.
      // This has to come after assertUtil, as that uses module to detect node status
      // in the lib scripts themselves, module is expected always, through either browserify or node.
      window.module = {};
    </script>

    <script src="./common.js"></script>
    <script src="../connection.js"></script>
    <script src="../controller.js"></script>
    <script src="../circular_buffer.js"></script>
    <script src="../frame.js"></script>
    <script src="../hand.js"></script>
    <script src="../interaction_box.js"></script>
    <script src="../pointable.js"></script>
    <script src="../finger.js"></script>
    <script src="../bone.js"></script>

    <script src="../../lib/dialog.js"></script>
    <script src="../dialog.js"></script>

    <script>
        mocha.run();
    </script>
  </body>
</html>
