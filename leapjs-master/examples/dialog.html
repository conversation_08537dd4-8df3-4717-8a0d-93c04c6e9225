<html>
  <head>
    <title>Dialog - Leap</title>
    <!-- This demos the "Leap out of date" warning dialog  -->
    <!--
      Use the following snippet to hide or stylize the dialog.

      <style type="text/css">
        .leapjs-dialog{
          display: none; /* hide warning for v1 users */
        }
      </style>
    -->
    <script src="../leap-1.1.1.js"></script>
    <script>

      var controller = (new Leap.Controller);

      // here we decrement the requestProtocolVersion so that we can spoof being connected to an old client.
      controller.connection.opts.requestProtocolVersion--;

      // on "ready", it should check the version and show the dialog.
      // it won't matter if a Leap is plugged in or not, as long as the Service is running.
      controller.connect();

    </script>
  </head>
  <body>
  <pre>
    <div id="out"></div>
  </pre>
  </body>
</html>