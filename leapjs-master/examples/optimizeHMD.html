<html>
  <head>
    <title>HMD Optimization - Leap</title>
    <script src="../leap-1.1.1.js"></script>
    <script>
      var controller = new Leap.Controller({optimizeHMD: true}).connect();
    </script>
  </head>
  <body>
  <button onclick="controller.setOptimizeHMD(true);">controller.setOptimizeHMD(true);</button>
  <button onclick="controller.setOptimizeHMD(false);">controller.setOptimizeHMD(false);</button>
  <pre>
    <div id="out"></div>
  </pre>
  </body>
</html>