<html>
  <head>
    <title>Dumper - Leap</title>
    <script src="../leap-1.1.1.js"></script>
    <script>
      var paused = false;
      window.onkeypress = function(e) {
        if (e.charCode == 32) {
          if (paused == false) {
            paused = true;
          } else {
            paused = false;
          }
        }
      };
      var controller = new Leap.Controller();
      controller.loop(function(frame) {
        latestFrame = frame;
        if (paused) {
          document.getElementById('pause').innerHTML = "<strong>PAUSED</strong>";
          return;
        }

        var str = "";
        for (var i in frame.handsMap) {
          var hand = frame.handsMap[i];
          str += "<p>" +
            "<strong>Roll:</strong> " + hand.roll() +
            "<br/><strong>Pitch:</strong> " + hand.pitch() +
            "<br/><strong>Yaw:</strong> " + hand.yaw() +
            "</p>";
        }
        console.log(str);
        document.getElementById('out').innerHTML = str;
      });
      controller.on('ready', function() {
          console.log("ready");
      });
      controller.on('connect', function() {
          console.log("connect");
      });
      controller.on('disconnect', function() {
          console.log("disconnect");
      });
      controller.on('focus', function() {
          console.log("focus");
      });
      controller.on('blur', function() {
          console.log("blur");
      });
      controller.on('deviceConnected', function() {
          console.log("deviceConnected");
      });
      controller.on('deviceDisconnected', function() {
          console.log("deviceDisconnected");
      });
    </script>
  </head>
  <body>
    <div id="pause"></div>
    <div id="out"></div>
  </body>
</html>
