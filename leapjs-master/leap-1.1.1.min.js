/*!                                                              
 * LeapJS v1.1.1                                                  
 * http://github.com/leapmotion/leapjs/                                        
 *                                                                             
 * Copyright 2021 Ultraleap, Inc. and other contributors                       
 * Released under the Apache-2.0 license                                       
 * http://github.com/leapmotion/leapjs/blob/master/LICENSE                     
 */ (function(){function t(n,e,r){function i(s,a){if(!e[s]){if(!n[s]){var u="function"==typeof require&&require;if(!a&&u)return u(s,!0);if(o)return o(s,!0);var c=Error("Cannot find module '"+s+"'");throw c.code="MODULE_NOT_FOUND",c}var _=e[s]={exports:{}};n[s][0].call(_.exports,function(t){return i(n[s][1][t]||t)},_,_.exports,t,n,e,r)}return e[s].exports}for(var o="function"==typeof require&&require,s=0;s<r.length;s++)i(r[s]);return i}return t})()({1:[function(t,n,e){t("./pointable");var r=t("gl-matrix"),i=r.vec3,o=r.mat3,s=r.mat4,a=n.exports=function(t,n){this.finger=t,this._center=null,this._matrix=null,this.type=n.type,this.prevJoint=n.prevJoint,this.nextJoint=n.nextJoint,this.width=n.width;var e=[,,,];i.sub(e,n.nextJoint,n.prevJoint),this.length=i.length(e),this.basis=n.basis};a.prototype.left=function(){return this._left||(this._left=0>o.determinant(this.basis[0].concat(this.basis[1]).concat(this.basis[2]))),this._left},a.prototype.matrix=function(){if(this._matrix)return this._matrix;var t=this.basis,n=this._matrix=s.create();return n[0]=t[0][0],n[1]=t[0][1],n[2]=t[0][2],n[4]=t[1][0],n[5]=t[1][1],n[6]=t[1][2],n[8]=t[2][0],n[9]=t[2][1],n[10]=t[2][2],n[3]=this.center()[0],n[7]=this.center()[1],n[11]=this.center()[2],this.left()&&(n[0]*=-1,n[1]*=-1,n[2]*=-1),this._matrix},a.prototype.lerp=function(t,n){i.lerp(t,this.prevJoint,this.nextJoint,n)},a.prototype.center=function(){if(this._center)return this._center;var t=i.create();return this.lerp(t,.5),this._center=t,t},a.prototype.direction=function(){return[-1*this.basis[2][0],-1*this.basis[2][1],-1*this.basis[2][2]]}},{"./pointable":14,"gl-matrix":21}],2:[function(t,n,e){var r=n.exports=function(t){this.pos=0,this._buf=[],this.size=t};r.prototype.get=function(t){if(void 0==t&&(t=0),!(t>=this.size)&&!(t>=this._buf.length))return this._buf[(this.pos-t-1)%this.size]},r.prototype.push=function(t){return this._buf[this.pos%this.size]=t,this.pos++}},{}],3:[function(t,n,e){var r=t("../protocol").chooseProtocol,i=t("events").EventEmitter,o=n.exports=function(t){this.opts=Object.assign({host:"127.0.0.1",scheme:this.getScheme(),port:this.getPort(),background:!1,optimizeHMD:!1,optimizeScreentop:!1,requestProtocolVersion:o.defaultProtocolVersion},t||{}),this.host=this.opts.host,this.port=this.opts.port,this.scheme=this.opts.scheme,this.protocolVersionVerified=!1,this.background=null,this.optimizeHMD=null,this.optimizeScreentop=null,this.on("ready",function(){this.setBackground(this.opts.background),this.setOptimizeHMD(this.opts.optimizeHMD),this.setOptimizeScreentop(this.opts.optimizeScreentop),this.opts.optimizeHMD?console.log("Optimized for head mounted display usage."):this.opts.optimizeScreentop?console.log("Optimized for screentop usage."):console.log("Optimized for desktop usage.")})};o.defaultProtocolVersion=6,o.prototype.getUrl=function(){return this.scheme+"//"+this.host+":"+this.port+"/v"+this.opts.requestProtocolVersion+".json"},o.prototype.getScheme=function(){return"ws:"},o.prototype.getPort=function(){return 6437},o.prototype.setBackground=function(t){this.opts.background=t,this.protocol&&this.protocol.sendBackground&&this.background!==this.opts.background&&(this.background=this.opts.background,this.protocol.sendBackground(this,this.opts.background))},o.prototype.setOptimizeHMD=function(t){this.opts.optimizeHMD=t,this.protocol&&this.protocol.sendOptimizeHMD&&this.optimizeHMD!==this.opts.optimizeHMD&&(!0===t&&(this.optimizeScreentop=!1),this.optimizeHMD=this.opts.optimizeHMD,this.protocol.sendOptimizeHMD(this,this.opts.optimizeHMD))},o.prototype.setOptimizeScreentop=function(t){this.opts.optimizeScreentop=t,this.protocol&&this.protocol.sendOptimizeScreentop&&this.optimizeScreentop!==this.opts.optimizeScreentop&&(!0===t&&(this.optimizeHMD=!1),this.optimizeScreentop=this.opts.optimizeScreentop,this.protocol.sendOptimizeScreentop(this,this.opts.optimizeScreentop))},o.prototype.handleOpen=function(){this.connected||(this.connected=!0,this.emit("connect"))},o.prototype.handleClose=function(t,n){this.connected&&(this.disconnect(),1001===t&&this.opts.requestProtocolVersion>1&&(this.protocolVersionVerified?this.protocolVersionVerified=!1:this.opts.requestProtocolVersion--),this.startReconnection())},o.prototype.startReconnection=function(){var t=this;this.reconnectionTimer||(this.reconnectionTimer=setInterval(function(){t.reconnect()},500))},o.prototype.stopReconnection=function(){this.reconnectionTimer=clearInterval(this.reconnectionTimer)},o.prototype.disconnect=function(t){if(t||this.stopReconnection(),this.socket)return this.socket.close(),delete this.socket,delete this.protocol,delete this.background,delete this.optimizeHMD,delete this.optimizeScreentop,delete this.focusedState,this.connected&&(this.connected=!1,this.emit("disconnect")),!0},o.prototype.reconnect=function(){this.connected?this.stopReconnection():(this.disconnect(!0),this.connect())},o.prototype.handleData=function(t){var n,e=JSON.parse(t);void 0===this.protocol?(n=this.protocol=r(e),this.protocolVersionVerified=!0,this.emit("ready")):n=this.protocol(e),this.emit(n.type,n)},o.prototype.connect=function(){if(!this.socket)return this.socket=this.setupSocket(),!0},o.prototype.send=function(t){this.socket.send(t)},o.prototype.reportFocus=function(t){this.connected&&this.focusedState!==t&&(this.focusedState=t,this.emit(this.focusedState?"focus":"blur"),this.protocol&&this.protocol.sendFocused&&this.protocol.sendFocused(this,this.focusedState))},Object.assign(o.prototype,i.prototype)},{"../protocol":15,events:31}],4:[function(t,n,e){var r=n.exports=t("./base"),i=n.exports=function(t){r.call(this,t);var n=this;this.on("ready",function(){n.startFocusLoop()}),this.on("disconnect",function(){n.stopFocusLoop()})};Object.assign(i.prototype,r.prototype),i.__proto__=r,i.prototype.useSecure=function(){return"https:"===location.protocol},i.prototype.getScheme=function(){return this.useSecure()?"wss:":"ws:"},i.prototype.getPort=function(){return this.useSecure()?6436:6437},i.prototype.setupSocket=function(){var t=this,n=new WebSocket(this.getUrl());return n.onopen=function(){t.handleOpen()},n.onclose=function(n){t.handleClose(n.code,n.reason)},n.onmessage=function(n){t.handleData(n.data)},n.onerror=function(n){t.useSecure()&&"wss:"===t.scheme&&(t.scheme="ws:",t.port=6437,t.disconnect(),t.connect())},n},i.prototype.startFocusLoop=function(){if(!this.focusDetectorTimer){var t=this,n=null;n=void 0!==document.hidden?"hidden":void 0!==document.mozHidden?"mozHidden":void 0!==document.msHidden?"msHidden":void 0!==document.webkitHidden?"webkitHidden":void 0,void 0===t.windowVisible&&(t.windowVisible=void 0===n||!1===document[n]);var e=window.addEventListener("focus",function(n){t.windowVisible=!0,i()}),r=window.addEventListener("blur",function(n){t.windowVisible=!1,i()});this.on("disconnect",function(){window.removeEventListener("focus",e),window.removeEventListener("blur",r)});var i=function(){var e=void 0===n||!1===document[n];t.reportFocus(e&&t.windowVisible)};i(),this.focusDetectorTimer=setInterval(i,100)}},i.prototype.stopFocusLoop=function(){this.focusDetectorTimer&&(clearTimeout(this.focusDetectorTimer),delete this.focusDetectorTimer)}},{"./base":3}],5:[function(t,n,e){var r=t("ws"),i=t("./base"),o=n.exports=function(t){i.call(this,t);var n=this;this.on("ready",function(){n.reportFocus(!0)})};Object.assign(o.prototype,i.prototype),o.__proto__=i,o.prototype.setupSocket=function(){var t=this,n=new r(this.getUrl());return n.on("open",function(){t.handleOpen()}),n.on("message",function(n){t.handleData(n)}),n.on("close",function(n,e){t.handleClose(n,e)}),n.on("error",function(){t.startReconnection()}),n}},{"./base":3,ws:33}],6:[function(t,n,e){(function(e){(function(){var r=t("./frame"),i=t("./hand"),o=t("./pointable"),s=t("./finger"),a=t("./circular_buffer"),u=t("./pipeline"),c=t("events").EventEmitter,_=t("./dialog"),$=n.exports=function(n){var i=void 0!==e&&e.versions&&e.versions.node,o=this;this.inNode=i,n=Object.assign({inNode:this.inNode,frameEventName:this.useAnimationLoop()?"animationFrame":"deviceFrame",suppressAnimationLoop:!this.useAnimationLoop(),loopWhileDisconnected:!0,useAllPlugins:!1,checkVersion:!0},n||{}),this.animationFrameRequested=!1,this.onAnimationFrame=function(t){o.lastConnectionFrame.valid&&o.emit("animationFrame",o.lastConnectionFrame),o.emit("frameEnd",t),o.loopWhileDisconnected&&(!1!==o.connection.focusedState||o.connection.opts.background)?window.requestAnimationFrame(o.onAnimationFrame):o.animationFrameRequested=!1},this.suppressAnimationLoop=n.suppressAnimationLoop,this.loopWhileDisconnected=n.loopWhileDisconnected,this.frameEventName=n.frameEventName,this.useAllPlugins=n.useAllPlugins,this.history=new a(200),this.lastFrame=r.Invalid,this.lastValidFrame=r.Invalid,this.lastConnectionFrame=r.Invalid,this.checkVersion=n.checkVersion,void 0===n.connectionType?this.connectionType=this.inBrowser()?t("./connection/browser"):t("./connection/node"):this.connectionType=n.connectionType,this.connection=new this.connectionType(n),this.streamingCount=0,this.devices={},this.plugins={},this._pluginPipelineSteps={},this._pluginExtendedMethods={},n.useAllPlugins&&this.useRegisteredPlugins(),this.setupFrameEvents(n),this.setupConnectionEvents(),this.startAnimationLoop()};$.prototype.setBackground=function(t){return this.connection.setBackground(t),this},$.prototype.setOptimizeHMD=function(t){return this.connection.setOptimizeHMD(t),this},$.prototype.setOptimizeScreentop=function(t){return this.connection.setOptimizeScreentop(t),this},$.prototype.inBrowser=function(){return!this.inNode},$.prototype.useAnimationLoop=function(){return this.inBrowser()&&!this.inBackgroundPage()},$.prototype.inBackgroundPage=function(){return"undefined"!=typeof chrome&&chrome.extension&&chrome.extension.getBackgroundPage&&chrome.extension.getBackgroundPage()===window},$.prototype.connect=function(){return this.connection.connect(),this},$.prototype.streaming=function(){return this.streamingCount>0},$.prototype.connected=function(){return!!this.connection.connected},$.prototype.startAnimationLoop=function(){this.suppressAnimationLoop||this.animationFrameRequested||(this.animationFrameRequested=!0,window.requestAnimationFrame(this.onAnimationFrame))},$.prototype.disconnect=function(){return this.connection.disconnect(),this},$.prototype.frame=function(t){return this.history.get(t)||r.Invalid},$.prototype.loop=function(t){return t&&("function"==typeof t?this.on(this.frameEventName,t):this.setupFrameEvents(t)),this.connect()},$.prototype.addStep=function(t){this.pipeline||(this.pipeline=new u(this)),this.pipeline.addStep(t)},$.prototype.processFrame=function(t){this.lastConnectionFrame=t,this.startAnimationLoop(),this.emit("deviceFrame",t)},$.prototype.processFinishedFrame=function(t){this.lastFrame=t,t.valid&&(this.lastValidFrame=t),t.controller=this,t.historyIdx=this.history.push(t),!this.pipeline||(t=this.pipeline.run(t))||(t=r.Invalid),this.emit("frame",t),this.emitHandEvents(t)},$.prototype.emitHandEvents=function(t){for(var n=0;n<t.hands.length;n++)this.emit("hand",t.hands[n])},$.prototype.setupFrameEvents=function(t){t.frame&&this.on("frame",t.frame),t.hand&&this.on("hand",t.hand)},$.prototype.setupConnectionEvents=function(){var t=this;this.connection.on("frame",function(n){t.processFrame(n)}),this.on(this.frameEventName,function(n){t.processFinishedFrame(n)});var n=function(){if(t.connection.opts.requestProtocolVersion<5&&0==t.streamingCount){t.streamingCount=1;var e={attached:!0,streaming:!0,type:"unknown",id:"Lx00000000000"};t.devices[e.id]=e,t.emit("deviceAttached",e),t.emit("deviceStreaming",e),t.emit("streamingStarted",e),t.connection.removeListener("frame",n)}},e=function(){if(t.streamingCount>0){for(var n in t.devices)t.emit("deviceStopped",t.devices[n]),t.emit("deviceRemoved",t.devices[n]);for(var n in t.emit("streamingStopped",t.devices[n]),t.streamingCount=0,t.devices)delete t.devices[n]}};this.connection.on("focus",function(){t.loopWhileDisconnected&&t.startAnimationLoop(),t.emit("focus")}),this.connection.on("blur",function(){t.emit("blur")}),this.connection.on("protocol",function(n){n.on("beforeFrameCreated",function(n){t.emit("beforeFrameCreated",n)}),n.on("afterFrameCreated",function(n,e){t.emit("afterFrameCreated",n,e)}),t.emit("protocol",n)}),this.connection.on("ready",function(){t.checkVersion&&!t.inNode&&t.checkOutOfDate(),t.emit("ready")}),this.connection.on("connect",function(){t.emit("connect"),t.connection.removeListener("frame",n),t.connection.on("frame",n)}),this.connection.on("disconnect",function(){t.emit("disconnect"),e()}),this.connection.on("deviceConnect",function(r){r.state?(t.emit("deviceConnected"),t.connection.removeListener("frame",n),t.connection.on("frame",n)):(t.emit("deviceDisconnected"),e())}),this.connection.on("deviceEvent",function(n){var e=n.state,r=t.devices[e.id],i={};for(var o in e)r&&r.hasOwnProperty(o)&&r[o]==e[o]||(i[o]=!0);t.devices[e.id]=e,i.attached&&t.emit(e.attached?"deviceAttached":"deviceRemoved",e),!i.streaming||(e.streaming?(t.streamingCount++,t.emit("deviceStreaming",e),1==t.streamingCount&&t.emit("streamingStarted",e),i.attached||t.emit("deviceConnected")):i.attached&&e.attached||(t.streamingCount--,t.emit("deviceStopped",e),0==t.streamingCount&&t.emit("streamingStopped",e),t.emit("deviceDisconnected")))}),this.on("newListener",function(t,n){("deviceConnected"==t||"deviceDisconnected"==t)&&console.warn(t+" events are deprecated.  Consider using 'streamingStarted/streamingStopped' or 'deviceStreaming/deviceStopped' instead")})},$.prototype.checkOutOfDate=function(){console.assert(this.connection&&this.connection.protocol);var t=this.connection.protocol.serviceVersion,n=this.connection.protocol.version,e=this.connectionType.defaultProtocolVersion;return e>n&&(console.warn("Your Protocol Version is v"+n+", this app was designed for v"+e),_.warnOutOfDate({sV:t,pV:n}),!0)},$._pluginFactories={},$.plugin=function(t,n){return this._pluginFactories[t]&&console.warn('Plugin "'+t+'" already registered'),this._pluginFactories[t]=n},$.plugins=function(){return Object.keys(this._pluginFactories)};var f=function(t,n,e){-1!=["beforeFrameCreated","afterFrameCreated"].indexOf(n)?this.on(n,e):(this.pipeline||(this.pipeline=new u(this)),this._pluginPipelineSteps[t]||(this._pluginPipelineSteps[t]=[]),this._pluginPipelineSteps[t].push(this.pipeline.addWrappedStep(n,e)))},p=function(t,n,e){var a;switch(this._pluginExtendedMethods[t]||(this._pluginExtendedMethods[t]=[]),n){case"frame":a=r;break;case"hand":a=i;break;case"pointable":a=o,Object.assign(s.prototype,e),Object.assign(s.Invalid,e);break;case"finger":a=s;break;default:throw t+' specifies invalid object type "'+n+'" for prototypical extension'}Object.assign(a.prototype,e),Object.assign(a.Invalid,e),this._pluginExtendedMethods[t].push([a,e])};$.prototype.use=function(t,n){var e,r,i,o;if(!(r="function"==typeof t?t:$._pluginFactories[t]))throw"Leap Plugin "+t+" not found.";if(n||(n={}),this.plugins[t])return Object.assign(this.plugins[t],n),this;for(i in this.plugins[t]=n,o=r.call(this,n))"function"==typeof(e=o[i])?f.call(this,t,i,e):p.call(this,t,i,e);return this},$.prototype.stopUsing=function(t){var n,e,r=this._pluginPipelineSteps[t],i=this._pluginExtendedMethods[t],o=0;if(this.plugins[t]){if(r)for(o=0;o<r.length;o++)this.pipeline.removeStep(r[o]);if(i)for(o=0;o<i.length;o++)for(var s in n=i[o][0],e=i[o][1])delete n.prototype[s],delete n.Invalid[s];return delete this.plugins[t],this}},$.prototype.useRegisteredPlugins=function(){for(var t in $._pluginFactories)this.use(t)},Object.assign($.prototype,c.prototype)}).call(this)}).call(this,t("_process"))},{"./circular_buffer":2,"./connection/browser":4,"./connection/node":5,"./dialog":7,"./finger":8,"./frame":9,"./hand":10,"./pipeline":13,"./pointable":14,_process:32,events:31}],7:[function(t,n,e){(function(t){(function(){var e=n.exports=function(t,n){this.options=n||{},this.message=t,this.createElement()};e.prototype.createElement=function(){this.element=document.createElement("div"),this.element.className="leapjs-dialog",this.element.style.position="fixed",this.element.style.top="8px",this.element.style.left=0,this.element.style.right=0,this.element.style.textAlign="center",this.element.style.zIndex=1e3;var t=document.createElement("div");this.element.appendChild(t),t.style.className="leapjs-dialog",t.style.display="inline-block",t.style.margin="auto",t.style.padding="8px",t.style.color="#222",t.style.background="#eee",t.style.borderRadius="4px",t.style.border="1px solid #999",t.style.textAlign="left",t.style.cursor="pointer",t.style.whiteSpace="nowrap",t.style.transition="box-shadow 1s linear",t.innerHTML=this.message,this.options.onclick&&t.addEventListener("click",this.options.onclick),this.options.onmouseover&&t.addEventListener("mouseover",this.options.onmouseover),this.options.onmouseout&&t.addEventListener("mouseout",this.options.onmouseout),this.options.onmousemove&&t.addEventListener("mousemove",this.options.onmousemove)},e.prototype.show=function(){return document.body.appendChild(this.element),this},e.prototype.hide=function(){return document.body.removeChild(this.element),this},e.warnOutOfDate=function(t){t||(t={});var n="http://developer.leapmotion.com?";for(var r in t.returnTo=window.location.href,t)n+=r+"="+encodeURIComponent(t[r])+"&";var i,o=function(t){if("leapjs-decline-upgrade"!=t.target.id){var e=window.open(n,"_blank","height=800,width=1000,location=1,menubar=1,resizable=1,status=1,toolbar=1,scrollbars=1");window.focus&&e.focus()}return i.hide(),!0};return(i=new e("This site requires Leap Motion Tracking V2.<button id='leapjs-accept-upgrade'  style='color: #444; transition: box-shadow 100ms linear; cursor: pointer; vertical-align: baseline; margin-left: 16px;'>Upgrade</button><button id='leapjs-decline-upgrade' style='color: #444; transition: box-shadow 100ms linear; cursor: pointer; vertical-align: baseline; margin-left: 8px; '>Not Now</button>",{onclick:o,onmousemove:function(t){t.target==document.getElementById("leapjs-decline-upgrade")?(document.getElementById("leapjs-decline-upgrade").style.color="#000",document.getElementById("leapjs-decline-upgrade").style.boxShadow="0px 0px 2px #5daa00",document.getElementById("leapjs-accept-upgrade").style.color="#444",document.getElementById("leapjs-accept-upgrade").style.boxShadow="none"):(document.getElementById("leapjs-accept-upgrade").style.color="#000",document.getElementById("leapjs-accept-upgrade").style.boxShadow="0px 0px 2px #5daa00",document.getElementById("leapjs-decline-upgrade").style.color="#444",document.getElementById("leapjs-decline-upgrade").style.boxShadow="none")},onmouseout:function(){document.getElementById("leapjs-decline-upgrade").style.color="#444",document.getElementById("leapjs-decline-upgrade").style.boxShadow="none",document.getElementById("leapjs-accept-upgrade").style.color="#444",document.getElementById("leapjs-accept-upgrade").style.boxShadow="none"}})).show()},e.hasWarnedBones=!1,e.warnBones=function(){this.hasWarnedBones||(this.hasWarnedBones=!0,console.warn("Your Leap Service is out of date"),void 0!==t&&t.versions&&t.versions.node||this.warnOutOfDate({reason:"bones"}))}}).call(this)}).call(this,t("_process"))},{_process:32}],8:[function(t,n,e){var r=t("./pointable"),i=t("./bone"),o=t("./dialog"),s=n.exports=function(t){r.call(this,t),this.dipPosition=t.dipPosition,this.pipPosition=t.pipPosition,this.mcpPosition=t.mcpPosition,this.carpPosition=t.carpPosition,this.extended=t.extended,this.type=t.type,this.finger=!0,this.positions=[this.carpPosition,this.mcpPosition,this.pipPosition,this.dipPosition,this.tipPosition],t.bases?this.addBones(t):o.warnBones()};Object.assign(s.prototype,r.prototype),s.prototype.addBones=function(t){this.metacarpal=new i(this,{type:0,width:this.width,prevJoint:this.carpPosition,nextJoint:this.mcpPosition,basis:t.bases[0]}),this.proximal=new i(this,{type:1,width:this.width,prevJoint:this.mcpPosition,nextJoint:this.pipPosition,basis:t.bases[1]}),this.medial=new i(this,{type:2,width:this.width,prevJoint:this.pipPosition,nextJoint:this.dipPosition,basis:t.bases[2]}),this.distal=new i(this,{type:3,width:this.width,prevJoint:this.dipPosition,nextJoint:t.btipPosition,basis:t.bases[3]}),this.bones=[this.metacarpal,this.proximal,this.medial,this.distal]},s.prototype.toString=function(){return"Finger [ id:"+this.id+" "+this.length+"mmx | width:"+this.width+"mm | direction:"+this.direction+" ]"},s.Invalid={valid:!1}},{"./bone":1,"./dialog":7,"./pointable":14}],9:[function(t,n,e){var r=t("./hand"),i=t("./pointable"),o=t("gl-matrix"),s=o.mat3,a=o.vec3,u=t("./interaction_box"),c=t("./finger"),_=n.exports=function(t){this.valid=!0,this.id=t.id,this.timestamp=t.timestamp,this.hands=[],this.handsMap={},this.pointables=[],this.fingers=[],t.interactionBox&&(this.interactionBox=new u(t.interactionBox)),this.pointablesMap={},this._translation=t.t,this._rotation=function t(n){return Array.isArray(n)?n.reduce(function(n,e){return n.concat(t(e))},[]):[n]}(t.r),this._scaleFactor=t.s,this.data=t,this.type="frame",this.currentFrameRate=t.currentFrameRate,this.postprocessData(t)};_.prototype.postprocessData=function(t){t||(t=this.data);for(var n=0,e=t.hands.length;n!=e;n++){var o=new r(t.hands[n]);o.frame=this,this.hands.push(o),this.handsMap[o.id]=o}t.pointables.sort(function(t,n){return t.id>n.id?1:n.id>t.id?-1:0});for(var s=0,a=t.pointables.length;s!=a;s++){var u=t.pointables[s],_=u.dipPosition?new c(u):new i(u);_.frame=this,this.addPointable(_)}},_.prototype.addPointable=function(t){if(this.pointables.push(t),this.pointablesMap[t.id]=t,this.fingers.push(t),void 0!==t.handId&&this.handsMap.hasOwnProperty(t.handId)){var n=this.handsMap[t.handId];switch(n.pointables.push(t),n.fingers.push(t),t.type){case 0:n.thumb=t;break;case 1:n.indexFinger=t;break;case 2:n.middleFinger=t;break;case 3:n.ringFinger=t;break;case 4:n.pinky=t}}},_.prototype.pointable=function(t){return this.pointablesMap[t]||i.Invalid},_.prototype.finger=function(t){return this.pointable(t)},_.prototype.hand=function(t){return this.handsMap[t]||r.Invalid},_.prototype.rotationAngle=function(t,n){if(!this.valid||!t.valid)return 0;var e=this.rotationMatrix(t),r=Math.acos((e[0]+e[4]+e[8]-1)*.5);if(r=isNaN(r)?0:r,void 0!==n){var i=this.rotationAxis(t);r*=a.dot(i,a.normalize(a.create(),n))}return r},_.prototype.rotationAxis=function(t){return this.valid&&t.valid?a.normalize(a.create(),[this._rotation[7]-t._rotation[5],this._rotation[2]-t._rotation[6],this._rotation[3]-t._rotation[1]]):a.create()},_.prototype.rotationMatrix=function(t){if(!this.valid||!t.valid)return s.create();var n=s.transpose(s.create(),this._rotation);return s.multiply(s.create(),t._rotation,n)},_.prototype.scaleFactor=function(t){return this.valid&&t.valid?Math.exp(this._scaleFactor-t._scaleFactor):1},_.prototype.translation=function(t){return this.valid&&t.valid?a.subtract(a.create(),this._translation,t._translation):a.create()},_.prototype.toString=function(){var t="Frame [ id:"+this.id+" | timestamp:"+this.timestamp+" | Hand count:("+this.hands.length+") | Pointable count:("+this.pointables.length+")";return t+" ]"},_.prototype.dump=function(){var t="";t+="Frame Info:<br/>",t+=this.toString(),t+="<br/><br/>Hands:<br/>";for(var n=0,e=this.hands.length;n!=e;n++)t+="  "+this.hands[n].toString()+"<br/>";t+="<br/><br/>Pointables:<br/>";for(var r=0,i=this.pointables.length;r!=i;r++)t+="  "+this.pointables[r].toString()+"<br/>";return t+="<br/><br/>Raw JSON:<br/>",t+=JSON.stringify(this.data)},_.Invalid={valid:!1,hands:[],fingers:[],pointables:[],pointable:function(){return i.Invalid},finger:function(){return i.Invalid},hand:function(){return r.Invalid},toString:function(){return"invalid frame"},dump:function(){return this.toString()},rotationAngle:function(){return 0},rotationMatrix:function(){return s.create()},rotationAxis:function(){return a.create()},scaleFactor:function(){return 1},translation:function(){return a.create()}}},{"./finger":8,"./hand":10,"./interaction_box":12,"./pointable":14,"gl-matrix":21}],10:[function(t,n,e){var r=t("./pointable"),i=t("./bone"),o=t("gl-matrix"),s=o.mat3,a=o.vec3,u=n.exports=function(t){this.id=t.id,this.palmPosition=t.palmPosition,this.direction=t.direction,this.palmVelocity=t.palmVelocity,this.palmNormal=t.palmNormal,this.sphereCenter=t.sphereCenter,this.sphereRadius=t.sphereRadius,this.valid=!0,this.pointables=[],this.fingers=[],t.armBasis?this.arm=new i(this,{type:4,width:t.armWidth,prevJoint:t.elbow,nextJoint:t.wrist,basis:t.armBasis}):this.arm=null,this._translation=t.t,this._rotation=function t(n){return Array.isArray(n)?n.reduce(function(n,e){return n.concat(t(e))},[]):[n]}(t.r),this._scaleFactor=t.s,this.timeVisible=t.timeVisible,this.stabilizedPalmPosition=t.stabilizedPalmPosition,this.type=t.type,this.grabStrength=t.grabStrength,this.pinchStrength=t.pinchStrength,this.confidence=t.confidence};u.prototype.finger=function(t){var n=this.frame.finger(t);return n&&n.handId==this.id?n:r.Invalid},u.prototype.rotationAngle=function(t,n){if(!this.valid||!t.valid||!t.hand(this.id).valid)return 0;var e=this.rotationMatrix(t),r=Math.acos((e[0]+e[4]+e[8]-1)*.5);if(r=isNaN(r)?0:r,void 0!==n){var i=this.rotationAxis(t);r*=a.dot(i,a.normalize(a.create(),n))}return r},u.prototype.rotationAxis=function(t){if(!this.valid||!t.valid)return a.create();var n=t.hand(this.id);return n.valid?a.normalize(a.create(),[this._rotation[7]-n._rotation[5],this._rotation[2]-n._rotation[6],this._rotation[3]-n._rotation[1]]):a.create()},u.prototype.rotationMatrix=function(t){if(!this.valid||!t.valid)return s.create();var n=t.hand(this.id);if(!n.valid)return s.create();var e=s.transpose(s.create(),this._rotation);return s.multiply(s.create(),n._rotation,e)},u.prototype.scaleFactor=function(t){if(!this.valid||!t.valid)return 1;var n=t.hand(this.id);return n.valid?Math.exp(this._scaleFactor-n._scaleFactor):1},u.prototype.translation=function(t){if(!this.valid||!t.valid)return a.create();var n=t.hand(this.id);return n.valid?[this._translation[0]-n._translation[0],this._translation[1]-n._translation[1],this._translation[2]-n._translation[2]]:a.create()},u.prototype.toString=function(){return"Hand ("+this.type+") [ id: "+this.id+" | palm velocity:"+this.palmVelocity+" | sphere center:"+this.sphereCenter+" ] "},u.prototype.pitch=function(){return Math.atan2(this.direction[1],-this.direction[2])},u.prototype.yaw=function(){return Math.atan2(this.direction[0],-this.direction[2])},u.prototype.roll=function(){return Math.atan2(this.palmNormal[0],-this.palmNormal[1])},u.Invalid={valid:!1,fingers:[],pointables:[],left:!1,pointable:function(){return r.Invalid},finger:function(){return r.Invalid},toString:function(){return"invalid frame"},dump:function(){return this.toString()},rotationAngle:function(){return 0},rotationMatrix:function(){return s.create()},rotationAxis:function(){return a.create()},scaleFactor:function(){return 1},translation:function(){return a.create()}}},{"./bone":1,"./pointable":14,"gl-matrix":21}],11:[function(t,n,e){n.exports={Controller:t("./controller"),Frame:t("./frame"),Hand:t("./hand"),Pointable:t("./pointable"),Finger:t("./finger"),InteractionBox:t("./interaction_box"),CircularBuffer:t("./circular_buffer"),UI:t("./ui"),JSONProtocol:t("./protocol").JSONProtocol,glMatrix:t("gl-matrix"),mat3:t("gl-matrix").mat3,vec3:t("gl-matrix").vec3,loopController:void 0,version:t("./version.js"),EventEmitter:t("events").EventEmitter,loop:function(t,n){return t&&void 0===n&&"[object Function]"===({}).toString.call(t)&&(n=t,t={}),this.loopController?t&&this.loopController.setupFrameEvents(t):this.loopController=new this.Controller(t),this.loopController.loop(n),this.loopController},plugin:function(t,n){this.Controller.plugin(t,n)}}},{"./circular_buffer":2,"./controller":6,"./finger":8,"./frame":9,"./hand":10,"./interaction_box":12,"./pointable":14,"./protocol":15,"./ui":16,"./version.js":19,events:31,"gl-matrix":21}],12:[function(t,n,e){var r=t("gl-matrix").vec3,i=n.exports=function(t){this.valid=!0,this.center=t.center,this.size=t.size,this.width=t.size[0],this.height=t.size[1],this.depth=t.size[2]};i.prototype.denormalizePoint=function(t){return r.fromValues((t[0]-.5)*this.size[0]+this.center[0],(t[1]-.5)*this.size[1]+this.center[1],(t[2]-.5)*this.size[2]+this.center[2])},i.prototype.normalizePoint=function(t,n){var e=r.fromValues((t[0]-this.center[0])/this.size[0]+.5,(t[1]-this.center[1])/this.size[1]+.5,(t[2]-this.center[2])/this.size[2]+.5);return n&&(e[0]=Math.min(Math.max(e[0],0),1),e[1]=Math.min(Math.max(e[1],0),1),e[2]=Math.min(Math.max(e[2],0),1)),e},i.prototype.toString=function(){return"InteractionBox [ width:"+this.width+" | height:"+this.height+" | depth:"+this.depth+" ]"},i.Invalid={valid:!1}},{"gl-matrix":21}],13:[function(t,n,e){var r=n.exports=function(t){this.steps=[],this.controller=t};r.prototype.addStep=function(t){this.steps.push(t)},r.prototype.run=function(t){for(var n=this.steps.length,e=0;e!=n&&t;e++)t=this.steps[e](t);return t},r.prototype.removeStep=function(t){var n=this.steps.indexOf(t);if(-1===n)throw"Step not found in pipeline";this.steps.splice(n,1)},r.prototype.addWrappedStep=function(t,n){var e=this.controller,r=function(r){var i,o,s;for(o=0,s=(i="frame"==t?[r]:r[t+"s"]||[]).length;o<s;o++)n.call(e,i[o]);return r};return this.addStep(r),r}},{}],14:[function(t,n,e){t("gl-matrix").vec3;var r=n.exports=function(t){this.valid=!0,this.id=t.id,this.handId=t.handId,this.length=t.length,this.tool=t.tool,this.width=t.width,this.direction=t.direction,this.stabilizedTipPosition=t.stabilizedTipPosition,this.tipPosition=t.tipPosition,this.tipVelocity=t.tipVelocity,this.touchZone=t.touchZone,this.touchDistance=t.touchDistance,this.timeVisible=t.timeVisible};r.prototype.toString=function(){return"Pointable [ id:"+this.id+" "+this.length+"mmx | width:"+this.width+"mm | direction:"+this.direction+" ]"},r.prototype.hand=function(){return this.frame.hand(this.handId)},r.Invalid={valid:!1}},{"gl-matrix":21}],15:[function(t,n,e){var r=t("./frame"),i=t("events").EventEmitter,o=function(t){this.type=t.type,this.state=t.state};e.chooseProtocol=function(t){var n;switch(t.version){case 1:case 2:case 3:case 4:case 5:case 6:(n=s(t)).sendBackground=function(t,e){t.send(n.encode({background:e}))},n.sendFocused=function(t,e){t.send(n.encode({focused:e}))},n.sendOptimizeHMD=function(t,e){t.send(n.encode({optimizeHMD:e}))},n.sendOptimizeScreentop=function(t,e){t.send(n.encode({optimizeScreentop:e}))};break;default:throw"unrecognized version"}return n};var s=e.JSONProtocol=function(t){var n=function(t){if(t.event)return new o(t.event);n.emit("beforeFrameCreated",t);var e=new r(t);return n.emit("afterFrameCreated",e,t),e};return n.encode=function(t){return JSON.stringify(t)},n.version=t.version,n.serviceVersion=t.serviceVersion,n.versionLong="Version "+t.version,n.type="protocol",Object.assign(n,i.prototype),n}},{"./frame":9,events:31}],16:[function(t,n,e){e.UI={Region:t("./ui/region"),Cursor:t("./ui/cursor")}},{"./ui/cursor":17,"./ui/region":18}],17:[function(t,n,e){n.exports=function(){return function(t){var n=t.pointables.sort(function(t,n){return t.z-n.z})[0];return n&&n.valid&&(t.cursorPosition=n.tipPosition),t}}},{}],18:[function(t,n,e){var r=t("events").EventEmitter,i=n.exports=function(t,n){this.start=new Vector(t),this.end=new Vector(n),this.enteredFrame=null};i.prototype.hasPointables=function(t){for(var n=0;n!=t.pointables.length;n++){var e=t.pointables[n].tipPosition;if(e.x>=this.start.x&&e.x<=this.end.x&&e.y>=this.start.y&&e.y<=this.end.y&&e.z>=this.start.z&&e.z<=this.end.z)return!0}return!1},i.prototype.listener=function(t){var n=this;return t&&t.nearThreshold&&this.setupNearRegion(t.nearThreshold),function(t){return n.updatePosition(t)}},i.prototype.clipper=function(){var t=this;return function(n){return t.updatePosition(n),t.enteredFrame?n:null}},i.prototype.setupNearRegion=function(t){var n=this.nearRegion=new i([this.start.x-t,this.start.y-t,this.start.z-t],[this.end.x+t,this.end.y+t,this.end.z+t]),e=this;n.on("enter",function(t){e.emit("near",t)}),n.on("exit",function(t){e.emit("far",t)}),e.on("exit",function(t){e.emit("near",t)})},i.prototype.updatePosition=function(t){return this.nearRegion&&this.nearRegion.updatePosition(t),this.hasPointables(t)&&null==this.enteredFrame?(this.enteredFrame=t,this.emit("enter",this.enteredFrame)):this.hasPointables(t)||null==this.enteredFrame||(this.enteredFrame=null,this.emit("exit",this.enteredFrame)),t},i.prototype.normalize=function(t){return new Vector([(t.x-this.start.x)/(this.end.x-this.start.x),(t.y-this.start.y)/(this.end.y-this.start.y),(t.z-this.start.z)/(this.end.z-this.start.z)])},i.prototype.mapToXY=function(t,n,e){var r=this.normalize(t),i=r.x,o=r.y;return i>1?i=1:i<-1&&(i=-1),o>1?o=1:o<-1&&(o=-1),[(i+1)/2*n,(1-o)/2*e,r.z]},Object.assign(i.prototype,r.prototype)},{events:31}],19:[function(t,n,e){n.exports={full:"1.1.1",major:1,minor:1,dot:1}},{}],20:[function(t,n,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.setMatrixArrayType=function t(n){e.ARRAY_TYPE=r=n},e.toRadian=s,e.equals=function t(n,e){return Math.abs(n-e)<=1e-6*Math.max(1,Math.abs(n),Math.abs(e))},e.RANDOM=e.ARRAY_TYPE=e.EPSILON=void 0,e.EPSILON=1e-6;var r="undefined"!=typeof Float32Array?Float32Array:Array;e.ARRAY_TYPE=r;var i=Math.random;e.RANDOM=i;var o=Math.PI/180;function s(t){return t*o}Math.hypot||(Math.hypot=function(){for(var t=0,n=arguments.length;n--;)t+=arguments[n]*arguments[n];return Math.sqrt(t)})},{}],21:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.vec4=e.vec3=e.vec2=e.quat2=e.quat=e.mat4=e.mat3=e.mat2d=e.mat2=e.glMatrix=void 0;var i=h(t("./common.js"));e.glMatrix=i;var o=h(t("./mat2.js"));e.mat2=o;var s=h(t("./mat2d.js"));e.mat2d=s;var a=h(t("./mat3.js"));e.mat3=a;var u=h(t("./mat4.js"));e.mat4=u;var c=h(t("./quat.js"));e.quat=c;var _=h(t("./quat2.js"));e.quat2=_;var $=h(t("./vec2.js"));e.vec2=$;var f=h(t("./vec3.js"));e.vec3=f;var p=h(t("./vec4.js"));function l(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return l=function n(){return t},t}function h(t){if(t&&t.__esModule)return t;if(null===t||"object"!==r(t)&&"function"!=typeof t)return{default:t};var n=l();if(n&&n.has(t))return n.get(t);var e={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if(Object.prototype.hasOwnProperty.call(t,o)){var s=i?Object.getOwnPropertyDescriptor(t,o):null;s&&(s.get||s.set)?Object.defineProperty(e,o,s):e[o]=t[o]}return e.default=t,n&&n.set(t,e),e}e.vec4=p},{"./common.js":20,"./mat2.js":22,"./mat2d.js":23,"./mat3.js":24,"./mat4.js":25,"./quat.js":26,"./quat2.js":27,"./vec2.js":28,"./vec3.js":29,"./vec4.js":30}],22:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=function t(){var n=new i.ARRAY_TYPE(4);return i.ARRAY_TYPE!=Float32Array&&(n[1]=0,n[2]=0),n[0]=1,n[3]=1,n},e.clone=function t(n){var e=new i.ARRAY_TYPE(4);return e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e},e.copy=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n},e.identity=function t(n){return n[0]=1,n[1]=0,n[2]=0,n[3]=1,n},e.fromValues=function t(n,e,r,o){var s=new i.ARRAY_TYPE(4);return s[0]=n,s[1]=e,s[2]=r,s[3]=o,s},e.set=function t(n,e,r,i,o){return n[0]=e,n[1]=r,n[2]=i,n[3]=o,n},e.transpose=function t(n,e){if(n===e){var r=e[1];n[1]=e[2],n[2]=r}else n[0]=e[0],n[1]=e[2],n[2]=e[1],n[3]=e[3];return n},e.invert=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=r*s-o*i;return a?(a=1/a,n[0]=s*a,n[1]=-i*a,n[2]=-o*a,n[3]=r*a,n):null},e.adjoint=function t(n,e){var r=e[0];return n[0]=e[3],n[1]=-e[1],n[2]=-e[2],n[3]=r,n},e.determinant=function t(n){return n[0]*n[3]-n[2]*n[1]},e.multiply=s,e.rotate=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=Math.sin(r),c=Math.cos(r);return n[0]=i*c+s*u,n[1]=o*c+a*u,n[2]=-(i*u)+s*c,n[3]=-(o*u)+a*c,n},e.scale=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=r[0],c=r[1];return n[0]=i*u,n[1]=o*u,n[2]=s*c,n[3]=a*c,n},e.fromRotation=function t(n,e){var r=Math.sin(e),i=Math.cos(e);return n[0]=i,n[1]=r,n[2]=-r,n[3]=i,n},e.fromScaling=function t(n,e){return n[0]=e[0],n[1]=0,n[2]=0,n[3]=e[1],n},e.str=function t(n){return"mat2("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+")"},e.frob=function t(n){return Math.hypot(n[0],n[1],n[2],n[3])},e.LDU=function t(n,e,r,i){return n[2]=i[2]/i[0],r[0]=i[0],r[1]=i[1],r[3]=i[3]-n[2]*r[1],[n,e,r]},e.add=function t(n,e,r){return n[0]=e[0]+r[0],n[1]=e[1]+r[1],n[2]=e[2]+r[2],n[3]=e[3]+r[3],n},e.subtract=a,e.exactEquals=function t(n,e){return n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]&&n[3]===e[3]},e.equals=function t(n,e){var r=n[0],o=n[1],s=n[2],a=n[3],u=e[0],c=e[1],_=e[2],$=e[3];return Math.abs(r-u)<=i.EPSILON*Math.max(1,Math.abs(r),Math.abs(u))&&Math.abs(o-c)<=i.EPSILON*Math.max(1,Math.abs(o),Math.abs(c))&&Math.abs(s-_)<=i.EPSILON*Math.max(1,Math.abs(s),Math.abs(_))&&Math.abs(a-$)<=i.EPSILON*Math.max(1,Math.abs(a),Math.abs($))},e.multiplyScalar=function t(n,e,r){return n[0]=e[0]*r,n[1]=e[1]*r,n[2]=e[2]*r,n[3]=e[3]*r,n},e.multiplyScalarAndAdd=function t(n,e,r,i){return n[0]=e[0]+r[0]*i,n[1]=e[1]+r[1]*i,n[2]=e[2]+r[2]*i,n[3]=e[3]+r[3]*i,n},e.sub=e.mul=void 0;var i=function t(n){if(n&&n.__esModule)return n;if(null===n||"object"!==r(n)&&"function"!=typeof n)return{default:n};var e=o();if(e&&e.has(n))return e.get(n);var i={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in n)if(Object.prototype.hasOwnProperty.call(n,a)){var u=s?Object.getOwnPropertyDescriptor(n,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=n[a]}return i.default=n,e&&e.set(n,i),i}(t("./common.js"));function o(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return o=function n(){return t},t}function s(t,n,e){var r=n[0],i=n[1],o=n[2],s=n[3],a=e[0],u=e[1],c=e[2],_=e[3];return t[0]=r*a+o*u,t[1]=i*a+s*u,t[2]=r*c+o*_,t[3]=i*c+s*_,t}function a(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t}e.mul=s,e.sub=a},{"./common.js":20}],23:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=function t(){var n=new i.ARRAY_TYPE(6);return i.ARRAY_TYPE!=Float32Array&&(n[1]=0,n[2]=0,n[4]=0,n[5]=0),n[0]=1,n[3]=1,n},e.clone=function t(n){var e=new i.ARRAY_TYPE(6);return e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e},e.copy=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n},e.identity=function t(n){return n[0]=1,n[1]=0,n[2]=0,n[3]=1,n[4]=0,n[5]=0,n},e.fromValues=function t(n,e,r,o,s,a){var u=new i.ARRAY_TYPE(6);return u[0]=n,u[1]=e,u[2]=r,u[3]=o,u[4]=s,u[5]=a,u},e.set=function t(n,e,r,i,o,s,a){return n[0]=e,n[1]=r,n[2]=i,n[3]=o,n[4]=s,n[5]=a,n},e.invert=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=e[4],u=e[5],c=r*s-i*o;return c?(c=1/c,n[0]=s*c,n[1]=-i*c,n[2]=-o*c,n[3]=r*c,n[4]=(o*u-s*a)*c,n[5]=(i*a-r*u)*c,n):null},e.determinant=function t(n){return n[0]*n[3]-n[1]*n[2]},e.multiply=s,e.rotate=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=e[4],c=e[5],_=Math.sin(r),$=Math.cos(r);return n[0]=i*$+s*_,n[1]=o*$+a*_,n[2]=-(i*_)+s*$,n[3]=-(o*_)+a*$,n[4]=u,n[5]=c,n},e.scale=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=e[4],c=e[5],_=r[0],$=r[1];return n[0]=i*_,n[1]=o*_,n[2]=s*$,n[3]=a*$,n[4]=u,n[5]=c,n},e.translate=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=e[4],c=e[5],_=r[0],$=r[1];return n[0]=i,n[1]=o,n[2]=s,n[3]=a,n[4]=i*_+s*$+u,n[5]=o*_+a*$+c,n},e.fromRotation=function t(n,e){var r=Math.sin(e),i=Math.cos(e);return n[0]=i,n[1]=r,n[2]=-r,n[3]=i,n[4]=0,n[5]=0,n},e.fromScaling=function t(n,e){return n[0]=e[0],n[1]=0,n[2]=0,n[3]=e[1],n[4]=0,n[5]=0,n},e.fromTranslation=function t(n,e){return n[0]=1,n[1]=0,n[2]=0,n[3]=1,n[4]=e[0],n[5]=e[1],n},e.str=function t(n){return"mat2d("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+", "+n[4]+", "+n[5]+")"},e.frob=function t(n){return Math.hypot(n[0],n[1],n[2],n[3],n[4],n[5],1)},e.add=function t(n,e,r){return n[0]=e[0]+r[0],n[1]=e[1]+r[1],n[2]=e[2]+r[2],n[3]=e[3]+r[3],n[4]=e[4]+r[4],n[5]=e[5]+r[5],n},e.subtract=a,e.multiplyScalar=function t(n,e,r){return n[0]=e[0]*r,n[1]=e[1]*r,n[2]=e[2]*r,n[3]=e[3]*r,n[4]=e[4]*r,n[5]=e[5]*r,n},e.multiplyScalarAndAdd=function t(n,e,r,i){return n[0]=e[0]+r[0]*i,n[1]=e[1]+r[1]*i,n[2]=e[2]+r[2]*i,n[3]=e[3]+r[3]*i,n[4]=e[4]+r[4]*i,n[5]=e[5]+r[5]*i,n},e.exactEquals=function t(n,e){return n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]&&n[3]===e[3]&&n[4]===e[4]&&n[5]===e[5]},e.equals=function t(n,e){var r=n[0],o=n[1],s=n[2],a=n[3],u=n[4],c=n[5],_=e[0],$=e[1],f=e[2],p=e[3],l=e[4],h=e[5];return Math.abs(r-_)<=i.EPSILON*Math.max(1,Math.abs(r),Math.abs(_))&&Math.abs(o-$)<=i.EPSILON*Math.max(1,Math.abs(o),Math.abs($))&&Math.abs(s-f)<=i.EPSILON*Math.max(1,Math.abs(s),Math.abs(f))&&Math.abs(a-p)<=i.EPSILON*Math.max(1,Math.abs(a),Math.abs(p))&&Math.abs(u-l)<=i.EPSILON*Math.max(1,Math.abs(u),Math.abs(l))&&Math.abs(c-h)<=i.EPSILON*Math.max(1,Math.abs(c),Math.abs(h))},e.sub=e.mul=void 0;var i=function t(n){if(n&&n.__esModule)return n;if(null===n||"object"!==r(n)&&"function"!=typeof n)return{default:n};var e=o();if(e&&e.has(n))return e.get(n);var i={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in n)if(Object.prototype.hasOwnProperty.call(n,a)){var u=s?Object.getOwnPropertyDescriptor(n,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=n[a]}return i.default=n,e&&e.set(n,i),i}(t("./common.js"));function o(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return o=function n(){return t},t}function s(t,n,e){var r=n[0],i=n[1],o=n[2],s=n[3],a=n[4],u=n[5],c=e[0],_=e[1],$=e[2],f=e[3],p=e[4],l=e[5];return t[0]=r*c+o*_,t[1]=i*c+s*_,t[2]=r*$+o*f,t[3]=i*$+s*f,t[4]=r*p+o*l+a,t[5]=i*p+s*l+u,t}function a(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t}e.mul=s,e.sub=a},{"./common.js":20}],24:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=function t(){var n=new i.ARRAY_TYPE(9);return i.ARRAY_TYPE!=Float32Array&&(n[1]=0,n[2]=0,n[3]=0,n[5]=0,n[6]=0,n[7]=0),n[0]=1,n[4]=1,n[8]=1,n},e.fromMat4=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[4],n[4]=e[5],n[5]=e[6],n[6]=e[8],n[7]=e[9],n[8]=e[10],n},e.clone=function t(n){var e=new i.ARRAY_TYPE(9);return e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e[6]=n[6],e[7]=n[7],e[8]=n[8],e},e.copy=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n},e.fromValues=function t(n,e,r,o,s,a,u,c,_){var $=new i.ARRAY_TYPE(9);return $[0]=n,$[1]=e,$[2]=r,$[3]=o,$[4]=s,$[5]=a,$[6]=u,$[7]=c,$[8]=_,$},e.set=function t(n,e,r,i,o,s,a,u,c,_){return n[0]=e,n[1]=r,n[2]=i,n[3]=o,n[4]=s,n[5]=a,n[6]=u,n[7]=c,n[8]=_,n},e.identity=function t(n){return n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=1,n[5]=0,n[6]=0,n[7]=0,n[8]=1,n},e.transpose=function t(n,e){if(n===e){var r=e[1],i=e[2],o=e[5];n[1]=e[3],n[2]=e[6],n[3]=r,n[5]=e[7],n[6]=i,n[7]=o}else n[0]=e[0],n[1]=e[3],n[2]=e[6],n[3]=e[1],n[4]=e[4],n[5]=e[7],n[6]=e[2],n[7]=e[5],n[8]=e[8];return n},e.invert=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=e[4],u=e[5],c=e[6],_=e[7],$=e[8],f=$*a-u*_,p=-$*s+u*c,l=_*s-a*c,h=r*f+i*p+o*l;return h?(h=1/h,n[0]=f*h,n[1]=(-$*i+o*_)*h,n[2]=(u*i-o*a)*h,n[3]=p*h,n[4]=($*r-o*c)*h,n[5]=(-u*r+o*s)*h,n[6]=l*h,n[7]=(-_*r+i*c)*h,n[8]=(a*r-i*s)*h,n):null},e.adjoint=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=e[4],u=e[5],c=e[6],_=e[7],$=e[8];return n[0]=a*$-u*_,n[1]=o*_-i*$,n[2]=i*u-o*a,n[3]=u*c-s*$,n[4]=r*$-o*c,n[5]=o*s-r*u,n[6]=s*_-a*c,n[7]=i*c-r*_,n[8]=r*a-i*s,n},e.determinant=function t(n){var e=n[0],r=n[1],i=n[2],o=n[3],s=n[4],a=n[5],u=n[6],c=n[7],_=n[8];return e*(_*s-a*c)+r*(-_*o+a*u)+i*(c*o-s*u)},e.multiply=s,e.translate=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=e[4],c=e[5],_=e[6],$=e[7],f=e[8],p=r[0],l=r[1];return n[0]=i,n[1]=o,n[2]=s,n[3]=a,n[4]=u,n[5]=c,n[6]=p*i+l*a+_,n[7]=p*o+l*u+$,n[8]=p*s+l*c+f,n},e.rotate=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=e[4],c=e[5],_=e[6],$=e[7],f=e[8],p=Math.sin(r),l=Math.cos(r);return n[0]=l*i+p*a,n[1]=l*o+p*u,n[2]=l*s+p*c,n[3]=l*a-p*i,n[4]=l*u-p*o,n[5]=l*c-p*s,n[6]=_,n[7]=$,n[8]=f,n},e.scale=function t(n,e,r){var i=r[0],o=r[1];return n[0]=i*e[0],n[1]=i*e[1],n[2]=i*e[2],n[3]=o*e[3],n[4]=o*e[4],n[5]=o*e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n},e.fromTranslation=function t(n,e){return n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=1,n[5]=0,n[6]=e[0],n[7]=e[1],n[8]=1,n},e.fromRotation=function t(n,e){var r=Math.sin(e),i=Math.cos(e);return n[0]=i,n[1]=r,n[2]=0,n[3]=-r,n[4]=i,n[5]=0,n[6]=0,n[7]=0,n[8]=1,n},e.fromScaling=function t(n,e){return n[0]=e[0],n[1]=0,n[2]=0,n[3]=0,n[4]=e[1],n[5]=0,n[6]=0,n[7]=0,n[8]=1,n},e.fromMat2d=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=0,n[3]=e[2],n[4]=e[3],n[5]=0,n[6]=e[4],n[7]=e[5],n[8]=1,n},e.fromQuat=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=r+r,u=i+i,c=o+o,_=r*a,$=i*a,f=i*u,p=o*a,l=o*u,h=o*c,d=s*a,m=s*u,v=s*c;return n[0]=1-f-h,n[3]=$-v,n[6]=p+m,n[1]=$+v,n[4]=1-_-h,n[7]=l-d,n[2]=p-m,n[5]=l+d,n[8]=1-_-f,n},e.normalFromMat4=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=e[4],u=e[5],c=e[6],_=e[7],$=e[8],f=e[9],p=e[10],l=e[11],h=e[12],d=e[13],m=e[14],v=e[15],y=r*u-i*a,b=r*c-o*a,g=r*_-s*a,P=i*c-o*u,x=i*_-s*u,w=o*_-s*c,E=$*d-f*h,S=$*m-p*h,A=$*v-l*h,L=f*m-p*d,O=f*v-l*d,R=p*v-l*m,I=y*R-b*O+g*L+P*A-x*S+w*E;return I?(I=1/I,n[0]=(u*R-c*O+_*L)*I,n[1]=(c*A-a*R-_*S)*I,n[2]=(a*O-u*A+_*E)*I,n[3]=(o*O-i*R-s*L)*I,n[4]=(r*R-o*A+s*S)*I,n[5]=(i*A-r*O-s*E)*I,n[6]=(d*w-m*x+v*P)*I,n[7]=(m*g-h*w-v*b)*I,n[8]=(h*x-d*g+v*y)*I,n):null},e.projection=function t(n,e,r){return n[0]=2/e,n[1]=0,n[2]=0,n[3]=0,n[4]=-2/r,n[5]=0,n[6]=-1,n[7]=1,n[8]=1,n},e.str=function t(n){return"mat3("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+", "+n[4]+", "+n[5]+", "+n[6]+", "+n[7]+", "+n[8]+")"},e.frob=function t(n){return Math.hypot(n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8])},e.add=function t(n,e,r){return n[0]=e[0]+r[0],n[1]=e[1]+r[1],n[2]=e[2]+r[2],n[3]=e[3]+r[3],n[4]=e[4]+r[4],n[5]=e[5]+r[5],n[6]=e[6]+r[6],n[7]=e[7]+r[7],n[8]=e[8]+r[8],n},e.subtract=a,e.multiplyScalar=function t(n,e,r){return n[0]=e[0]*r,n[1]=e[1]*r,n[2]=e[2]*r,n[3]=e[3]*r,n[4]=e[4]*r,n[5]=e[5]*r,n[6]=e[6]*r,n[7]=e[7]*r,n[8]=e[8]*r,n},e.multiplyScalarAndAdd=function t(n,e,r,i){return n[0]=e[0]+r[0]*i,n[1]=e[1]+r[1]*i,n[2]=e[2]+r[2]*i,n[3]=e[3]+r[3]*i,n[4]=e[4]+r[4]*i,n[5]=e[5]+r[5]*i,n[6]=e[6]+r[6]*i,n[7]=e[7]+r[7]*i,n[8]=e[8]+r[8]*i,n},e.exactEquals=function t(n,e){return n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]&&n[3]===e[3]&&n[4]===e[4]&&n[5]===e[5]&&n[6]===e[6]&&n[7]===e[7]&&n[8]===e[8]},e.equals=function t(n,e){var r=n[0],o=n[1],s=n[2],a=n[3],u=n[4],c=n[5],_=n[6],$=n[7],f=n[8],p=e[0],l=e[1],h=e[2],d=e[3],m=e[4],v=e[5],y=e[6],b=e[7],g=e[8];return Math.abs(r-p)<=i.EPSILON*Math.max(1,Math.abs(r),Math.abs(p))&&Math.abs(o-l)<=i.EPSILON*Math.max(1,Math.abs(o),Math.abs(l))&&Math.abs(s-h)<=i.EPSILON*Math.max(1,Math.abs(s),Math.abs(h))&&Math.abs(a-d)<=i.EPSILON*Math.max(1,Math.abs(a),Math.abs(d))&&Math.abs(u-m)<=i.EPSILON*Math.max(1,Math.abs(u),Math.abs(m))&&Math.abs(c-v)<=i.EPSILON*Math.max(1,Math.abs(c),Math.abs(v))&&Math.abs(_-y)<=i.EPSILON*Math.max(1,Math.abs(_),Math.abs(y))&&Math.abs($-b)<=i.EPSILON*Math.max(1,Math.abs($),Math.abs(b))&&Math.abs(f-g)<=i.EPSILON*Math.max(1,Math.abs(f),Math.abs(g))},e.sub=e.mul=void 0;var i=function t(n){if(n&&n.__esModule)return n;if(null===n||"object"!==r(n)&&"function"!=typeof n)return{default:n};var e=o();if(e&&e.has(n))return e.get(n);var i={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in n)if(Object.prototype.hasOwnProperty.call(n,a)){var u=s?Object.getOwnPropertyDescriptor(n,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=n[a]}return i.default=n,e&&e.set(n,i),i}(t("./common.js"));function o(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return o=function n(){return t},t}function s(t,n,e){var r=n[0],i=n[1],o=n[2],s=n[3],a=n[4],u=n[5],c=n[6],_=n[7],$=n[8],f=e[0],p=e[1],l=e[2],h=e[3],d=e[4],m=e[5],v=e[6],y=e[7],b=e[8];return t[0]=f*r+p*s+l*c,t[1]=f*i+p*a+l*_,t[2]=f*o+p*u+l*$,t[3]=h*r+d*s+m*c,t[4]=h*i+d*a+m*_,t[5]=h*o+d*u+m*$,t[6]=v*r+y*s+b*c,t[7]=v*i+y*a+b*_,t[8]=v*o+y*u+b*$,t}function a(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t[6]=n[6]-e[6],t[7]=n[7]-e[7],t[8]=n[8]-e[8],t}e.mul=s,e.sub=a},{"./common.js":20}],25:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=function t(){var n=new i.ARRAY_TYPE(16);return i.ARRAY_TYPE!=Float32Array&&(n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[11]=0,n[12]=0,n[13]=0,n[14]=0),n[0]=1,n[5]=1,n[10]=1,n[15]=1,n},e.clone=function t(n){var e=new i.ARRAY_TYPE(16);return e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e[6]=n[6],e[7]=n[7],e[8]=n[8],e[9]=n[9],e[10]=n[10],e[11]=n[11],e[12]=n[12],e[13]=n[13],e[14]=n[14],e[15]=n[15],e},e.copy=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n},e.fromValues=function t(n,e,r,o,s,a,u,c,_,$,f,p,l,h,d,m){var v=new i.ARRAY_TYPE(16);return v[0]=n,v[1]=e,v[2]=r,v[3]=o,v[4]=s,v[5]=a,v[6]=u,v[7]=c,v[8]=_,v[9]=$,v[10]=f,v[11]=p,v[12]=l,v[13]=h,v[14]=d,v[15]=m,v},e.set=function t(n,e,r,i,o,s,a,u,c,_,$,f,p,l,h,d,m){return n[0]=e,n[1]=r,n[2]=i,n[3]=o,n[4]=s,n[5]=a,n[6]=u,n[7]=c,n[8]=_,n[9]=$,n[10]=f,n[11]=p,n[12]=l,n[13]=h,n[14]=d,n[15]=m,n},e.identity=s,e.transpose=function t(n,e){if(n===e){var r=e[1],i=e[2],o=e[3],s=e[6],a=e[7],u=e[11];n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=r,n[6]=e[9],n[7]=e[13],n[8]=i,n[9]=s,n[11]=e[14],n[12]=o,n[13]=a,n[14]=u}else n[0]=e[0],n[1]=e[4],n[2]=e[8],n[3]=e[12],n[4]=e[1],n[5]=e[5],n[6]=e[9],n[7]=e[13],n[8]=e[2],n[9]=e[6],n[10]=e[10],n[11]=e[14],n[12]=e[3],n[13]=e[7],n[14]=e[11],n[15]=e[15];return n},e.invert=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=e[4],u=e[5],c=e[6],_=e[7],$=e[8],f=e[9],p=e[10],l=e[11],h=e[12],d=e[13],m=e[14],v=e[15],y=r*u-i*a,b=r*c-o*a,g=r*_-s*a,P=i*c-o*u,x=i*_-s*u,w=o*_-s*c,E=$*d-f*h,S=$*m-p*h,A=$*v-l*h,L=f*m-p*d,O=f*v-l*d,R=p*v-l*m,I=y*R-b*O+g*L+P*A-x*S+w*E;return I?(I=1/I,n[0]=(u*R-c*O+_*L)*I,n[1]=(o*O-i*R-s*L)*I,n[2]=(d*w-m*x+v*P)*I,n[3]=(p*x-f*w-l*P)*I,n[4]=(c*A-a*R-_*S)*I,n[5]=(r*R-o*A+s*S)*I,n[6]=(m*g-h*w-v*b)*I,n[7]=($*w-p*g+l*b)*I,n[8]=(a*O-u*A+_*E)*I,n[9]=(i*A-r*O-s*E)*I,n[10]=(h*x-d*g+v*y)*I,n[11]=(f*g-$*x-l*y)*I,n[12]=(u*S-a*L-c*E)*I,n[13]=(r*L-i*S+o*E)*I,n[14]=(d*b-h*P-m*y)*I,n[15]=($*P-f*b+p*y)*I,n):null},e.adjoint=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=e[4],u=e[5],c=e[6],_=e[7],$=e[8],f=e[9],p=e[10],l=e[11],h=e[12],d=e[13],m=e[14],v=e[15];return n[0]=u*(p*v-l*m)-f*(c*v-_*m)+d*(c*l-_*p),n[1]=-(i*(p*v-l*m)-f*(o*v-s*m)+d*(o*l-s*p)),n[2]=i*(c*v-_*m)-u*(o*v-s*m)+d*(o*_-s*c),n[3]=-(i*(c*l-_*p)-u*(o*l-s*p)+f*(o*_-s*c)),n[4]=-(a*(p*v-l*m)-$*(c*v-_*m)+h*(c*l-_*p)),n[5]=r*(p*v-l*m)-$*(o*v-s*m)+h*(o*l-s*p),n[6]=-(r*(c*v-_*m)-a*(o*v-s*m)+h*(o*_-s*c)),n[7]=r*(c*l-_*p)-a*(o*l-s*p)+$*(o*_-s*c),n[8]=a*(f*v-l*d)-$*(u*v-_*d)+h*(u*l-_*f),n[9]=-(r*(f*v-l*d)-$*(i*v-s*d)+h*(i*l-s*f)),n[10]=r*(u*v-_*d)-a*(i*v-s*d)+h*(i*_-s*u),n[11]=-(r*(u*l-_*f)-a*(i*l-s*f)+$*(i*_-s*u)),n[12]=-(a*(f*m-p*d)-$*(u*m-c*d)+h*(u*p-c*f)),n[13]=r*(f*m-p*d)-$*(i*m-o*d)+h*(i*p-o*f),n[14]=-(r*(u*m-c*d)-a*(i*m-o*d)+h*(i*c-o*u)),n[15]=r*(u*p-c*f)-a*(i*p-o*f)+$*(i*c-o*u),n},e.determinant=function t(n){var e=n[0],r=n[1],i=n[2],o=n[3],s=n[4],a=n[5],u=n[6],c=n[7],_=n[8],$=n[9],f=n[10],p=n[11],l=n[12],h=n[13],d=n[14],m=n[15];return(e*a-r*s)*(f*m-p*d)-(e*u-i*s)*($*m-p*h)+(e*c-o*s)*($*d-f*h)+(r*u-i*a)*(_*m-p*l)-(r*c-o*a)*(_*d-f*l)+(i*c-o*u)*(_*h-$*l)},e.multiply=a,e.translate=function t(n,e,r){var i,o,s,a,u,c,_,$,f,p,l,h,d=r[0],m=r[1],v=r[2];return e===n?(n[12]=e[0]*d+e[4]*m+e[8]*v+e[12],n[13]=e[1]*d+e[5]*m+e[9]*v+e[13],n[14]=e[2]*d+e[6]*m+e[10]*v+e[14],n[15]=e[3]*d+e[7]*m+e[11]*v+e[15]):(i=e[0],o=e[1],s=e[2],a=e[3],u=e[4],c=e[5],_=e[6],$=e[7],f=e[8],p=e[9],l=e[10],h=e[11],n[0]=i,n[1]=o,n[2]=s,n[3]=a,n[4]=u,n[5]=c,n[6]=_,n[7]=$,n[8]=f,n[9]=p,n[10]=l,n[11]=h,n[12]=i*d+u*m+f*v+e[12],n[13]=o*d+c*m+p*v+e[13],n[14]=s*d+_*m+l*v+e[14],n[15]=a*d+$*m+h*v+e[15]),n},e.scale=function t(n,e,r){var i=r[0],o=r[1],s=r[2];return n[0]=e[0]*i,n[1]=e[1]*i,n[2]=e[2]*i,n[3]=e[3]*i,n[4]=e[4]*o,n[5]=e[5]*o,n[6]=e[6]*o,n[7]=e[7]*o,n[8]=e[8]*s,n[9]=e[9]*s,n[10]=e[10]*s,n[11]=e[11]*s,n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15],n},e.rotate=function t(n,e,r,o){var s,a,u,c,_,$,f,p,l,h,d,m,v,y,b,g,P,x,w,E,S,A,L,O,R=o[0],I=o[1],q=o[2],N=Math.hypot(R,I,q);return N<i.EPSILON?null:(R*=N=1/N,I*=N,q*=N,s=Math.sin(r),u=1-(a=Math.cos(r)),c=e[0],_=e[1],$=e[2],f=e[3],p=e[4],l=e[5],h=e[6],d=e[7],m=e[8],v=e[9],y=e[10],b=e[11],g=R*R*u+a,P=I*R*u+q*s,x=q*R*u-I*s,w=R*I*u-q*s,E=I*I*u+a,S=q*I*u+R*s,A=R*q*u+I*s,L=I*q*u-R*s,O=q*q*u+a,n[0]=c*g+p*P+m*x,n[1]=_*g+l*P+v*x,n[2]=$*g+h*P+y*x,n[3]=f*g+d*P+b*x,n[4]=c*w+p*E+m*S,n[5]=_*w+l*E+v*S,n[6]=$*w+h*E+y*S,n[7]=f*w+d*E+b*S,n[8]=c*A+p*L+m*O,n[9]=_*A+l*L+v*O,n[10]=$*A+h*L+y*O,n[11]=f*A+d*L+b*O,e!==n&&(n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15]),n)},e.rotateX=function t(n,e,r){var i=Math.sin(r),o=Math.cos(r),s=e[4],a=e[5],u=e[6],c=e[7],_=e[8],$=e[9],f=e[10],p=e[11];return e!==n&&(n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15]),n[4]=s*o+_*i,n[5]=a*o+$*i,n[6]=u*o+f*i,n[7]=c*o+p*i,n[8]=_*o-s*i,n[9]=$*o-a*i,n[10]=f*o-u*i,n[11]=p*o-c*i,n},e.rotateY=function t(n,e,r){var i=Math.sin(r),o=Math.cos(r),s=e[0],a=e[1],u=e[2],c=e[3],_=e[8],$=e[9],f=e[10],p=e[11];return e!==n&&(n[4]=e[4],n[5]=e[5],n[6]=e[6],n[7]=e[7],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15]),n[0]=s*o-_*i,n[1]=a*o-$*i,n[2]=u*o-f*i,n[3]=c*o-p*i,n[8]=s*i+_*o,n[9]=a*i+$*o,n[10]=u*i+f*o,n[11]=c*i+p*o,n},e.rotateZ=function t(n,e,r){var i=Math.sin(r),o=Math.cos(r),s=e[0],a=e[1],u=e[2],c=e[3],_=e[4],$=e[5],f=e[6],p=e[7];return e!==n&&(n[8]=e[8],n[9]=e[9],n[10]=e[10],n[11]=e[11],n[12]=e[12],n[13]=e[13],n[14]=e[14],n[15]=e[15]),n[0]=s*o+_*i,n[1]=a*o+$*i,n[2]=u*o+f*i,n[3]=c*o+p*i,n[4]=_*o-s*i,n[5]=$*o-a*i,n[6]=f*o-u*i,n[7]=p*o-c*i,n},e.fromTranslation=function t(n,e){return n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=1,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=1,n[11]=0,n[12]=e[0],n[13]=e[1],n[14]=e[2],n[15]=1,n},e.fromScaling=function t(n,e){return n[0]=e[0],n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=e[1],n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=e[2],n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n},e.fromRotation=function t(n,e,r){var o,s,a,u=r[0],c=r[1],_=r[2],$=Math.hypot(u,c,_);return $<i.EPSILON?null:(u*=$=1/$,c*=$,_*=$,o=Math.sin(e),a=1-(s=Math.cos(e)),n[0]=u*u*a+s,n[1]=c*u*a+_*o,n[2]=_*u*a-c*o,n[3]=0,n[4]=u*c*a-_*o,n[5]=c*c*a+s,n[6]=_*c*a+u*o,n[7]=0,n[8]=u*_*a+c*o,n[9]=c*_*a-u*o,n[10]=_*_*a+s,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n)},e.fromXRotation=function t(n,e){var r=Math.sin(e),i=Math.cos(e);return n[0]=1,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=i,n[6]=r,n[7]=0,n[8]=0,n[9]=-r,n[10]=i,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n},e.fromYRotation=function t(n,e){var r=Math.sin(e),i=Math.cos(e);return n[0]=i,n[1]=0,n[2]=-r,n[3]=0,n[4]=0,n[5]=1,n[6]=0,n[7]=0,n[8]=r,n[9]=0,n[10]=i,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n},e.fromZRotation=function t(n,e){var r=Math.sin(e),i=Math.cos(e);return n[0]=i,n[1]=r,n[2]=0,n[3]=0,n[4]=-r,n[5]=i,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=1,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n},e.fromRotationTranslation=u,e.fromQuat2=function t(n,e){var r=new i.ARRAY_TYPE(3),o=-e[0],s=-e[1],a=-e[2],c=e[3],_=e[4],$=e[5],f=e[6],p=e[7],l=o*o+s*s+a*a+c*c;return l>0?(r[0]=(_*c+p*o+$*a-f*s)*2/l,r[1]=($*c+p*s+f*o-_*a)*2/l,r[2]=(f*c+p*a+_*s-$*o)*2/l):(r[0]=(_*c+p*o+$*a-f*s)*2,r[1]=($*c+p*s+f*o-_*a)*2,r[2]=(f*c+p*a+_*s-$*o)*2),u(n,e,r),n},e.getTranslation=function t(n,e){return n[0]=e[12],n[1]=e[13],n[2]=e[14],n},e.getScaling=c,e.getRotation=function t(n,e){var r=new i.ARRAY_TYPE(3);c(r,e);var o=1/r[0],s=1/r[1],a=1/r[2],u=e[0]*o,_=e[1]*s,$=e[2]*a,f=e[4]*o,p=e[5]*s,l=e[6]*a,h=e[8]*o,d=e[9]*s,m=e[10]*a,v=u+p+m,y=0;return v>0?(y=2*Math.sqrt(v+1),n[3]=.25*y,n[0]=(l-d)/y,n[1]=(h-$)/y,n[2]=(_-f)/y):u>p&&u>m?(y=2*Math.sqrt(1+u-p-m),n[3]=(l-d)/y,n[0]=.25*y,n[1]=(_+f)/y,n[2]=(h+$)/y):p>m?(y=2*Math.sqrt(1+p-u-m),n[3]=(h-$)/y,n[0]=(_+f)/y,n[1]=.25*y,n[2]=(l+d)/y):(y=2*Math.sqrt(1+m-u-p),n[3]=(_-f)/y,n[0]=(h+$)/y,n[1]=(l+d)/y,n[2]=.25*y),n},e.fromRotationTranslationScale=function t(n,e,r,i){var o=e[0],s=e[1],a=e[2],u=e[3],c=o+o,_=s+s,$=a+a,f=o*c,p=o*_,l=o*$,h=s*_,d=s*$,m=a*$,v=u*c,y=u*_,b=u*$,g=i[0],P=i[1],x=i[2];return n[0]=(1-(h+m))*g,n[1]=(p+b)*g,n[2]=(l-y)*g,n[3]=0,n[4]=(p-b)*P,n[5]=(1-(f+m))*P,n[6]=(d+v)*P,n[7]=0,n[8]=(l+y)*x,n[9]=(d-v)*x,n[10]=(1-(f+h))*x,n[11]=0,n[12]=r[0],n[13]=r[1],n[14]=r[2],n[15]=1,n},e.fromRotationTranslationScaleOrigin=function t(n,e,r,i,o){var s=e[0],a=e[1],u=e[2],c=e[3],_=s+s,$=a+a,f=u+u,p=s*_,l=s*$,h=s*f,d=a*$,m=a*f,v=u*f,y=c*_,b=c*$,g=c*f,P=i[0],x=i[1],w=i[2],E=o[0],S=o[1],A=o[2],L=(1-(d+v))*P,O=(l+g)*P,R=(h-b)*P,I=(l-g)*x,q=(1-(p+v))*x,N=(m+y)*x,z=(h+b)*w,F=(m-y)*w,M=(1-(p+d))*w;return n[0]=L,n[1]=O,n[2]=R,n[3]=0,n[4]=I,n[5]=q,n[6]=N,n[7]=0,n[8]=z,n[9]=F,n[10]=M,n[11]=0,n[12]=r[0]+E-(L*E+I*S+z*A),n[13]=r[1]+S-(O*E+q*S+F*A),n[14]=r[2]+A-(R*E+N*S+M*A),n[15]=1,n},e.fromQuat=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=r+r,u=i+i,c=o+o,_=r*a,$=i*a,f=i*u,p=o*a,l=o*u,h=o*c,d=s*a,m=s*u,v=s*c;return n[0]=1-f-h,n[1]=$+v,n[2]=p-m,n[3]=0,n[4]=$-v,n[5]=1-_-h,n[6]=l+d,n[7]=0,n[8]=p+m,n[9]=l-d,n[10]=1-_-f,n[11]=0,n[12]=0,n[13]=0,n[14]=0,n[15]=1,n},e.frustum=function t(n,e,r,i,o,s,a){var u=1/(r-e),c=1/(o-i),_=1/(s-a);return n[0]=2*s*u,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=2*s*c,n[6]=0,n[7]=0,n[8]=(r+e)*u,n[9]=(o+i)*c,n[10]=(a+s)*_,n[11]=-1,n[12]=0,n[13]=0,n[14]=a*s*2*_,n[15]=0,n},e.perspective=function t(n,e,r,i,o){var s,a=1/Math.tan(e/2);return n[0]=a/r,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=a,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[11]=-1,n[12]=0,n[13]=0,n[15]=0,null!=o&&o!==1/0?(s=1/(i-o),n[10]=(o+i)*s,n[14]=2*o*i*s):(n[10]=-1,n[14]=-2*i),n},e.perspectiveFromFieldOfView=function t(n,e,r,i){var o=Math.tan(e.upDegrees*Math.PI/180),s=Math.tan(e.downDegrees*Math.PI/180),a=Math.tan(e.leftDegrees*Math.PI/180),u=Math.tan(e.rightDegrees*Math.PI/180),c=2/(a+u),_=2/(o+s);return n[0]=c,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=_,n[6]=0,n[7]=0,n[8]=-((a-u)*c*.5),n[9]=(o-s)*_*.5,n[10]=i/(r-i),n[11]=-1,n[12]=0,n[13]=0,n[14]=i*r/(r-i),n[15]=0,n},e.ortho=function t(n,e,r,i,o,s,a){var u=1/(e-r),c=1/(i-o),_=1/(s-a);return n[0]=-2*u,n[1]=0,n[2]=0,n[3]=0,n[4]=0,n[5]=-2*c,n[6]=0,n[7]=0,n[8]=0,n[9]=0,n[10]=2*_,n[11]=0,n[12]=(e+r)*u,n[13]=(o+i)*c,n[14]=(a+s)*_,n[15]=1,n},e.lookAt=function t(n,e,r,o){var a,u,c,_,$,f,p,l,h,d,m=e[0],v=e[1],y=e[2],b=o[0],g=o[1],P=o[2],x=r[0],w=r[1],E=r[2];return Math.abs(m-x)<i.EPSILON&&Math.abs(v-w)<i.EPSILON&&Math.abs(y-E)<i.EPSILON?s(n):(p=m-x,d=1/Math.hypot(p,l=v-w,h=y-E),p*=d,l*=d,h*=d,a=g*h-P*l,(d=Math.hypot(a,u=P*p-b*h,c=b*l-g*p))?(a*=d=1/d,u*=d,c*=d):(a=0,u=0,c=0),_=l*c-h*u,(d=Math.hypot(_,$=h*a-p*c,f=p*u-l*a))?(_*=d=1/d,$*=d,f*=d):(_=0,$=0,f=0),n[0]=a,n[1]=_,n[2]=p,n[3]=0,n[4]=u,n[5]=$,n[6]=l,n[7]=0,n[8]=c,n[9]=f,n[10]=h,n[11]=0,n[12]=-(a*m+u*v+c*y),n[13]=-(_*m+$*v+f*y),n[14]=-(p*m+l*v+h*y),n[15]=1,n)},e.targetTo=function t(n,e,r,i){var o=e[0],s=e[1],a=e[2],u=i[0],c=i[1],_=i[2],$=o-r[0],f=s-r[1],p=a-r[2],l=$*$+f*f+p*p;l>0&&($*=l=1/Math.sqrt(l),f*=l,p*=l);var h=c*p-_*f,d=_*$-u*p,m=u*f-c*$;return(l=h*h+d*d+m*m)>0&&(h*=l=1/Math.sqrt(l),d*=l,m*=l),n[0]=h,n[1]=d,n[2]=m,n[3]=0,n[4]=f*m-p*d,n[5]=p*h-$*m,n[6]=$*d-f*h,n[7]=0,n[8]=$,n[9]=f,n[10]=p,n[11]=0,n[12]=o,n[13]=s,n[14]=a,n[15]=1,n},e.str=function t(n){return"mat4("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+", "+n[4]+", "+n[5]+", "+n[6]+", "+n[7]+", "+n[8]+", "+n[9]+", "+n[10]+", "+n[11]+", "+n[12]+", "+n[13]+", "+n[14]+", "+n[15]+")"},e.frob=function t(n){return Math.hypot(n[0],n[1],n[2],n[3],n[4],n[5],n[6],n[7],n[8],n[9],n[10],n[11],n[12],n[13],n[14],n[15])},e.add=function t(n,e,r){return n[0]=e[0]+r[0],n[1]=e[1]+r[1],n[2]=e[2]+r[2],n[3]=e[3]+r[3],n[4]=e[4]+r[4],n[5]=e[5]+r[5],n[6]=e[6]+r[6],n[7]=e[7]+r[7],n[8]=e[8]+r[8],n[9]=e[9]+r[9],n[10]=e[10]+r[10],n[11]=e[11]+r[11],n[12]=e[12]+r[12],n[13]=e[13]+r[13],n[14]=e[14]+r[14],n[15]=e[15]+r[15],n},e.subtract=_,e.multiplyScalar=function t(n,e,r){return n[0]=e[0]*r,n[1]=e[1]*r,n[2]=e[2]*r,n[3]=e[3]*r,n[4]=e[4]*r,n[5]=e[5]*r,n[6]=e[6]*r,n[7]=e[7]*r,n[8]=e[8]*r,n[9]=e[9]*r,n[10]=e[10]*r,n[11]=e[11]*r,n[12]=e[12]*r,n[13]=e[13]*r,n[14]=e[14]*r,n[15]=e[15]*r,n},e.multiplyScalarAndAdd=function t(n,e,r,i){return n[0]=e[0]+r[0]*i,n[1]=e[1]+r[1]*i,n[2]=e[2]+r[2]*i,n[3]=e[3]+r[3]*i,n[4]=e[4]+r[4]*i,n[5]=e[5]+r[5]*i,n[6]=e[6]+r[6]*i,n[7]=e[7]+r[7]*i,n[8]=e[8]+r[8]*i,n[9]=e[9]+r[9]*i,n[10]=e[10]+r[10]*i,n[11]=e[11]+r[11]*i,n[12]=e[12]+r[12]*i,n[13]=e[13]+r[13]*i,n[14]=e[14]+r[14]*i,n[15]=e[15]+r[15]*i,n},e.exactEquals=function t(n,e){return n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]&&n[3]===e[3]&&n[4]===e[4]&&n[5]===e[5]&&n[6]===e[6]&&n[7]===e[7]&&n[8]===e[8]&&n[9]===e[9]&&n[10]===e[10]&&n[11]===e[11]&&n[12]===e[12]&&n[13]===e[13]&&n[14]===e[14]&&n[15]===e[15]},e.equals=function t(n,e){var r=n[0],o=n[1],s=n[2],a=n[3],u=n[4],c=n[5],_=n[6],$=n[7],f=n[8],p=n[9],l=n[10],h=n[11],d=n[12],m=n[13],v=n[14],y=n[15],b=e[0],g=e[1],P=e[2],x=e[3],w=e[4],E=e[5],S=e[6],A=e[7],L=e[8],O=e[9],R=e[10],I=e[11],q=e[12],N=e[13],z=e[14],F=e[15];return Math.abs(r-b)<=i.EPSILON*Math.max(1,Math.abs(r),Math.abs(b))&&Math.abs(o-g)<=i.EPSILON*Math.max(1,Math.abs(o),Math.abs(g))&&Math.abs(s-P)<=i.EPSILON*Math.max(1,Math.abs(s),Math.abs(P))&&Math.abs(a-x)<=i.EPSILON*Math.max(1,Math.abs(a),Math.abs(x))&&Math.abs(u-w)<=i.EPSILON*Math.max(1,Math.abs(u),Math.abs(w))&&Math.abs(c-E)<=i.EPSILON*Math.max(1,Math.abs(c),Math.abs(E))&&Math.abs(_-S)<=i.EPSILON*Math.max(1,Math.abs(_),Math.abs(S))&&Math.abs($-A)<=i.EPSILON*Math.max(1,Math.abs($),Math.abs(A))&&Math.abs(f-L)<=i.EPSILON*Math.max(1,Math.abs(f),Math.abs(L))&&Math.abs(p-O)<=i.EPSILON*Math.max(1,Math.abs(p),Math.abs(O))&&Math.abs(l-R)<=i.EPSILON*Math.max(1,Math.abs(l),Math.abs(R))&&Math.abs(h-I)<=i.EPSILON*Math.max(1,Math.abs(h),Math.abs(I))&&Math.abs(d-q)<=i.EPSILON*Math.max(1,Math.abs(d),Math.abs(q))&&Math.abs(m-N)<=i.EPSILON*Math.max(1,Math.abs(m),Math.abs(N))&&Math.abs(v-z)<=i.EPSILON*Math.max(1,Math.abs(v),Math.abs(z))&&Math.abs(y-F)<=i.EPSILON*Math.max(1,Math.abs(y),Math.abs(F))},e.sub=e.mul=void 0;var i=function t(n){if(n&&n.__esModule)return n;if(null===n||"object"!==r(n)&&"function"!=typeof n)return{default:n};var e=o();if(e&&e.has(n))return e.get(n);var i={},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in n)if(Object.prototype.hasOwnProperty.call(n,a)){var u=s?Object.getOwnPropertyDescriptor(n,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=n[a]}return i.default=n,e&&e.set(n,i),i}(t("./common.js"));function o(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return o=function n(){return t},t}function s(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=0,t[5]=1,t[6]=0,t[7]=0,t[8]=0,t[9]=0,t[10]=1,t[11]=0,t[12]=0,t[13]=0,t[14]=0,t[15]=1,t}function a(t,n,e){var r=n[0],i=n[1],o=n[2],s=n[3],a=n[4],u=n[5],c=n[6],_=n[7],$=n[8],f=n[9],p=n[10],l=n[11],h=n[12],d=n[13],m=n[14],v=n[15],y=e[0],b=e[1],g=e[2],P=e[3];return t[0]=y*r+b*a+g*$+P*h,t[1]=y*i+b*u+g*f+P*d,t[2]=y*o+b*c+g*p+P*m,t[3]=y*s+b*_+g*l+P*v,y=e[4],b=e[5],g=e[6],P=e[7],t[4]=y*r+b*a+g*$+P*h,t[5]=y*i+b*u+g*f+P*d,t[6]=y*o+b*c+g*p+P*m,t[7]=y*s+b*_+g*l+P*v,y=e[8],b=e[9],g=e[10],P=e[11],t[8]=y*r+b*a+g*$+P*h,t[9]=y*i+b*u+g*f+P*d,t[10]=y*o+b*c+g*p+P*m,t[11]=y*s+b*_+g*l+P*v,y=e[12],b=e[13],g=e[14],P=e[15],t[12]=y*r+b*a+g*$+P*h,t[13]=y*i+b*u+g*f+P*d,t[14]=y*o+b*c+g*p+P*m,t[15]=y*s+b*_+g*l+P*v,t}function u(t,n,e){var r=n[0],i=n[1],o=n[2],s=n[3],a=r+r,u=i+i,c=o+o,_=r*a,$=r*u,f=r*c,p=i*u,l=i*c,h=o*c,d=s*a,m=s*u,v=s*c;return t[0]=1-(p+h),t[1]=$+v,t[2]=f-m,t[3]=0,t[4]=$-v,t[5]=1-(_+h),t[6]=l+d,t[7]=0,t[8]=f+m,t[9]=l-d,t[10]=1-(_+p),t[11]=0,t[12]=e[0],t[13]=e[1],t[14]=e[2],t[15]=1,t}function c(t,n){var e=n[0],r=n[1],i=n[2],o=n[4],s=n[5],a=n[6],u=n[8],c=n[9],_=n[10];return t[0]=Math.hypot(e,r,i),t[1]=Math.hypot(o,s,a),t[2]=Math.hypot(u,c,_),t}function _(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t[4]=n[4]-e[4],t[5]=n[5]-e[5],t[6]=n[6]-e[6],t[7]=n[7]-e[7],t[8]=n[8]-e[8],t[9]=n[9]-e[9],t[10]=n[10]-e[10],t[11]=n[11]-e[11],t[12]=n[12]-e[12],t[13]=n[13]-e[13],t[14]=n[14]-e[14],t[15]=n[15]-e[15],t}e.mul=a,e.sub=_},{"./common.js":20}],26:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=_,e.identity=function t(n){return n[0]=0,n[1]=0,n[2]=0,n[3]=1,n},e.setAxisAngle=$,e.getAxisAngle=function t(n,e){var r=2*Math.acos(e[3]),o=Math.sin(r/2);return o>i.EPSILON?(n[0]=e[0]/o,n[1]=e[1]/o,n[2]=e[2]/o):(n[0]=1,n[1]=0,n[2]=0),r},e.getAngle=function t(n,e){var r=x(n,e);return Math.acos(2*r*r-1)},e.multiply=f,e.rotateX=function t(n,e,r){r*=.5;var i=e[0],o=e[1],s=e[2],a=e[3],u=Math.sin(r),c=Math.cos(r);return n[0]=i*c+a*u,n[1]=o*c+s*u,n[2]=s*c-o*u,n[3]=a*c-i*u,n},e.rotateY=function t(n,e,r){r*=.5;var i=e[0],o=e[1],s=e[2],a=e[3],u=Math.sin(r),c=Math.cos(r);return n[0]=i*c-s*u,n[1]=o*c+a*u,n[2]=s*c+i*u,n[3]=a*c-o*u,n},e.rotateZ=function t(n,e,r){r*=.5;var i=e[0],o=e[1],s=e[2],a=e[3],u=Math.sin(r),c=Math.cos(r);return n[0]=i*c+o*u,n[1]=o*c-i*u,n[2]=s*c+a*u,n[3]=a*c-s*u,n},e.calculateW=function t(n,e){var r=e[0],i=e[1],o=e[2];return n[0]=r,n[1]=i,n[2]=o,n[3]=Math.sqrt(Math.abs(1-r*r-i*i-o*o)),n},e.exp=p,e.ln=l,e.pow=function t(n,e,r){return l(n,e),P(n,n,r),p(n,n),n},e.slerp=h,e.random=function t(n){var e=i.RANDOM(),r=i.RANDOM(),o=i.RANDOM(),s=Math.sqrt(1-e),a=Math.sqrt(e);return n[0]=s*Math.sin(2*Math.PI*r),n[1]=s*Math.cos(2*Math.PI*r),n[2]=a*Math.sin(2*Math.PI*o),n[3]=a*Math.cos(2*Math.PI*o),n},e.invert=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=r*r+i*i+o*o+s*s,u=a?1/a:0;return n[0]=-r*u,n[1]=-i*u,n[2]=-o*u,n[3]=s*u,n},e.conjugate=function t(n,e){return n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=e[3],n},e.fromMat3=d,e.fromEuler=function t(n,e,r,i){var o=.5*Math.PI/180;e*=o;var s=Math.sin(e),a=Math.cos(e),u=Math.sin(r*=o),c=Math.cos(r),_=Math.sin(i*=o),$=Math.cos(i);return n[0]=s*c*$-a*u*_,n[1]=a*u*$+s*c*_,n[2]=a*c*_-s*u*$,n[3]=a*c*$+s*u*_,n},e.str=function t(n){return"quat("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+")"},e.setAxes=e.sqlerp=e.rotationTo=e.equals=e.exactEquals=e.normalize=e.sqrLen=e.squaredLength=e.len=e.length=e.lerp=e.dot=e.scale=e.mul=e.add=e.set=e.copy=e.fromValues=e.clone=void 0;var i=c(t("./common.js")),o=c(t("./mat3.js")),s=c(t("./vec3.js")),a=c(t("./vec4.js"));function u(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return u=function n(){return t},t}function c(t){if(t&&t.__esModule)return t;if(null===t||"object"!==r(t)&&"function"!=typeof t)return{default:t};var n=u();if(n&&n.has(t))return n.get(t);var e={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if(Object.prototype.hasOwnProperty.call(t,o)){var s=i?Object.getOwnPropertyDescriptor(t,o):null;s&&(s.get||s.set)?Object.defineProperty(e,o,s):e[o]=t[o]}return e.default=t,n&&n.set(t,e),e}function _(){var t=new i.ARRAY_TYPE(4);return i.ARRAY_TYPE!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t[3]=1,t}function $(t,n,e){var r=Math.sin(e*=.5);return t[0]=r*n[0],t[1]=r*n[1],t[2]=r*n[2],t[3]=Math.cos(e),t}function f(t,n,e){var r=n[0],i=n[1],o=n[2],s=n[3],a=e[0],u=e[1],c=e[2],_=e[3];return t[0]=r*_+s*a+i*c-o*u,t[1]=i*_+s*u+o*a-r*c,t[2]=o*_+s*c+r*u-i*a,t[3]=s*_-r*a-i*u-o*c,t}function p(t,n){var e=n[0],r=n[1],i=n[2],o=n[3],s=Math.sqrt(e*e+r*r+i*i),a=Math.exp(o),u=s>0?a*Math.sin(s)/s:0;return t[0]=e*u,t[1]=r*u,t[2]=i*u,t[3]=a*Math.cos(s),t}function l(t,n){var e=n[0],r=n[1],i=n[2],o=n[3],s=Math.sqrt(e*e+r*r+i*i),a=s>0?Math.atan2(s,o)/s:0;return t[0]=e*a,t[1]=r*a,t[2]=i*a,t[3]=.5*Math.log(e*e+r*r+i*i+o*o),t}function h(t,n,e,r){var o,s,a,u,c,_=n[0],$=n[1],f=n[2],p=n[3],l=e[0],h=e[1],d=e[2],m=e[3];return(s=_*l+$*h+f*d+p*m)<0&&(s=-s,l=-l,h=-h,d=-d,m=-m),1-s>i.EPSILON?(u=Math.sin((1-r)*o)/(a=Math.sin(o=Math.acos(s))),c=Math.sin(r*o)/a):(u=1-r,c=r),t[0]=u*_+c*l,t[1]=u*$+c*h,t[2]=u*f+c*d,t[3]=u*p+c*m,t}function d(t,n){var e,r=n[0]+n[4]+n[8];if(r>0)e=Math.sqrt(r+1),t[3]=.5*e,e=.5/e,t[0]=(n[5]-n[7])*e,t[1]=(n[6]-n[2])*e,t[2]=(n[1]-n[3])*e;else{var i=0;n[4]>n[0]&&(i=1),n[8]>n[3*i+i]&&(i=2);var o=(i+1)%3,s=(i+2)%3;e=Math.sqrt(n[3*i+i]-n[3*o+o]-n[3*s+s]+1),t[i]=.5*e,e=.5/e,t[3]=(n[3*o+s]-n[3*s+o])*e,t[o]=(n[3*o+i]+n[3*i+o])*e,t[s]=(n[3*s+i]+n[3*i+s])*e}return t}var m=a.clone;e.clone=m;var v=a.fromValues;e.fromValues=v;var y=a.copy;e.copy=y;var b=a.set;e.set=b;var g=a.add;e.add=g,e.mul=f;var P=a.scale;e.scale=P;var x=a.dot;e.dot=x;var w=a.lerp;e.lerp=w;var E=a.length;e.length=E,e.len=E;var S=a.squaredLength;e.squaredLength=S,e.sqrLen=S;var A=a.normalize;e.normalize=A;var L=a.exactEquals;e.exactEquals=L;var O,R,I,q=a.equals;e.equals=q;var N,z,F=(O=s.create(),R=s.fromValues(1,0,0),I=s.fromValues(0,1,0),function(t,n,e){var r=s.dot(n,e);return r<-.999999?(s.cross(O,R,n),1e-6>s.len(O)&&s.cross(O,I,n),s.normalize(O,O),$(t,O,Math.PI),t):r>.999999?(t[0]=0,t[1]=0,t[2]=0,t[3]=1,t):(s.cross(O,n,e),t[0]=O[0],t[1]=O[1],t[2]=O[2],t[3]=1+r,A(t,t))});e.rotationTo=F;var M,D=(N=_(),z=_(),function(t,n,e,r,i,o){return h(N,n,i,o),h(z,e,r,o),h(t,N,z,2*o*(1-o)),t});e.sqlerp=D;var Y=(M=o.create(),function(t,n,e,r){return M[0]=e[0],M[3]=e[1],M[6]=e[2],M[1]=r[0],M[4]=r[1],M[7]=r[2],M[2]=-n[0],M[5]=-n[1],M[8]=-n[2],A(t,d(t,M))});e.setAxes=Y},{"./common.js":20,"./mat3.js":24,"./vec3.js":29,"./vec4.js":30}],27:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=function t(){var n=new i.ARRAY_TYPE(8);return i.ARRAY_TYPE!=Float32Array&&(n[0]=0,n[1]=0,n[2]=0,n[4]=0,n[5]=0,n[6]=0,n[7]=0),n[3]=1,n},e.clone=function t(n){var e=new i.ARRAY_TYPE(8);return e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e[4]=n[4],e[5]=n[5],e[6]=n[6],e[7]=n[7],e},e.fromValues=function t(n,e,r,o,s,a,u,c){var _=new i.ARRAY_TYPE(8);return _[0]=n,_[1]=e,_[2]=r,_[3]=o,_[4]=s,_[5]=a,_[6]=u,_[7]=c,_},e.fromRotationTranslationValues=function t(n,e,r,o,s,a,u){var c=new i.ARRAY_TYPE(8);c[0]=n,c[1]=e,c[2]=r,c[3]=o;var _=.5*s,$=.5*a,f=.5*u;return c[4]=_*o+$*r-f*e,c[5]=$*o+f*n-_*r,c[6]=f*o+_*e-$*n,c[7]=-_*n-$*e-f*r,c},e.fromRotationTranslation=c,e.fromTranslation=function t(n,e){return n[0]=0,n[1]=0,n[2]=0,n[3]=1,n[4]=.5*e[0],n[5]=.5*e[1],n[6]=.5*e[2],n[7]=0,n},e.fromRotation=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n[4]=0,n[5]=0,n[6]=0,n[7]=0,n},e.fromMat4=function t(n,e){var r=o.create();s.getRotation(r,e);var a=new i.ARRAY_TYPE(3);return s.getTranslation(a,e),c(n,r,a),n},e.copy=_,e.identity=function t(n){return n[0]=0,n[1]=0,n[2]=0,n[3]=1,n[4]=0,n[5]=0,n[6]=0,n[7]=0,n},e.set=function t(n,e,r,i,o,s,a,u,c){return n[0]=e,n[1]=r,n[2]=i,n[3]=o,n[4]=s,n[5]=a,n[6]=u,n[7]=c,n},e.getDual=function t(n,e){return n[0]=e[4],n[1]=e[5],n[2]=e[6],n[3]=e[7],n},e.setDual=function t(n,e){return n[4]=e[0],n[5]=e[1],n[6]=e[2],n[7]=e[3],n},e.getTranslation=function t(n,e){var r=e[4],i=e[5],o=e[6],s=e[7],a=-e[0],u=-e[1],c=-e[2],_=e[3];return n[0]=(r*_+s*a+i*c-o*u)*2,n[1]=(i*_+s*u+o*a-r*c)*2,n[2]=(o*_+s*c+r*u-i*a)*2,n},e.translate=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=.5*r[0],c=.5*r[1],_=.5*r[2],$=e[4],f=e[5],p=e[6],l=e[7];return n[0]=i,n[1]=o,n[2]=s,n[3]=a,n[4]=a*u+o*_-s*c+$,n[5]=a*c+s*u-i*_+f,n[6]=a*_+i*c-o*u+p,n[7]=-i*u-o*c-s*_+l,n},e.rotateX=function t(n,e,r){var i=-e[0],s=-e[1],a=-e[2],u=e[3],c=e[4],_=e[5],$=e[6],f=e[7],p=c*u+f*i+_*a-$*s,l=_*u+f*s+$*i-c*a,h=$*u+f*a+c*s-_*i,d=f*u-c*i-_*s-$*a;return o.rotateX(n,e,r),i=n[0],s=n[1],a=n[2],u=n[3],n[4]=p*u+d*i+l*a-h*s,n[5]=l*u+d*s+h*i-p*a,n[6]=h*u+d*a+p*s-l*i,n[7]=d*u-p*i-l*s-h*a,n},e.rotateY=function t(n,e,r){var i=-e[0],s=-e[1],a=-e[2],u=e[3],c=e[4],_=e[5],$=e[6],f=e[7],p=c*u+f*i+_*a-$*s,l=_*u+f*s+$*i-c*a,h=$*u+f*a+c*s-_*i,d=f*u-c*i-_*s-$*a;return o.rotateY(n,e,r),i=n[0],s=n[1],a=n[2],u=n[3],n[4]=p*u+d*i+l*a-h*s,n[5]=l*u+d*s+h*i-p*a,n[6]=h*u+d*a+p*s-l*i,n[7]=d*u-p*i-l*s-h*a,n},e.rotateZ=function t(n,e,r){var i=-e[0],s=-e[1],a=-e[2],u=e[3],c=e[4],_=e[5],$=e[6],f=e[7],p=c*u+f*i+_*a-$*s,l=_*u+f*s+$*i-c*a,h=$*u+f*a+c*s-_*i,d=f*u-c*i-_*s-$*a;return o.rotateZ(n,e,r),i=n[0],s=n[1],a=n[2],u=n[3],n[4]=p*u+d*i+l*a-h*s,n[5]=l*u+d*s+h*i-p*a,n[6]=h*u+d*a+p*s-l*i,n[7]=d*u-p*i-l*s-h*a,n},e.rotateByQuatAppend=function t(n,e,r){var i=r[0],o=r[1],s=r[2],a=r[3],u=e[0],c=e[1],_=e[2],$=e[3];return n[0]=u*a+$*i+c*s-_*o,n[1]=c*a+$*o+_*i-u*s,n[2]=_*a+$*s+u*o-c*i,n[3]=$*a-u*i-c*o-_*s,u=e[4],c=e[5],_=e[6],$=e[7],n[4]=u*a+$*i+c*s-_*o,n[5]=c*a+$*o+_*i-u*s,n[6]=_*a+$*s+u*o-c*i,n[7]=$*a-u*i-c*o-_*s,n},e.rotateByQuatPrepend=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3],u=r[0],c=r[1],_=r[2],$=r[3];return n[0]=i*$+a*u+o*_-s*c,n[1]=o*$+a*c+s*u-i*_,n[2]=s*$+a*_+i*c-o*u,n[3]=a*$-i*u-o*c-s*_,u=r[4],c=r[5],_=r[6],$=r[7],n[4]=i*$+a*u+o*_-s*c,n[5]=o*$+a*c+s*u-i*_,n[6]=s*$+a*_+i*c-o*u,n[7]=a*$-i*u-o*c-s*_,n},e.rotateAroundAxis=function t(n,e,r,o){if(Math.abs(o)<i.EPSILON)return _(n,e);var s=Math.hypot(r[0],r[1],r[2]),a=Math.sin(o*=.5),u=a*r[0]/s,c=a*r[1]/s,$=a*r[2]/s,f=Math.cos(o),p=e[0],l=e[1],h=e[2],d=e[3];n[0]=p*f+d*u+l*$-h*c,n[1]=l*f+d*c+h*u-p*$,n[2]=h*f+d*$+p*c-l*u,n[3]=d*f-p*u-l*c-h*$;var m=e[4],v=e[5],y=e[6],b=e[7];return n[4]=m*f+b*u+v*$-y*c,n[5]=v*f+b*c+y*u-m*$,n[6]=y*f+b*$+m*c-v*u,n[7]=b*f-m*u-v*c-y*$,n},e.add=function t(n,e,r){return n[0]=e[0]+r[0],n[1]=e[1]+r[1],n[2]=e[2]+r[2],n[3]=e[3]+r[3],n[4]=e[4]+r[4],n[5]=e[5]+r[5],n[6]=e[6]+r[6],n[7]=e[7]+r[7],n},e.multiply=p,e.scale=function t(n,e,r){return n[0]=e[0]*r,n[1]=e[1]*r,n[2]=e[2]*r,n[3]=e[3]*r,n[4]=e[4]*r,n[5]=e[5]*r,n[6]=e[6]*r,n[7]=e[7]*r,n},e.lerp=function t(n,e,r,i){var o=1-i;return 0>l(e,r)&&(i=-i),n[0]=e[0]*o+r[0]*i,n[1]=e[1]*o+r[1]*i,n[2]=e[2]*o+r[2]*i,n[3]=e[3]*o+r[3]*i,n[4]=e[4]*o+r[4]*i,n[5]=e[5]*o+r[5]*i,n[6]=e[6]*o+r[6]*i,n[7]=e[7]*o+r[7]*i,n},e.invert=function t(n,e){var r=d(e);return n[0]=-e[0]/r,n[1]=-e[1]/r,n[2]=-e[2]/r,n[3]=e[3]/r,n[4]=-e[4]/r,n[5]=-e[5]/r,n[6]=-e[6]/r,n[7]=e[7]/r,n},e.conjugate=function t(n,e){return n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=e[3],n[4]=-e[4],n[5]=-e[5],n[6]=-e[6],n[7]=e[7],n},e.normalize=function t(n,e){var r=d(e);if(r>0){r=Math.sqrt(r);var i=e[0]/r,o=e[1]/r,s=e[2]/r,a=e[3]/r,u=e[4],c=e[5],_=e[6],$=e[7],f=i*u+o*c+s*_+a*$;n[0]=i,n[1]=o,n[2]=s,n[3]=a,n[4]=(u-i*f)/r,n[5]=(c-o*f)/r,n[6]=(_-s*f)/r,n[7]=($-a*f)/r}return n},e.str=function t(n){return"quat2("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+", "+n[4]+", "+n[5]+", "+n[6]+", "+n[7]+")"},e.exactEquals=function t(n,e){return n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]&&n[3]===e[3]&&n[4]===e[4]&&n[5]===e[5]&&n[6]===e[6]&&n[7]===e[7]},e.equals=function t(n,e){var r=n[0],o=n[1],s=n[2],a=n[3],u=n[4],c=n[5],_=n[6],$=n[7],f=e[0],p=e[1],l=e[2],h=e[3],d=e[4],m=e[5],v=e[6],y=e[7];return Math.abs(r-f)<=i.EPSILON*Math.max(1,Math.abs(r),Math.abs(f))&&Math.abs(o-p)<=i.EPSILON*Math.max(1,Math.abs(o),Math.abs(p))&&Math.abs(s-l)<=i.EPSILON*Math.max(1,Math.abs(s),Math.abs(l))&&Math.abs(a-h)<=i.EPSILON*Math.max(1,Math.abs(a),Math.abs(h))&&Math.abs(u-d)<=i.EPSILON*Math.max(1,Math.abs(u),Math.abs(d))&&Math.abs(c-m)<=i.EPSILON*Math.max(1,Math.abs(c),Math.abs(m))&&Math.abs(_-v)<=i.EPSILON*Math.max(1,Math.abs(_),Math.abs(v))&&Math.abs($-y)<=i.EPSILON*Math.max(1,Math.abs($),Math.abs(y))},e.sqrLen=e.squaredLength=e.len=e.length=e.dot=e.mul=e.setReal=e.getReal=void 0;var i=u(t("./common.js")),o=u(t("./quat.js")),s=u(t("./mat4.js"));function a(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return a=function n(){return t},t}function u(t){if(t&&t.__esModule)return t;if(null===t||"object"!==r(t)&&"function"!=typeof t)return{default:t};var n=a();if(n&&n.has(t))return n.get(t);var e={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if(Object.prototype.hasOwnProperty.call(t,o)){var s=i?Object.getOwnPropertyDescriptor(t,o):null;s&&(s.get||s.set)?Object.defineProperty(e,o,s):e[o]=t[o]}return e.default=t,n&&n.set(t,e),e}function c(t,n,e){var r=.5*e[0],i=.5*e[1],o=.5*e[2],s=n[0],a=n[1],u=n[2],c=n[3];return t[0]=s,t[1]=a,t[2]=u,t[3]=c,t[4]=r*c+i*u-o*a,t[5]=i*c+o*s-r*u,t[6]=o*c+r*a-i*s,t[7]=-r*s-i*a-o*u,t}function _(t,n){return t[0]=n[0],t[1]=n[1],t[2]=n[2],t[3]=n[3],t[4]=n[4],t[5]=n[5],t[6]=n[6],t[7]=n[7],t}var $=o.copy;e.getReal=$;var f=o.copy;function p(t,n,e){var r=n[0],i=n[1],o=n[2],s=n[3],a=e[4],u=e[5],c=e[6],_=e[7],$=n[4],f=n[5],p=n[6],l=n[7],h=e[0],d=e[1],m=e[2],v=e[3];return t[0]=r*v+s*h+i*m-o*d,t[1]=i*v+s*d+o*h-r*m,t[2]=o*v+s*m+r*d-i*h,t[3]=s*v-r*h-i*d-o*m,t[4]=r*_+s*a+i*c-o*u+$*v+l*h+f*m-p*d,t[5]=i*_+s*u+o*a-r*c+f*v+l*d+p*h-$*m,t[6]=o*_+s*c+r*u-i*a+p*v+l*m+$*d-f*h,t[7]=s*_-r*a-i*u-o*c+l*v-$*h-f*d-p*m,t}e.setReal=f,e.mul=p;var l=o.dot;e.dot=l;var h=o.length;e.length=h,e.len=h;var d=o.squaredLength;e.squaredLength=d,e.sqrLen=d},{"./common.js":20,"./mat4.js":25,"./quat.js":26}],28:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=a,e.clone=function t(n){var e=new o.ARRAY_TYPE(2);return e[0]=n[0],e[1]=n[1],e},e.fromValues=function t(n,e){var r=new o.ARRAY_TYPE(2);return r[0]=n,r[1]=e,r},e.copy=function t(n,e){return n[0]=e[0],n[1]=e[1],n},e.set=function t(n,e,r){return n[0]=e,n[1]=r,n},e.add=function t(n,e,r){return n[0]=e[0]+r[0],n[1]=e[1]+r[1],n},e.subtract=u,e.multiply=c,e.divide=_,e.ceil=function t(n,e){return n[0]=Math.ceil(e[0]),n[1]=Math.ceil(e[1]),n},e.floor=function t(n,e){return n[0]=Math.floor(e[0]),n[1]=Math.floor(e[1]),n},e.min=function t(n,e,r){return n[0]=Math.min(e[0],r[0]),n[1]=Math.min(e[1],r[1]),n},e.max=function t(n,e,r){return n[0]=Math.max(e[0],r[0]),n[1]=Math.max(e[1],r[1]),n},e.round=function t(n,e){return n[0]=Math.round(e[0]),n[1]=Math.round(e[1]),n},e.scale=function t(n,e,r){return n[0]=e[0]*r,n[1]=e[1]*r,n},e.scaleAndAdd=function t(n,e,r,i){return n[0]=e[0]+r[0]*i,n[1]=e[1]+r[1]*i,n},e.distance=$,e.squaredDistance=f,e.length=p,e.squaredLength=l,e.negate=function t(n,e){return n[0]=-e[0],n[1]=-e[1],n},e.inverse=function t(n,e){return n[0]=1/e[0],n[1]=1/e[1],n},e.normalize=function t(n,e){var r=e[0],i=e[1],o=r*r+i*i;return o>0&&(o=1/Math.sqrt(o)),n[0]=e[0]*o,n[1]=e[1]*o,n},e.dot=function t(n,e){return n[0]*e[0]+n[1]*e[1]},e.cross=function t(n,e,r){var i=e[0]*r[1]-e[1]*r[0];return n[0]=n[1]=0,n[2]=i,n},e.lerp=function t(n,e,r,i){var o=e[0],s=e[1];return n[0]=o+i*(r[0]-o),n[1]=s+i*(r[1]-s),n},e.random=function t(n,e){e=e||1;var r=2*o.RANDOM()*Math.PI;return n[0]=Math.cos(r)*e,n[1]=Math.sin(r)*e,n},e.transformMat2=function t(n,e,r){var i=e[0],o=e[1];return n[0]=r[0]*i+r[2]*o,n[1]=r[1]*i+r[3]*o,n},e.transformMat2d=function t(n,e,r){var i=e[0],o=e[1];return n[0]=r[0]*i+r[2]*o+r[4],n[1]=r[1]*i+r[3]*o+r[5],n},e.transformMat3=function t(n,e,r){var i=e[0],o=e[1];return n[0]=r[0]*i+r[3]*o+r[6],n[1]=r[1]*i+r[4]*o+r[7],n},e.transformMat4=function t(n,e,r){var i=e[0],o=e[1];return n[0]=r[0]*i+r[4]*o+r[12],n[1]=r[1]*i+r[5]*o+r[13],n},e.rotate=function t(n,e,r,i){var o=e[0]-r[0],s=e[1]-r[1],a=Math.sin(i),u=Math.cos(i);return n[0]=o*u-s*a+r[0],n[1]=o*a+s*u+r[1],n},e.angle=function t(n,e){var r=n[0],i=n[1],o=e[0],s=e[1],a=Math.sqrt(r*r+i*i)*Math.sqrt(o*o+s*s);return Math.acos(Math.min(Math.max(a&&(r*o+i*s)/a,-1),1))},e.zero=function t(n){return n[0]=0,n[1]=0,n},e.str=function t(n){return"vec2("+n[0]+", "+n[1]+")"},e.exactEquals=function t(n,e){return n[0]===e[0]&&n[1]===e[1]},e.equals=function t(n,e){var r=n[0],i=n[1],s=e[0],a=e[1];return Math.abs(r-s)<=o.EPSILON*Math.max(1,Math.abs(r),Math.abs(s))&&Math.abs(i-a)<=o.EPSILON*Math.max(1,Math.abs(i),Math.abs(a))},e.forEach=e.sqrLen=e.sqrDist=e.dist=e.div=e.mul=e.sub=e.len=void 0;var i,o=function t(n){if(n&&n.__esModule)return n;if(null===n||"object"!==r(n)&&"function"!=typeof n)return{default:n};var e=s();if(e&&e.has(n))return e.get(n);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in n)if(Object.prototype.hasOwnProperty.call(n,a)){var u=o?Object.getOwnPropertyDescriptor(n,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=n[a]}return i.default=n,e&&e.set(n,i),i}(t("./common.js"));function s(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return s=function n(){return t},t}function a(){var t=new o.ARRAY_TYPE(2);return o.ARRAY_TYPE!=Float32Array&&(t[0]=0,t[1]=0),t}function u(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t}function c(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t}function _(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t}function $(t,n){var e;return Math.hypot(n[0]-t[0],n[1]-t[1])}function f(t,n){var e=n[0]-t[0],r=n[1]-t[1];return e*e+r*r}function p(t){var n;return Math.hypot(t[0],t[1])}function l(t){var n=t[0],e=t[1];return n*n+e*e}e.len=p,e.sub=u,e.mul=c,e.div=_,e.dist=$,e.sqrDist=f,e.sqrLen=l;var h=(i=a(),function(t,n,e,r,o,s){var a,u;for(n||(n=2),e||(e=0),u=r?Math.min(r*n+e,t.length):t.length,a=e;a<u;a+=n)i[0]=t[a],i[1]=t[a+1],o(i,i,s),t[a]=i[0],t[a+1]=i[1];return t});e.forEach=h},{"./common.js":20}],29:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=a,e.clone=function t(n){var e=new o.ARRAY_TYPE(3);return e[0]=n[0],e[1]=n[1],e[2]=n[2],e},e.length=u,e.fromValues=function t(n,e,r){var i=new o.ARRAY_TYPE(3);return i[0]=n,i[1]=e,i[2]=r,i},e.copy=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n},e.set=function t(n,e,r,i){return n[0]=e,n[1]=r,n[2]=i,n},e.add=function t(n,e,r){return n[0]=e[0]+r[0],n[1]=e[1]+r[1],n[2]=e[2]+r[2],n},e.subtract=c,e.multiply=_,e.divide=$,e.ceil=function t(n,e){return n[0]=Math.ceil(e[0]),n[1]=Math.ceil(e[1]),n[2]=Math.ceil(e[2]),n},e.floor=function t(n,e){return n[0]=Math.floor(e[0]),n[1]=Math.floor(e[1]),n[2]=Math.floor(e[2]),n},e.min=function t(n,e,r){return n[0]=Math.min(e[0],r[0]),n[1]=Math.min(e[1],r[1]),n[2]=Math.min(e[2],r[2]),n},e.max=function t(n,e,r){return n[0]=Math.max(e[0],r[0]),n[1]=Math.max(e[1],r[1]),n[2]=Math.max(e[2],r[2]),n},e.round=function t(n,e){return n[0]=Math.round(e[0]),n[1]=Math.round(e[1]),n[2]=Math.round(e[2]),n},e.scale=function t(n,e,r){return n[0]=e[0]*r,n[1]=e[1]*r,n[2]=e[2]*r,n},e.scaleAndAdd=function t(n,e,r,i){return n[0]=e[0]+r[0]*i,n[1]=e[1]+r[1]*i,n[2]=e[2]+r[2]*i,n},e.distance=f,e.squaredDistance=p,e.squaredLength=l,e.negate=function t(n,e){return n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n},e.inverse=function t(n,e){return n[0]=1/e[0],n[1]=1/e[1],n[2]=1/e[2],n},e.normalize=function t(n,e){var r=e[0],i=e[1],o=e[2],s=r*r+i*i+o*o;return s>0&&(s=1/Math.sqrt(s)),n[0]=e[0]*s,n[1]=e[1]*s,n[2]=e[2]*s,n},e.dot=h,e.cross=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=r[0],u=r[1],c=r[2];return n[0]=o*c-s*u,n[1]=s*a-i*c,n[2]=i*u-o*a,n},e.lerp=function t(n,e,r,i){var o=e[0],s=e[1],a=e[2];return n[0]=o+i*(r[0]-o),n[1]=s+i*(r[1]-s),n[2]=a+i*(r[2]-a),n},e.hermite=function t(n,e,r,i,o,s){var a=s*s,u=a*(2*s-3)+1,c=a*(s-2)+s,_=a*(s-1),$=a*(3-2*s);return n[0]=e[0]*u+r[0]*c+i[0]*_+o[0]*$,n[1]=e[1]*u+r[1]*c+i[1]*_+o[1]*$,n[2]=e[2]*u+r[2]*c+i[2]*_+o[2]*$,n},e.bezier=function t(n,e,r,i,o,s){var a=1-s,u=a*a,c=s*s,_=u*a,$=3*s*u,f=3*c*a,p=c*s;return n[0]=e[0]*_+r[0]*$+i[0]*f+o[0]*p,n[1]=e[1]*_+r[1]*$+i[1]*f+o[1]*p,n[2]=e[2]*_+r[2]*$+i[2]*f+o[2]*p,n},e.random=function t(n,e){e=e||1;var r=2*o.RANDOM()*Math.PI,i=2*o.RANDOM()-1,s=Math.sqrt(1-i*i)*e;return n[0]=Math.cos(r)*s,n[1]=Math.sin(r)*s,n[2]=i*e,n},e.transformMat4=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=r[3]*i+r[7]*o+r[11]*s+r[15];return a=a||1,n[0]=(r[0]*i+r[4]*o+r[8]*s+r[12])/a,n[1]=(r[1]*i+r[5]*o+r[9]*s+r[13])/a,n[2]=(r[2]*i+r[6]*o+r[10]*s+r[14])/a,n},e.transformMat3=function t(n,e,r){var i=e[0],o=e[1],s=e[2];return n[0]=i*r[0]+o*r[3]+s*r[6],n[1]=i*r[1]+o*r[4]+s*r[7],n[2]=i*r[2]+o*r[5]+s*r[8],n},e.transformQuat=function t(n,e,r){var i=r[0],o=r[1],s=r[2],a=r[3],u=e[0],c=e[1],_=e[2],$=o*_-s*c,f=s*u-i*_,p=i*c-o*u,l=o*p-s*f,h=s*$-i*p,d=i*f-o*$,m=2*a;return $*=m,f*=m,p*=m,l*=2,h*=2,d*=2,n[0]=u+$+l,n[1]=c+f+h,n[2]=_+p+d,n},e.rotateX=function t(n,e,r,i){var o=[],s=[];return o[0]=e[0]-r[0],o[1]=e[1]-r[1],o[2]=e[2]-r[2],s[0]=o[0],s[1]=o[1]*Math.cos(i)-o[2]*Math.sin(i),s[2]=o[1]*Math.sin(i)+o[2]*Math.cos(i),n[0]=s[0]+r[0],n[1]=s[1]+r[1],n[2]=s[2]+r[2],n},e.rotateY=function t(n,e,r,i){var o=[],s=[];return o[0]=e[0]-r[0],o[1]=e[1]-r[1],o[2]=e[2]-r[2],s[0]=o[2]*Math.sin(i)+o[0]*Math.cos(i),s[1]=o[1],s[2]=o[2]*Math.cos(i)-o[0]*Math.sin(i),n[0]=s[0]+r[0],n[1]=s[1]+r[1],n[2]=s[2]+r[2],n},e.rotateZ=function t(n,e,r,i){var o=[],s=[];return o[0]=e[0]-r[0],o[1]=e[1]-r[1],o[2]=e[2]-r[2],s[0]=o[0]*Math.cos(i)-o[1]*Math.sin(i),s[1]=o[0]*Math.sin(i)+o[1]*Math.cos(i),s[2]=o[2],n[0]=s[0]+r[0],n[1]=s[1]+r[1],n[2]=s[2]+r[2],n},e.angle=function t(n,e){var r=n[0],i=n[1],o=n[2],s=e[0],a=e[1],u=e[2],c=Math.sqrt(r*r+i*i+o*o)*Math.sqrt(s*s+a*a+u*u);return Math.acos(Math.min(Math.max(c&&h(n,e)/c,-1),1))},e.zero=function t(n){return n[0]=0,n[1]=0,n[2]=0,n},e.str=function t(n){return"vec3("+n[0]+", "+n[1]+", "+n[2]+")"},e.exactEquals=function t(n,e){return n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]},e.equals=function t(n,e){var r=n[0],i=n[1],s=n[2],a=e[0],u=e[1],c=e[2];return Math.abs(r-a)<=o.EPSILON*Math.max(1,Math.abs(r),Math.abs(a))&&Math.abs(i-u)<=o.EPSILON*Math.max(1,Math.abs(i),Math.abs(u))&&Math.abs(s-c)<=o.EPSILON*Math.max(1,Math.abs(s),Math.abs(c))},e.forEach=e.sqrLen=e.len=e.sqrDist=e.dist=e.div=e.mul=e.sub=void 0;var i,o=function t(n){if(n&&n.__esModule)return n;if(null===n||"object"!==r(n)&&"function"!=typeof n)return{default:n};var e=s();if(e&&e.has(n))return e.get(n);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in n)if(Object.prototype.hasOwnProperty.call(n,a)){var u=o?Object.getOwnPropertyDescriptor(n,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=n[a]}return i.default=n,e&&e.set(n,i),i}(t("./common.js"));function s(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return s=function n(){return t},t}function a(){var t=new o.ARRAY_TYPE(3);return o.ARRAY_TYPE!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0),t}function u(t){var n,e=t[0];return Math.hypot(e,t[1],t[2])}function c(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t}function _(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t[2]=n[2]*e[2],t}function $(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t[2]=n[2]/e[2],t}function f(t,n){var e,r=n[0]-t[0];return Math.hypot(r,n[1]-t[1],n[2]-t[2])}function p(t,n){var e=n[0]-t[0],r=n[1]-t[1],i=n[2]-t[2];return e*e+r*r+i*i}function l(t){var n=t[0],e=t[1],r=t[2];return n*n+e*e+r*r}function h(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}e.sub=c,e.mul=_,e.div=$,e.dist=f,e.sqrDist=p,e.len=u,e.sqrLen=l;var d=(i=a(),function(t,n,e,r,o,s){var a,u;for(n||(n=3),e||(e=0),u=r?Math.min(r*n+e,t.length):t.length,a=e;a<u;a+=n)i[0]=t[a],i[1]=t[a+1],i[2]=t[a+2],o(i,i,s),t[a]=i[0],t[a+1]=i[1],t[a+2]=i[2];return t});e.forEach=d},{"./common.js":20}],30:[function(t,n,e){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function t(n){return typeof n}:function t(n){return n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n})(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.create=a,e.clone=function t(n){var e=new o.ARRAY_TYPE(4);return e[0]=n[0],e[1]=n[1],e[2]=n[2],e[3]=n[3],e},e.fromValues=function t(n,e,r,i){var s=new o.ARRAY_TYPE(4);return s[0]=n,s[1]=e,s[2]=r,s[3]=i,s},e.copy=function t(n,e){return n[0]=e[0],n[1]=e[1],n[2]=e[2],n[3]=e[3],n},e.set=function t(n,e,r,i,o){return n[0]=e,n[1]=r,n[2]=i,n[3]=o,n},e.add=function t(n,e,r){return n[0]=e[0]+r[0],n[1]=e[1]+r[1],n[2]=e[2]+r[2],n[3]=e[3]+r[3],n},e.subtract=u,e.multiply=c,e.divide=_,e.ceil=function t(n,e){return n[0]=Math.ceil(e[0]),n[1]=Math.ceil(e[1]),n[2]=Math.ceil(e[2]),n[3]=Math.ceil(e[3]),n},e.floor=function t(n,e){return n[0]=Math.floor(e[0]),n[1]=Math.floor(e[1]),n[2]=Math.floor(e[2]),n[3]=Math.floor(e[3]),n},e.min=function t(n,e,r){return n[0]=Math.min(e[0],r[0]),n[1]=Math.min(e[1],r[1]),n[2]=Math.min(e[2],r[2]),n[3]=Math.min(e[3],r[3]),n},e.max=function t(n,e,r){return n[0]=Math.max(e[0],r[0]),n[1]=Math.max(e[1],r[1]),n[2]=Math.max(e[2],r[2]),n[3]=Math.max(e[3],r[3]),n},e.round=function t(n,e){return n[0]=Math.round(e[0]),n[1]=Math.round(e[1]),n[2]=Math.round(e[2]),n[3]=Math.round(e[3]),n},e.scale=function t(n,e,r){return n[0]=e[0]*r,n[1]=e[1]*r,n[2]=e[2]*r,n[3]=e[3]*r,n},e.scaleAndAdd=function t(n,e,r,i){return n[0]=e[0]+r[0]*i,n[1]=e[1]+r[1]*i,n[2]=e[2]+r[2]*i,n[3]=e[3]+r[3]*i,n},e.distance=$,e.squaredDistance=f,e.length=p,e.squaredLength=l,e.negate=function t(n,e){return n[0]=-e[0],n[1]=-e[1],n[2]=-e[2],n[3]=-e[3],n},e.inverse=function t(n,e){return n[0]=1/e[0],n[1]=1/e[1],n[2]=1/e[2],n[3]=1/e[3],n},e.normalize=function t(n,e){var r=e[0],i=e[1],o=e[2],s=e[3],a=r*r+i*i+o*o+s*s;return a>0&&(a=1/Math.sqrt(a)),n[0]=r*a,n[1]=i*a,n[2]=o*a,n[3]=s*a,n},e.dot=function t(n,e){return n[0]*e[0]+n[1]*e[1]+n[2]*e[2]+n[3]*e[3]},e.cross=function t(n,e,r,i){var o=r[0]*i[1]-r[1]*i[0],s=r[0]*i[2]-r[2]*i[0],a=r[0]*i[3]-r[3]*i[0],u=r[1]*i[2]-r[2]*i[1],c=r[1]*i[3]-r[3]*i[1],_=r[2]*i[3]-r[3]*i[2],$=e[0],f=e[1],p=e[2],l=e[3];return n[0]=f*_-p*c+l*u,n[1]=-($*_)+p*a-l*s,n[2]=$*c-f*a+l*o,n[3]=-($*u)+f*s-p*o,n},e.lerp=function t(n,e,r,i){var o=e[0],s=e[1],a=e[2],u=e[3];return n[0]=o+i*(r[0]-o),n[1]=s+i*(r[1]-s),n[2]=a+i*(r[2]-a),n[3]=u+i*(r[3]-u),n},e.random=function t(n,e){e=e||1;do u=(r=2*o.RANDOM()-1)*r+(i=2*o.RANDOM()-1)*i;while(u>=1);do c=(s=2*o.RANDOM()-1)*s+(a=2*o.RANDOM()-1)*a;while(c>=1);var r,i,s,a,u,c,_=Math.sqrt((1-u)/c);return n[0]=e*r,n[1]=e*i,n[2]=e*s*_,n[3]=e*a*_,n},e.transformMat4=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=e[3];return n[0]=r[0]*i+r[4]*o+r[8]*s+r[12]*a,n[1]=r[1]*i+r[5]*o+r[9]*s+r[13]*a,n[2]=r[2]*i+r[6]*o+r[10]*s+r[14]*a,n[3]=r[3]*i+r[7]*o+r[11]*s+r[15]*a,n},e.transformQuat=function t(n,e,r){var i=e[0],o=e[1],s=e[2],a=r[0],u=r[1],c=r[2],_=r[3],$=_*i+u*s-c*o,f=_*o+c*i-a*s,p=_*s+a*o-u*i,l=-a*i-u*o-c*s;return n[0]=$*_+-(l*a)+-(f*c)- -(p*u),n[1]=f*_+-(l*u)+-(p*a)- -($*c),n[2]=p*_+-(l*c)+-($*u)- -(f*a),n[3]=e[3],n},e.zero=function t(n){return n[0]=0,n[1]=0,n[2]=0,n[3]=0,n},e.str=function t(n){return"vec4("+n[0]+", "+n[1]+", "+n[2]+", "+n[3]+")"},e.exactEquals=function t(n,e){return n[0]===e[0]&&n[1]===e[1]&&n[2]===e[2]&&n[3]===e[3]},e.equals=function t(n,e){var r=n[0],i=n[1],s=n[2],a=n[3],u=e[0],c=e[1],_=e[2],$=e[3];return Math.abs(r-u)<=o.EPSILON*Math.max(1,Math.abs(r),Math.abs(u))&&Math.abs(i-c)<=o.EPSILON*Math.max(1,Math.abs(i),Math.abs(c))&&Math.abs(s-_)<=o.EPSILON*Math.max(1,Math.abs(s),Math.abs(_))&&Math.abs(a-$)<=o.EPSILON*Math.max(1,Math.abs(a),Math.abs($))},e.forEach=e.sqrLen=e.len=e.sqrDist=e.dist=e.div=e.mul=e.sub=void 0;var i,o=function t(n){if(n&&n.__esModule)return n;if(null===n||"object"!==r(n)&&"function"!=typeof n)return{default:n};var e=s();if(e&&e.has(n))return e.get(n);var i={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in n)if(Object.prototype.hasOwnProperty.call(n,a)){var u=o?Object.getOwnPropertyDescriptor(n,a):null;u&&(u.get||u.set)?Object.defineProperty(i,a,u):i[a]=n[a]}return i.default=n,e&&e.set(n,i),i}(t("./common.js"));function s(){if("function"!=typeof WeakMap)return null;var t=new WeakMap;return s=function n(){return t},t}function a(){var t=new o.ARRAY_TYPE(4);return o.ARRAY_TYPE!=Float32Array&&(t[0]=0,t[1]=0,t[2]=0,t[3]=0),t}function u(t,n,e){return t[0]=n[0]-e[0],t[1]=n[1]-e[1],t[2]=n[2]-e[2],t[3]=n[3]-e[3],t}function c(t,n,e){return t[0]=n[0]*e[0],t[1]=n[1]*e[1],t[2]=n[2]*e[2],t[3]=n[3]*e[3],t}function _(t,n,e){return t[0]=n[0]/e[0],t[1]=n[1]/e[1],t[2]=n[2]/e[2],t[3]=n[3]/e[3],t}function $(t,n){var e,r=n[0]-t[0],i=n[1]-t[1];return Math.hypot(r,i,n[2]-t[2],n[3]-t[3])}function f(t,n){var e=n[0]-t[0],r=n[1]-t[1],i=n[2]-t[2],o=n[3]-t[3];return e*e+r*r+i*i+o*o}function p(t){var n,e=t[0],r=t[1];return Math.hypot(e,r,t[2],t[3])}function l(t){var n=t[0],e=t[1],r=t[2],i=t[3];return n*n+e*e+r*r+i*i}e.sub=u,e.mul=c,e.div=_,e.dist=$,e.sqrDist=f,e.len=p,e.sqrLen=l;var h=(i=a(),function(t,n,e,r,o,s){var a,u;for(n||(n=4),e||(e=0),u=r?Math.min(r*n+e,t.length):t.length,a=e;a<u;a+=n)i[0]=t[a],i[1]=t[a+1],i[2]=t[a+2],i[3]=t[a+3],o(i,i,s),t[a]=i[0],t[a+1]=i[1],t[a+2]=i[2],t[a+3]=i[3];return t});e.forEach=h},{"./common.js":20}],31:[function(t,n,e){"use strict";var r,i="object"==typeof Reflect?Reflect:null,o=i&&"function"==typeof i.apply?i.apply:function t(n,e,r){return Function.prototype.apply.call(n,e,r)};r=i&&"function"==typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function t(n){return Object.getOwnPropertyNames(n).concat(Object.getOwnPropertySymbols(n))}:function t(n){return Object.getOwnPropertyNames(n)};var s=Number.isNaN||function t(n){return n!=n};function a(){a.init.call(this)}n.exports=a,n.exports.once=function t(n,e){return new Promise(function(t,r){var i,o,s;function a(t){n.removeListener(e,u),r(t)}function u(){"function"==typeof n.removeListener&&n.removeListener("error",a),t([].slice.call(arguments))}m(n,e,u,{once:!0}),"error"!==e&&(i=n,o=a,s={once:!0},"function"==typeof i.on&&m(i,"error",o,s))})},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var u=10;function c(t){if("function"!=typeof t)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function _(t){return void 0===t._maxListeners?a.defaultMaxListeners:t._maxListeners}function $(t,n,e,r){if(c(e),void 0===(o=t._events)?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",n,e.listener?e.listener:e),o=t._events),s=o[n]),void 0===s)s=o[n]=e,++t._eventsCount;else if("function"==typeof s?s=o[n]=r?[e,s]:[s,e]:r?s.unshift(e):s.push(e),(i=_(t))>0&&s.length>i&&!s.warned){s.warned=!0;var i,o,s,a,u=Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(n)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=t,u.type=n,u.count=s.length,a=u,console&&console.warn&&console.warn(a)}return t}function f(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function p(t,n,e){var r={fired:!1,wrapFn:void 0,target:t,type:n,listener:e},i=f.bind(r);return i.listener=e,r.wrapFn=i,i}function l(t,n,e){var r=t._events;if(void 0===r)return[];var i=r[n];return void 0===i?[]:"function"==typeof i?e?[i.listener||i]:[i]:e?function t(n){for(var e=Array(n.length),r=0;r<e.length;++r)e[r]=n[r].listener||n[r];return e}(i):d(i,i.length)}function h(t){var n=this._events;if(void 0!==n){var e=n[t];if("function"==typeof e)return 1;if(void 0!==e)return e.length}return 0}function d(t,n){for(var e=Array(n),r=0;r<n;++r)e[r]=t[r];return e}function m(t,n,e,r){if("function"==typeof t.on)r.once?t.once(n,e):t.on(n,e);else if("function"==typeof t.addEventListener)t.addEventListener(n,function i(o){r.once&&t.removeEventListener(n,i),e(o)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t)}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return u},set:function(t){if("number"!=typeof t||t<0||s(t))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");u=t}}),a.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function t(n){if("number"!=typeof n||n<0||s(n))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+n+".");return this._maxListeners=n,this},a.prototype.getMaxListeners=function t(){return _(this)},a.prototype.emit=function t(n){for(var e=[],r=1;r<arguments.length;r++)e.push(arguments[r]);var i="error"===n,s=this._events;if(void 0!==s)i=i&&void 0===s.error;else if(!i)return!1;if(i){if(e.length>0&&(a=e[0]),a instanceof Error)throw a;var a,u=Error("Unhandled error."+(a?" ("+a.message+")":""));throw u.context=a,u}var c=s[n];if(void 0===c)return!1;if("function"==typeof c)o(c,this,e);else for(var _=c.length,$=d(c,_),r=0;r<_;++r)o($[r],this,e);return!0},a.prototype.addListener=function t(n,e){return $(this,n,e,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function t(n,e){return $(this,n,e,!0)},a.prototype.once=function t(n,e){return c(e),this.on(n,p(this,n,e)),this},a.prototype.prependOnceListener=function t(n,e){return c(e),this.prependListener(n,p(this,n,e)),this},a.prototype.removeListener=function t(n,e){var r,i,o,s,a;if(c(e),void 0===(i=this._events)||void 0===(r=i[n]))return this;if(r===e||r.listener===e)0==--this._eventsCount?this._events=Object.create(null):(delete i[n],i.removeListener&&this.emit("removeListener",n,r.listener||e));else if("function"!=typeof r){for(o=-1,s=r.length-1;s>=0;s--)if(r[s]===e||r[s].listener===e){a=r[s].listener,o=s;break}if(o<0)return this;0===o?r.shift():function t(n,e){for(;e+1<n.length;e++)n[e]=n[e+1];n.pop()}(r,o),1===r.length&&(i[n]=r[0]),void 0!==i.removeListener&&this.emit("removeListener",n,a||e)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function t(n){var e,r,i;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[n]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[n]),this;if(0===arguments.length){var o,s=Object.keys(r);for(i=0;i<s.length;++i)"removeListener"!==(o=s[i])&&this.removeAllListeners(o);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(e=r[n]))this.removeListener(n,e);else if(void 0!==e)for(i=e.length-1;i>=0;i--)this.removeListener(n,e[i]);return this},a.prototype.listeners=function t(n){return l(this,n,!0)},a.prototype.rawListeners=function t(n){return l(this,n,!1)},a.listenerCount=function(t,n){return"function"==typeof t.listenerCount?t.listenerCount(n):h.call(t,n)},a.prototype.listenerCount=h,a.prototype.eventNames=function t(){return this._eventsCount>0?r(this._events):[]}},{}],32:[function(t,n,e){var r,i,o,s=n.exports={};function a(){throw Error("setTimeout has not been defined")}function u(){throw Error("clearTimeout has not been defined")}function c(t){if(r===setTimeout)return setTimeout(t,0);if((r===a||!r)&&setTimeout)return r=setTimeout,setTimeout(t,0);try{return r(t,0)}catch(n){try{return r.call(null,t,0)}catch(e){return r.call(this,t,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:a}catch(t){r=a}try{i="function"==typeof clearTimeout?clearTimeout:u}catch(n){i=u}}();var _=[],$=!1,f=-1;function p(){$&&o&&($=!1,o.length?_=o.concat(_):f=-1,_.length&&l())}function l(){if(!$){var t=c(p);$=!0;for(var n=_.length;n;){for(o=_,_=[];++f<n;)o&&o[f].run();f=-1,n=_.length}o=null,$=!1,function t(n){if(i===clearTimeout)return clearTimeout(n);if((i===u||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(n);try{return i(n)}catch(e){try{return i.call(null,n)}catch(r){return i.call(this,n)}}}(t)}}function h(t,n){this.fun=t,this.array=n}function d(){}s.nextTick=function(t){var n=Array(arguments.length-1);if(arguments.length>1)for(var e=1;e<arguments.length;e++)n[e-1]=arguments[e];_.push(new h(t,n)),1!==_.length||$||c(l)},h.prototype.run=function(){this.fun.apply(null,this.array)},s.title="browser",s.browser=!0,s.env={},s.argv=[],s.version="",s.versions={},s.on=d,s.addListener=d,s.once=d,s.off=d,s.removeListener=d,s.removeAllListeners=d,s.emit=d,s.prependListener=d,s.prependOnceListener=d,s.listeners=function(t){return[]},s.binding=function(t){throw Error("process.binding is not supported")},s.cwd=function(){return"/"},s.chdir=function(t){throw Error("process.chdir is not supported")},s.umask=function(){return 0}},{}],33:[function(t,n,e){"use strict";n.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},{}],34:[function(t,n,e){"undefined"!=typeof window?("function"!=typeof window.requestAnimationFrame&&(window.requestAnimationFrame=window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){setTimeout(t,1e3/60)}),window.Leap=t("../lib/index")):Leap=t("../lib/index")},{"../lib/index":11}]},{},[34]);