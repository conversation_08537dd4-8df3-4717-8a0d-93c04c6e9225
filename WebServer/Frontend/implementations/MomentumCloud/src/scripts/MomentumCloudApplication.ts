// Copyright Digital Domain, Inc. All Rights Reserved.
import { PixelStreaming, Flags, LatencyTestResults, AggregatedStats, CandidatePairStats, InitialSettings } from '@epicgames-ps/lib-pixelstreamingfrontend-ue5.4';
import { langCodeToDisplayName } from './LanguageCode';
import { Modal } from 'bootstrap';
import { VideoPlayerConfigManager } from './VideoPlayerConfig';

export enum TTS_ENGINE_ID {
    Flite = 0,
    ReadSpeaker,
    iFlytek,
    AzureSpeech,
    AmazonPolly,
    GoogleCloud,
};

export enum TTS_CHATBOT_ID {
    ChatGPT = 0,
    AzureOpenAI,
    TongyiQianwen,
    OpenAIRealtime,
};

export class micRecorder {

    private recording = false;
    private audioContext: any = null;
    private stream: MediaStream = null;
    private audioBuffer = new Int16Array(0);

    public sampleRate = 16000;
    public numChannels = 1;

    constructor() {
    }

    async start() {
        try {
            this.audioBuffer = new Int16Array(0);
            this.audioContext = new ((<any>window).AudioContext || (<any>window).webkitAudioContext)({sampleRate: this.sampleRate});

            let audioOptions = {
                autoGainControl: false,
                channelCount: this.numChannels,
                echoCancellation: false,
                latency: 0,
                noiseSuppression: true,
                sampleRate: this.sampleRate,
                volume: 1.0
            };
            this.stream = await navigator.mediaDevices.getUserMedia({video: false, audio: audioOptions});

            let convertbitsworklet =
                `class ConvertBitsProcessor extends AudioWorkletProcessor {
                    constructor() {
                        super();
                    }

                    convertFloat32ToInt16(inputs) {
                        const inputChannelData = inputs[0][0];
                        return Int16Array.from(inputChannelData, (n) => {
                            const res = n < 0 ? n * 32768 : n * 32767; // convert in range [-32768, 32767]
                            return Math.max(-32768, Math.min(32767, res)); // clamp
                        });
                    }

                    process(inputs) {
                        if (inputs[0].length != 0) {
                            const val = this.convertFloat32ToInt16(inputs);
                            this.port.postMessage({ eventType: "data", audioBuffer: val })
                        }
                        return true;
                    }
                }
                registerProcessor("convert-bits-processor", ConvertBitsProcessor);`

            let reader = new FileReader();
            let moduleURI = await new Promise((resolve) => {
                reader.onloadend = function () {
                    resolve(reader.result);
                }
                reader.readAsDataURL(new Blob([convertbitsworklet], {type: 'application/javascript'}));
            });
            await this.audioContext.audioWorklet.addModule(moduleURI);

            const processNode = new AudioWorkletNode(this.audioContext, "convert-bits-processor", {channelCount: this.numChannels});
            processNode.port.onmessage = (e) => {
                if (e.data.eventType === "data") {
                    let ori = this.audioBuffer;
                    this.audioBuffer = new Int16Array(ori.length + e.data.audioBuffer.length);
                    this.audioBuffer.set(ori);
                    this.audioBuffer.set(e.data.audioBuffer, ori.length);
                }
            };

            const sourceNode = this.audioContext.createMediaStreamSource(this.stream);
            sourceNode.connect(processNode).connect(this.audioContext.destination);

            this.recording = true;

        } catch (error) {
            this.onError({ error: error });
            this.stop();
            return;
        }
        this.onStart();
    }

    async stop() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => { track.stop(); });
            this.stream = null;
        }
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        this.recording = false;

        this.onStop();
    }

    isRecording() {
        return this.recording;
    }

    getByteArray() {
        var uint8Array = new Uint8Array(this.audioBuffer.buffer, this.audioBuffer.byteOffset, this.audioBuffer.byteLength);
        return uint8Array;
    }

    getBase64String() {
        var binary = '';
        var uint8Array = new Uint8Array(this.audioBuffer.buffer, this.audioBuffer.byteOffset, this.audioBuffer.byteLength);
        for (var i = 0; i < uint8Array.byteLength; i++) {
            binary += String.fromCharCode(uint8Array[i]);
        }
        return window.btoa(binary);
    }

    onStart() {
        // Default Functionality: Do Nothing
    }

    onStop() {
        // Default Functionality: Do Nothing
    }
    onError(event: any) {
        // Default Functionality: Do Nothing
    }
};

export class MomentumCloudApplication {

    private _pixelStreaming : PixelStreaming;
    private isConnected = false;

    private speechRecognition: any = null;
    private isRecognizing = false;
    private final_transcript = '';
    private micRecorderInterface = new micRecorder();

    private receivedFrameWidth = 0;
    private receivedFrameHeight = 0;
    private joystickIsDragging = false;
    private joystickOffset = { x: 0, y: 0 };

    private timecodeRecording = false;
    private timecodeRecordingStartTime: any = null;
    private timecodeRecordingContent = '';
    private timecodeAudioStartCommandQueue: string[];
    private timecodeAudioEndCommandQueue: string[];
    private timecodeTimeoutFirstId = -1;

    private ts_eval = { run : (str: string) => { eval(str); }};

    // Leap Motion properties
    private leapController: any = null;
    private leapEnabled = false;
    private leapControlMode = 'gesture'; // 'gesture', 'position', 'animation'
    private leapSensitivity = 1.0;
    private lastHandCount = 0;
    private lastGesture = 'None';
    private gestureThreshold = 0.8;

    // Swipe gesture detection
    private handPositionHistory: Array<{x: number, y: number, z: number, timestamp: number}> = [];
    private maxHistoryLength = 15; // 增加历史记录长度
    private swipeThreshold = 50; // 降低最小滑动距离（原100->50）
    private swipeTimeWindow = 1500; // 增加时间窗口（原1000->1500ms）
    private lastSwipeTime = 0;
    private swipeCooldown = 1000; // 减少冷却时间（原2000->1000ms）
    private outfitSwipeEnabled = true;

    // 音频和视频同步播放相关属性
    private savedAudioVolume: string = '1.0'; // 保存的音量值
    private isPlayingVideoWithAudio = false; // 是否正在播放视频+音频
    private currentVideoWindow: Window | null = null; // 当前视频窗口引用

    constructor (pixelStreaming: PixelStreaming) {
        console.log('🚀 MomentumCloudApplication构造函数开始');
        this._pixelStreaming = pixelStreaming;
        this.registerCallbacks();

        // Setup HTML events with error handling
        try {
            this.setupHtmlEvents();
        } catch (error) {
            console.error('❌ setupHtmlEvents failed:', error);
        }

        this.setupChatHistoryOverlay();
        console.log('📢 即将调用setupSpeechRecognition');
        this.setupSpeechRecognition();
        this.setupTTSTimeCodeEditor();
        this.setupVideoPlayer();
        this.setupVideoWindowCommunication();

        // Setup Leap Motion with error handling
        try {
            console.log('🖐️ 开始设置 Leap Motion...');
            this.setupLeapMotion();
        } catch (error) {
            console.warn('❌ Leap Motion setup failed:', error);
        }

        // Setup Voice Assistant with error handling
        try {
            this.setupVoiceAssistant();
        } catch (error) {
            console.warn('❌ Voice Assistant setup failed:', error);
        }

        console.log('✅ MomentumCloudApplication构造函数完成');
    }

    public get pixelStreaming() {
        return this._pixelStreaming;
    }

    // ui-library/src/Util/MathUtils
    formatBytes(bytes: number, decimals: number): string {
        if (bytes === 0) {
            return '0';
        }

        const factor = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = [
            'Bytes',
            'KiB',
            'MiB',
            'GiB',
            'TiB',
            'PiB',
            'EiB',
            'ZiB',
            'YiB'
        ];

        const i = Math.floor(Math.log(bytes) / Math.log(factor));

        return (
            parseFloat((bytes / Math.pow(factor, i)).toFixed(dm)) +
            ' ' +
            sizes[i]
        );
    }

    emitCommand(descriptor: object) {
        this._pixelStreaming.emitCommand(descriptor);
    }

    emitUIInteraction(descriptor: object | string) {
        try {
            if (this.timecodeRecording) {
                let duration = (new Date()).getTime() - this.timecodeRecordingStartTime;
                let hours   = Math.floor((duration / (1000 * 60 * 60)) % 24);
                let minutes = Math.floor((duration / (1000 * 60)) % 60);
                let seconds = Math.floor((duration / 1000) % 60);
                let milliseconds = Math.floor((duration % 1000) / 100);
                let str_time = ((hours < 10) ? '0' + hours : hours) + ':'
                            + ((minutes < 10) ? '0' + minutes : minutes) + ':'
                            + ((seconds < 10) ? '0' + seconds : seconds) + '.'
                            + ((milliseconds < 10) ? '00' + milliseconds : (milliseconds < 100) ? '0' + milliseconds : milliseconds);
                this.timecodeRecordingContent += str_time + ',emitUIInteraction(' + JSON.stringify(descriptor) + ');' + '\r\n';
            }
        } catch (e) {
            console.log(e);
        }
        this._pixelStreaming.emitUIInteraction(descriptor);
    }

    registerCallbacks() {
        this._pixelStreaming.addEventListener(
            'webRtcConnected',
            () => this.onWebRtcConnected()
        );
        this._pixelStreaming.addEventListener(
            'webRtcDisconnected',
            () => this.onWebRtcDisconnected()
        );
        this._pixelStreaming.addEventListener(
            'videoInitialized',
            () => this.onVideoInitialized()
        );
        this._pixelStreaming.addEventListener(
            'playerCount',
            ({ data: { count }}) => this.onPlayerCount(count)
        );
        this._pixelStreaming.addEventListener(
            'statsReceived',
            ({ data: { aggregatedStats }}) => this.onStatsReceived(aggregatedStats)
        );
        this._pixelStreaming.addEventListener(
            'latencyTestResult',
            ({ data: { latencyTimings }}) => this.onLatencyTestResults(latencyTimings)
        );
        this._pixelStreaming.addEventListener(
            'initialSettings',
            ({ data: { settings }}) => this.onInitialSettings(settings)
        );
    }

    onWebRtcConnected() {
        this.isConnected = true;
    }

    onWebRtcDisconnected() {
        this.isConnected = false;
        let statsResult = document.getElementById('statsResult');
        if (statsResult !== null) {
            statsResult.innerHTML = 'Not Connected';
        }
    }

    onVideoInitialized() {
        this.requestStreamerResponse();
    }

    onPlayerCount(count: number) {
        let kickButton = document.getElementById('kick-other-players-button') as HTMLInputElement;
        if (kickButton) {
            kickButton.value = `Kick (${count - 1})`;
        }
    }

    onStatsReceived(stats: AggregatedStats) {
        // record resolution
        if (Object.prototype.hasOwnProperty.call(stats.inboundVideoStats, 'frameWidth')) {
            this.receivedFrameWidth = stats.inboundVideoStats.frameWidth;
        }
        if (Object.prototype.hasOwnProperty.call(stats.inboundVideoStats, 'frameHeight')) {
            this.receivedFrameHeight = stats.inboundVideoStats.frameHeight;
        }

        let statsResultInnerHTML = '';

        const orangeQP = 26;
        const redQP = 35;
        const VideoEncoderQP = stats.sessionStats.videoEncoderAvgQP;
        if (VideoEncoderQP > redQP) {
            statsResultInnerHTML += '<div style="color: red">Poor Encoding Quality</div>';
        } else if (VideoEncoderQP > orangeQP) {
            statsResultInnerHTML += '<div style="color: orange">Blocky Encoding Quality</div>';
        } else {
            statsResultInnerHTML += '<div style="color: lime">Clear Encoding Quality</div>';
        }

        // format numbering based on the browser language
        const numberFormat = new Intl.NumberFormat(window.navigator.language, {maximumFractionDigits: 0});

        // Inbound data
        const inboundData = this.formatBytes(stats.inboundVideoStats.bytesReceived, 2);
        statsResultInnerHTML += `<div>Received: ${inboundData}</div>`;

        // Packets lost
        const packetsLostStat = Object.prototype.hasOwnProperty.call(stats.inboundVideoStats, 'packetsLost') ?
            numberFormat.format(stats.inboundVideoStats.packetsLost) : 'Chrome only';
        statsResultInnerHTML += `<div>Packets Lost: ${packetsLostStat}</div>`;

        // Bitrate
        if (stats.inboundVideoStats.bitrate) {
            statsResultInnerHTML += `<div>Video Bitrate (kbps): ${stats.inboundVideoStats.bitrate.toString()}</div>`;
        }

        if (stats.inboundAudioStats.bitrate) {
            statsResultInnerHTML += `<div>Audio Bitrate (kbps): ${stats.inboundAudioStats.bitrate.toString()}</div>`;
        }

        // Video resolution
        const resStat =
            Object.prototype.hasOwnProperty.call(stats.inboundVideoStats, 'frameWidth') &&
            stats.inboundVideoStats.frameWidth &&
            Object.prototype.hasOwnProperty.call(stats.inboundVideoStats, 'frameHeight') &&
            stats.inboundVideoStats.frameHeight ?
            stats.inboundVideoStats.frameWidth + 'x' + stats.inboundVideoStats.frameHeight : 'Chrome only';
        statsResultInnerHTML += `<div>Video resolution: ${resStat}</div>`;

        // Frames decoded
        const framesDecoded = Object.prototype.hasOwnProperty.call(stats.inboundVideoStats, 'framesDecoded') ?
            numberFormat.format(stats.inboundVideoStats.framesDecoded) : 'Chrome only';
        statsResultInnerHTML += `<div>Frames Decoded: ${framesDecoded}</div>`;

        // Framerate
        if (stats.inboundVideoStats.framesPerSecond) {
            statsResultInnerHTML += `<div>Framerate: ${stats.inboundVideoStats.framesPerSecond.toString()}</div>`;
        }

        // Frames dropped
        if (stats.inboundVideoStats.framesDropped) {
            statsResultInnerHTML += `<div>Frames dropped: ${stats.inboundVideoStats.framesDropped.toString()}</div>`;
        }

        if (stats.inboundVideoStats.codecId) {
            // Split the codec to remove the Fmtp line
            const codecId = stats.codecs.get(stats.inboundVideoStats.codecId)?.split(' ')[0] ?? ''
            statsResultInnerHTML += `<div>Video codec: ${codecId}</div>`;
        }

        if (stats.inboundAudioStats.codecId) {
            // Split the codec to remove the Fmtp line
            const codecId = stats.codecs.get(stats.inboundAudioStats.codecId)?.split(' ')[0] ?? ''
            statsResultInnerHTML += `<div>Audio codec: ${codecId}</div>`;
        }

        // Store the active candidate pair return a new Candidate pair stat if getActiveCandidate is null
        let activeCandidatePair = stats.getActiveCandidatePair() != null ? stats.getActiveCandidatePair() : new CandidatePairStats();

        // RTT
        const netRTT =
            Object.prototype.hasOwnProperty.call(activeCandidatePair, 'currentRoundTripTime') && stats.isNumber(activeCandidatePair.currentRoundTripTime) ?
            numberFormat.format(activeCandidatePair.currentRoundTripTime * 1000) : "Can't calculate";
        statsResultInnerHTML += `<div>Net RTT (ms): ${netRTT}</div>`;

        statsResultInnerHTML += `<div>Duration: ${stats.sessionStats.runTime}</div>`;

        statsResultInnerHTML += `<div>Controls stream input: ${stats.sessionStats.controlsStreamInput}</div>`;

        // QP
        statsResultInnerHTML += `<div>Video quantization parameter: ${stats.sessionStats.videoEncoderAvgQP.toString()}</div>`;

        // Show server IP
        try {
            let turnURL = this._pixelStreaming.webRtcController.peerConfig.iceServers[0].urls[1];
            let serverIP = turnURL.split(':')[1].substring(0);
            statsResultInnerHTML += `<div>Public Server IP: ${serverIP}</div>`;
        } catch (e) {
            console.log(e);
        }

        let statsResult = document.getElementById('statsResult');
        if (statsResult !== null) {
            statsResult.innerHTML = statsResultInnerHTML;
        }
    }

    onLatencyTestResults(latencyTimings: LatencyTestResults) {
        let latencyStatsInnerHTML = '';
        latencyStatsInnerHTML += `<div>Net latency RTT (ms): ${latencyTimings.networkLatency}</div>`;
        latencyStatsInnerHTML += `<div>UE Encode (ms): ${latencyTimings.EncodeMs}</div>`;
        latencyStatsInnerHTML += `<div>UE Capture (ms): ${latencyTimings.CaptureToSendMs}</div>`;
        latencyStatsInnerHTML += `<div>Browser send latency (ms): ${latencyTimings.browserSendLatency}</div>`;
        latencyStatsInnerHTML += latencyTimings.frameDisplayDeltaTimeMs && latencyTimings.browserReceiptTimeMs ?
        `<div>Browser receive latency (ms): ${latencyTimings.frameDisplayDeltaTimeMs}</div>` : '';
        latencyStatsInnerHTML += `<div>Total latency (excluding browser) (ms): ${latencyTimings.latencyExcludingDecode}</div>`;
        latencyStatsInnerHTML += latencyTimings.endToEndLatency ? `<div>Total latency (ms): ${latencyTimings.endToEndLatency}</div>` : '';

        let latencyStatsResult = document.getElementById('latencyStatsResult');
        if (latencyStatsResult !== null) {
            latencyStatsResult.innerHTML = latencyStatsInnerHTML;
        }
    }

    onInitialSettings(settings: InitialSettings) {
        if (settings.EncoderSettings) {

            let minQP = document.getElementById('encoder-min-qp-text') as HTMLInputElement;
            if (minQP !== null) {
                minQP.value = settings.EncoderSettings.MinQP.toString();
            }
            let maxQP = document.getElementById('encoder-max-qp-text') as HTMLInputElement;
            if (maxQP !== null) {
                maxQP.value = settings.EncoderSettings.MaxQP.toString();
            }
        }
        if (settings.WebRTCSettings) {
            let fps = document.getElementById('webrtc-fps-text') as HTMLInputElement;
            if (fps !== null) {
                fps.value = settings.WebRTCSettings.FPS.toString();
            }
            let minBitrate = document.getElementById('webrtc-min-bitrate-text') as HTMLInputElement;
            if (minBitrate !== null) {
                minBitrate.value = (settings.WebRTCSettings.MinBitrate / 1000).toString(); /* bps to kbps */;
            }
            let maxBitrate = document.getElementById('webrtc-max-bitrate-text') as HTMLInputElement;
            if (maxBitrate !== null) {
                maxBitrate.value = (settings.WebRTCSettings.MaxBitrate / 1000).toString(); /* bps to kbps */;
            }
        }
    }

    requestLatencyTest() {
        this._pixelStreaming.requestLatencyTest();
    }

    playAudio(url: string | URL) {
        this._pixelStreaming.webRtcController?.peerConnectionController?.playAudio(url);
    }

    stopAudio() {
        this._pixelStreaming.webRtcController?.peerConnectionController?.stopAudio();
    }

    startMic(callback: (success: boolean) => void) {
        this._pixelStreaming.webRtcController?.peerConnectionController?.startMic(callback);
    }

    stopMic() {
        this._pixelStreaming.webRtcController?.peerConnectionController?.stopMic();
    }

    setupHtmlEvents() {
        console.log('🔧 开始设置HTML事件...');

        // 添加安全的事件绑定函数
        const safeBindEvent = (elementId: string, eventType: string, handler: (event: Event) => void) => {
            try {
                const element = document.getElementById(elementId);
                if (element !== null) {
                    (element as any)[eventType] = handler;
                } else {
                    console.warn(`⚠️ 元素 ${elementId} 不存在，跳过 ${eventType} 事件绑定`);
                }
            } catch (error) {
                console.error(`❌ 绑定元素 ${elementId} 的 ${eventType} 事件时出错:`, error);
            }
        };

        // 添加全局错误处理包装器
        const safeExecute = (description: string, fn: () => void) => {
            try {
                fn();
            } catch (error) {
                console.error(`❌ ${description} 时出错:`, error);
                // 继续执行，不中断整个初始化过程
            }
        };

        // ToClient response
        this._pixelStreaming.addResponseEventListener('mcloud_response', (data:string) => this.mcloudResponse(data));

        // Window events
        window.addEventListener('beforeunload', () => this.beforeunload());

        // joystick events
        let joystickHandle = document.getElementById('joystickHandle');
        if (joystickHandle !== null) {
            joystickHandle.addEventListener('mousedown', (event) => this.joystickStartDrag(event));
            document.addEventListener('mousemove', (event) => this.joystickDragging(event));
            document.addEventListener('mouseup', (event) => this.joystickEndDrag(event));
        }

        // HTML elements controls
        document.addEventListener('focus', (event) => {
            if (this._pixelStreaming.config.isFlagEnabled(Flags.KeyboardInput)) {
                // Disable KeyboardInput. Keep focus on current element, and don't send keyboard events to streamer.
                let stopPropagation = (event: KeyboardEvent) => { event.stopPropagation(); };
                document.addEventListener('keydown',  stopPropagation, true);
                document.addEventListener('keyup',    stopPropagation, true);
                document.addEventListener('keypress', stopPropagation, true);

                // Enable KeyboardInput when focus out
                document.addEventListener('blur', (event) => {
                    document.removeEventListener('keydown',  stopPropagation, true);
                    document.removeEventListener('keyup',    stopPropagation, true);
                    document.removeEventListener('keypress', stopPropagation, true);
                }, { capture: true, once: true }); // run once
            }
        }, true);

        let collElements = document.getElementsByClassName('collapsible');
        Array.prototype.forEach.call(collElements, function(element: Element) {
            element.addEventListener('click', function() {
                element.classList.toggle('collapsible-active');
                let content = element.nextElementSibling;
                if (content !== null) {
                    content.classList.toggle('content-active');
                }
            });
        });
        let toggleButtons = document.querySelectorAll('.toggle-button');
        toggleButtons.forEach((element: Element) => {
            element.addEventListener('click', function(event: Event) {
                let target = event.target as HTMLElement;
                if (target.classList.contains('toggle-button-group')) {
                    let buttons = target.querySelectorAll('.toggle-button');
                    for (let i: number = 0; i < buttons.length; i++) {
                        if (buttons[i] != event.target) {
                            buttons[i].classList.remove('toggle-button-active');
                        }
                    }
                }
                target.classList.toggle('toggle-button-active');
            });
        });

        let capContainer = document.getElementById('caption-container');
        if (capContainer !== null) {
            let audiosourceTabEls = document.querySelectorAll('button[data-bs-target^="#audiosource-"]');
            audiosourceTabEls.forEach(function(item) {
                item.addEventListener('click', function(event) {
                    if (item.getAttribute('data-bs-target') == '#audiosource-stream-container') {
                        capContainer.style.display = 'none';
                    } else {
                        capContainer.style.display = 'block';
                    }
                });
            });
        }

        let numInputs = document.querySelectorAll('input[type=number]');
        numInputs.forEach(function(item: HTMLInputElement) {
            item.addEventListener('change', function(event) {
                if (!isNaN(Number(item.max)) && !isNaN(Number(item.min))) {
                    let value = parseFloat(item.value);
                    let max = parseFloat(item.max);
                    let min = parseFloat(item.min);
                    if (value > max) item.value = item.max;
                    if (value < min) item.value = item.min;
                }
            });
        });

        let showFPSCheckBox = document.getElementById('show-fps-tgl');
        if (showFPSCheckBox !== null) {
            showFPSCheckBox.onchange = (event: Event) => {
                this._pixelStreaming.requestShowFps();
            };
        }

        let statsCheckBox = document.getElementById('show-stats-tgl') as HTMLInputElement;
        if (statsCheckBox !== null) {
            statsCheckBox.onchange = (event: Event) => {
                document.getElementById('statsContainer').style.display = statsCheckBox.checked ? 'block' : 'none';
            };
        }

        let minQPInput = document.getElementById('encoder-min-qp-text') as HTMLInputElement;
        if (minQPInput !== null) {
            minQPInput.onchange = (event: Event) => {
                this._pixelStreaming.webRtcController?.sendEncoderMinQP(Number(minQPInput.value));
            };
        }

        let maxQPInput = document.getElementById('encoder-max-qp-text') as HTMLInputElement;
        if (maxQPInput !== null) {
            maxQPInput.onchange = (event: Event) => {
                this._pixelStreaming.webRtcController?.sendEncoderMaxQP(Number(maxQPInput.value));
            };
        }

        let maxFPSInput = document.getElementById('webrtc-fps-text') as HTMLInputElement;
        if (maxFPSInput !== null) {
            maxFPSInput.onchange = (event: Event) => {
                this._pixelStreaming.webRtcController?.sendWebRTCFps(Number(maxFPSInput.value));
            };
        }

        let minBitrate = document.getElementById('webrtc-min-bitrate-text') as HTMLInputElement;
        if (minBitrate !== null) {
            minBitrate.onchange = (event: Event) => {
                this._pixelStreaming.webRtcController?.sendWebRTCMinBitrate(Number(minBitrate.value) * 1000); // kbps to bps
            };
        }

        let maxBitrate = document.getElementById('webrtc-max-bitrate-text') as HTMLInputElement;
        if (maxBitrate !== null) {
            maxBitrate.onchange = (event: Event) => {
                this._pixelStreaming.webRtcController?.sendWebRTCMaxBitrate(Number(maxBitrate.value) * 1000); // kbps to bps
            };
        }

        let latencyButton = document.getElementById('test-latency-button');
        if (latencyButton) {
            latencyButton.onclick = (event: Event) => {
                this.requestLatencyTest();
            };
        }

        let kickButton = document.getElementById('kick-other-players-button');
        if (kickButton) {
            kickButton.onclick = (event: Event) => {
                this._pixelStreaming.webSocketController?.webSocket?.send(JSON.stringify({type: 'kick'}));
            };
        }

        /*******************************************************************************
         * Script
         ******************************************************************************/
        let startDemoButton = document.getElementById('start-demo-button');
        if (startDemoButton !== null) {
            startDemoButton.onclick = (event: Event) => {
                /*******************************************************************************
                 * [Demo Script]
                 * This demo script is to show the working of Momentum Cloud. You can also refer to the documents
                 * "Development Guide.pdf" and "Demo Story.pdf" to clarify the design and structure of this demo script.
                 * We welcome you to modify this script to the scenario you need. You are also welcome to share feedback
                 * that could help us optimize our service in the future. You could send the suggestions to the email "<EMAIL>."
                 * We look forward to hearing from you, also a successful working relationship in the future.
                 ******************************************************************************/
                let ttsEngineControl = document.getElementById('tts-engine-control') as HTMLSelectElement;
                let ttsVoiceControl  = document.getElementById('tts-voice-control') as HTMLSelectElement;
                if (ttsEngineControl !== null && ttsVoiceControl !== null) {
                    if (ttsEngineControl.value != TTS_ENGINE_ID.AzureSpeech.toString()) {
                        ttsEngineControl.value = TTS_ENGINE_ID.AzureSpeech.toString();
                        ttsEngineControl.onchange(null);
                    }

                    // Check voices list and then start demo script
                    let checkTtsTimes = 0;
                    let checkTtsIntervalId = window.setInterval(() => {
                        let voiceIdList: string[] = [];
                        for (var i = 0; i < ttsVoiceControl.options.length; i++) {
                            voiceIdList.push(ttsVoiceControl.options[i].value);
                        }
                        if (voiceIdList.indexOf('en-US-AvaMultilingualNeural') != -1 && voiceIdList.indexOf('en-US-RyanMultilingualNeural') != -1) {
                            window.clearInterval(checkTtsIntervalId);
                            window.setTimeout(() => { this.timecodePlay('/timecode/demoscript.csv'); }, 1000);
                        }
                        if (++checkTtsTimes < 5) {
                            return;
                        }
                        alert('Please activate AzureSpeech license before play demo script.');
                        window.clearInterval(checkTtsIntervalId);
                    }, 1000);
                }
            };
        }

        let stopDemoButton = document.getElementById('stop-demo-button');
        if (stopDemoButton !== null) {
            stopDemoButton.onclick = (event: Event) => {
                this.timecodeClear();
            };
        }

        let timecodeFile = document.getElementById('timecode-file') as HTMLInputElement;
        let timecodeFileText = document.getElementById('timecode-file-text');
        let timecodeFileButton = document.getElementById('timecode-file-button');
        if (timecodeFile !== null && timecodeFileText !== null && timecodeFileButton !== null) {
            timecodeFile.onchange = (event: Event) => {
                timecodeFileText.innerHTML = timecodeFile.value.split(/(\\|\/)/g).pop();
            };

            timecodeFileButton.onclick = (event: Event) => {
                timecodeFile.click();
            };
        }
        /****MRQ Start***/
        let MRQtimecodeFile = document.getElementById('MRQtimecode-file') as HTMLInputElement;
        let MRQtimecodeFileText = document.getElementById('MRQtimecode-file-text');
        let MRQtimecodeFileButton = document.getElementById('MRQtimecode-file-button');
        if (MRQtimecodeFile !== null && MRQtimecodeFileText !== null && MRQtimecodeFileButton !== null) {
            MRQtimecodeFile.onchange = (event: Event) => {
                MRQtimecodeFileText.innerHTML = MRQtimecodeFile.value.split(/(\\|\/)/g).pop();
            };

            MRQtimecodeFileButton.onclick = (event: Event) => {
                MRQtimecodeFile.click();
            };
        }
        /****MRQ End***/
        let timecodePlayButton = document.getElementById('timecode-play-button');
        if (timecodePlayButton !== null) {
            timecodePlayButton.onclick = (event: Event) => {
                let timecodeFile = document.getElementById('timecode-file') as HTMLInputElement;
                if (timecodeFile !== null && timecodeFile.files.length) {
                    let objectURL = URL.createObjectURL(timecodeFile.files[0]);
                    this.timecodePlay(objectURL);
                    URL.revokeObjectURL(objectURL);
                }
            };
        }

        let timecodeStopButton = document.getElementById('timecode-stop-button');
        if (timecodeStopButton !== null) {
            timecodeStopButton.onclick = (event: Event) => {
                this.timecodeClear();
            };
        }

        let timecodeToggleRecordingButton = document.getElementById('timecode-toggle-recording-button') as HTMLButtonElement;
        if (timecodeToggleRecordingButton !== null) {
            timecodeToggleRecordingButton.onclick = (event: Event) => {
                if (this.timecodeRecording) {
                    let data = new Blob([this.timecodeRecordingContent], {type: 'text/plain'});
                    let link = document.createElement('a');
                    link.setAttribute('id', 'timecodeRecordingDownload');
                    link.setAttribute('href', URL.createObjectURL(data));
                    link.setAttribute('download', 'TimecodeRecords.csv');
                    link.innerHTML = 'Download';
                    timecodeToggleRecordingButton.parentElement.appendChild(link);

                    this.timecodeRecordingStartTime = null;
                    this.timecodeRecordingContent = '';
                    timecodeToggleRecordingButton.value = 'Start Recording';
                } else {
                    let link = document.getElementById('timecodeRecordingDownload');
                    if (link !== null) {
                        link.remove();
                    }
                    this.timecodeRecordingStartTime = (new Date()).getTime();
                    timecodeToggleRecordingButton.value = 'Stop Recording';
                }
                this.timecodeRecording = !this.timecodeRecording;
            };
        }

        /*******************************************************************************
         * Character
         ******************************************************************************/
        let switchCharacterButtonResetCloth = document.getElementById('switch-character-button-resetcloth');
        if (switchCharacterButtonResetCloth !== null) {
            switchCharacterButtonResetCloth.onclick = (event: Event) => {
                this.emitUIInteraction({ ClothReset: '1' });
            };
        }

        let characterSelect = document.getElementById('character-select') as HTMLSelectElement;
        if (characterSelect !== null) {
            characterSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Character: { id: characterSelect.value }});

                let metahumanOptions = document.getElementById('metahuman-options');
                if (metahumanOptions !== null) {
                    metahumanOptions.style.display = characterSelect.value === '314' ? 'block' : 'none';
                }
            };
        }

        let vrmFile = document.getElementById('vrm-file') as HTMLInputElement;
        let vrmFileText = document.getElementById('vrm-text');
        let vrmFileButton = document.getElementById('vrm-button');
        let vrmFileUploadButton = document.getElementById('vrm-file-upload');
        let vrmMatSelect = document.getElementById('vrm-mat-select') as HTMLSelectElement;
        if (vrmFile !== null && vrmFileButton !== null && vrmFileUploadButton !== null) {
            vrmFile.onchange = (event: Event) => {
                vrmFileText.innerHTML = vrmFile.value.split(/(\\|\/)/g).pop();
            };

            vrmFileButton.onclick = (event: Event) => {
                vrmFile.click();
            };

            vrmFileUploadButton.onclick = (event: Event) => {
                if (vrmFile.files.length > 0) {
                    let file = vrmFile.files[0];
                    try {
                        let formData = new FormData();
                        formData.append('vrmFile', file);

                        let xhr = new XMLHttpRequest();
                        xhr.open('POST', '/upload/vrm', true);
                        xhr.onreadystatechange = ((request: XMLHttpRequest, event: Event): any => {
                            if (request.readyState === 4) {
                                if (request.status === 200) {
                                    alert('Upload Successful.');
                                    this.emitUIInteraction({ ImportVRM: { Path: vrmFileText.innerHTML, Material: vrmMatSelect.value }});
                                    this.requestStreamerResponse();
                                } else {
                                    alert('Upload Failure: ' + request.status + ' (' + request.statusText + ').');
                                }
                            }
                        }).bind(this, xhr);
                        xhr.send(formData);
                    } catch (error) {
                        console.log(error);
                    }
                }
            };
        }

        let vrmDeleteButton = document.getElementById('vrm-file-delete') as HTMLInputElement;
        let vrmDeleteSelect = document.getElementById('vrm-char-select') as HTMLSelectElement;
        if (vrmDeleteButton !== null && vrmDeleteSelect !== null) {
            vrmDeleteButton.onclick = (event: Event) => {
                this.emitUIInteraction({ DeleteVRM: { Name: vrmDeleteSelect.value }});
            }
        }

        /* Metahuman Start */
        let MHCharSelect = document.getElementById('MHChar-select') as HTMLSelectElement | null;
        if (MHCharSelect !== null) {
            MHCharSelect.onchange = (event: Event)=> {
                this.emitUIInteraction({ MHChar: { id: MHCharSelect.value }});
            };
        }

        let metahumanFile = document.getElementById('metahuman-file') as HTMLInputElement | null;
        let metahumanFileText = document.getElementById('metahuman-text') as HTMLElement | null;
        let metahumanFileButton = document.getElementById('metahuman-button') as HTMLButtonElement | null;
        let metahumanFileUploadButton = document.getElementById('metahuman-file-upload') as HTMLButtonElement | null;
        let metahumanFileImportButton = document.getElementById('metahuman-file-import') as HTMLButtonElement | null;

        if (
            metahumanFile &&
            metahumanFileText &&
            metahumanFileButton &&
            metahumanFileUploadButton &&
            metahumanFileImportButton
        ) {
            metahumanFile.onchange = (event: Event)=> {
                if (metahumanFile.files) {
                    metahumanFileText.innerHTML = metahumanFile.value.split(/(\\|\/)/g).pop() || '';
                }
            };

            metahumanFileButton.onclick = (event: Event)=> {
                metahumanFile.click();
            };

            metahumanFileUploadButton.onclick = (event: Event)=> {
                if (metahumanFile.files && metahumanFile.files.length > 0) {
                    let MHfile = metahumanFile.files[0];
                    try {
                        // Show progress container
                        let progressContainer = document.getElementById('progress-container') as HTMLElement | null;
                        let progressBar = document.getElementById('progress-bar') as HTMLElement | null;

                        if (progressContainer && progressBar) {
                            progressContainer.style.display = 'block';
                        }

                        let formData = new FormData();
                        formData.append('metahumanFile', MHfile);

                        let xhr = new XMLHttpRequest();
                        xhr.open('POST', '/upload/metahuman', true);

                        // Update progress bar
                        xhr.upload.onprogress = (event: ProgressEvent) => {
                            if (event.lengthComputable && progressBar) {
                                let percentComplete = Math.round((event.loaded / event.total) * 100);
                                progressBar.style.width = percentComplete + '%';
                                progressBar.textContent = percentComplete + '%';
                            }
                        };

                        xhr.onreadystatechange = (event: Event)=> {
                            if (xhr.readyState === 4) {
                                if (xhr.status === 200 && progressBar) {
                                    progressBar.style.width = '100%';
                                    progressBar.textContent = 'Upload Complete';
                                    setTimeout(() => {
                                        alert('Upload Successful.');
                                    }, 1000);

                                    this.requestStreamerResponse();
                                } else {
                                    alert('Upload Failure: ' + xhr.status + ' (' + xhr.statusText + ').');
                                }

                                // Hide progress container after a short delay
                                if (progressContainer && progressBar) {
                                    setTimeout(() => {
                                        progressContainer.style.display = 'none';
                                        progressBar.style.width = '0%';
                                    }, 1000);
                                }
                            }
                        };
                        xhr.send(formData);
                    } catch (error) {
                        console.log(error);
                    }
                }
            };

            let metahumanSelect_1 = document.getElementById('metahuman-select') as HTMLSelectElement | null;

            metahumanFileImportButton.onclick = (event: Event)=> {
                if (metahumanSelect_1) {
                    this.emitUIInteraction({ ImportMetahuman: { Path: metahumanSelect_1.value }});
                }
            };        }
        /* Metahuman End */

        /*******************************************************************************
         * HoloDisplay
         ******************************************************************************/
        let holoDisplayBackgroundDistance = document.getElementById('holodisplay-background-distance') as HTMLInputElement;
        if (holoDisplayBackgroundDistance !== null) {
            holoDisplayBackgroundDistance.onchange = (event: Event) => {
                this.emitUIInteraction({ Background: { distance: holoDisplayBackgroundDistance.value }});
            };
        }

        // HoloDisplay Camera Location
        let holoDisplayCameraLocationXNum = document.getElementById('holodisplay-camera-location-x') as HTMLInputElement;
        let holoDisplayCameraLocationYNum = document.getElementById('holodisplay-camera-location-y') as HTMLInputElement;
        let holoDisplayCameraLocationZNum = document.getElementById('holodisplay-camera-location-z') as HTMLInputElement;
        if (holoDisplayCameraLocationXNum !== null && holoDisplayCameraLocationYNum !== null && holoDisplayCameraLocationZNum !== null) {
            let holoDisplayCameraLocationChange = () => {
                this.emitUIInteraction({ HoloDisplay: { location: holoDisplayCameraLocationXNum.value + ',' + holoDisplayCameraLocationYNum.value + ',' + holoDisplayCameraLocationZNum.value }});
            };
            holoDisplayCameraLocationXNum.onchange = (event: Event) => {
                holoDisplayCameraLocationChange();
            };
            holoDisplayCameraLocationYNum.onchange = (event: Event) => {
                holoDisplayCameraLocationChange();
            };
            holoDisplayCameraLocationZNum.onchange = (event: Event) => {
                holoDisplayCameraLocationChange();
            };
        }

        // HoloDisplay Camera Rotation
        let holoDisplayCameraRotationPNum = document.getElementById('holodisplay-camera-rotation-p') as HTMLInputElement;
        let holoDisplayCameraRotationYNum = document.getElementById('holodisplay-camera-rotation-y') as HTMLInputElement;
        let holoDisplayCameraRotationRNum = document.getElementById('holodisplay-camera-rotation-r') as HTMLInputElement;
        if (holoDisplayCameraRotationPNum !== null && holoDisplayCameraRotationYNum !== null && holoDisplayCameraRotationRNum !== null) {
            let holoDisplayCameraRotationChange = () => {
                this.emitUIInteraction({ HoloDisplay: { rotation: holoDisplayCameraRotationPNum.value + ',' + holoDisplayCameraRotationYNum.value + ',' + holoDisplayCameraRotationRNum.value }});
            };
            holoDisplayCameraRotationPNum.onchange = (event: Event) => {
                holoDisplayCameraRotationChange();
            };
            holoDisplayCameraRotationYNum.onchange = (event: Event) => {
                holoDisplayCameraRotationChange();
            };
            holoDisplayCameraRotationRNum.onchange = (event: Event) => {
                holoDisplayCameraRotationChange();
            };
        }

        // HoloDisplay Camera Size
        let holoDisplayCameraSize = document.getElementById('holodisplay-camera-size') as HTMLInputElement;
        if (holoDisplayCameraSize !== null) {
            holoDisplayCameraSize.onchange = (event: Event) => {
                this.emitUIInteraction({ HoloDisplay: { cameraSize: holoDisplayCameraSize.value }});
            };
        }

        // HoloDisplay Camera FOV
        let holoDisplayCameraFov = document.getElementById('holodisplay-camera-fov') as HTMLInputElement;
        if (holoDisplayCameraFov !== null) {
            holoDisplayCameraFov.onchange = (event: Event) => {
                this.emitUIInteraction({ HoloDisplay: { fov: holoDisplayCameraFov.value }});
            };
        }

        let shadowEnable  = document.getElementById('shadow-switch') as HTMLInputElement;
        let shadowAngle   = document.getElementById('shadow-angle-text') as HTMLInputElement;
        let shadowLength  = document.getElementById('shadow-length-text') as HTMLInputElement;
        let shadowOpacity = document.getElementById('shadow-opacity-text') as HTMLInputElement;
        let shadowOffset  = document.getElementById('shadow-offset-text') as HTMLInputElement;
        let shadowBlur    = document.getElementById('shadow-blur-text') as HTMLInputElement;
        if (shadowEnable !== null && shadowAngle !== null && shadowLength !== null && shadowOpacity !== null && shadowOffset !== null && shadowBlur !== null){
            shadowEnable.onchange = (event: Event) => {
                this.emitUIInteraction({ FakeShadow: { enable: shadowEnable.checked ? 1 : 0 }});
            };

            shadowAngle.onchange = (event: Event) => {
                this.emitUIInteraction({ FakeShadow: { angle: shadowAngle.value }});
            };

            shadowLength.onchange = (event: Event) => {
                this.emitUIInteraction({ FakeShadow: { length: shadowLength.value }});
            };

            shadowOpacity.onchange = (event: Event) => {
                this.emitUIInteraction({ FakeShadow: { opacity: shadowOpacity.value }});
            };

            shadowOffset.onchange = (event: Event) => {
                this.emitUIInteraction({ FakeShadow: { offset: shadowOffset.value }});
            };

            shadowBlur.onchange = (event: Event) => {
                this.emitUIInteraction({ FakeShadow: { blur: shadowBlur.value }});
            };
        }

        /*******************************************************************************
         * Outfit
         ******************************************************************************/
        let outfitSelect = document.getElementById('outfit-select') as HTMLSelectElement;
        if (outfitSelect !== null) {
            outfitSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Outfit: { id: outfitSelect.value }});
            };

            let outfitDock = document.getElementById('outfit-dock');
            let outfitDockContainer = document.getElementById('outfit-dock-container');
            if (outfitDock !== null && outfitDockContainer !== null) {
                outfitDock.onclick = (event: Event) => {
                    if (!this.isConnected) {
                        return;
                    }

                    let selectedText = outfitSelect.options[outfitSelect.selectedIndex].text;
                    let selectedValue = outfitSelect.options[outfitSelect.selectedIndex].value;
                    if (Array.prototype.some.call(outfitDockContainer.childNodes, (child: HTMLDivElement) => child.id == selectedText)) {
                        return;
                    }

                    let div = document.createElement('div');
                    div.setAttribute('style', 'display:inline-block; margin:0px 5px 5px 0px;');
                    div.id = selectedText;

                    let button = document.createElement('button');
                    button.classList.add('btn', 'btn-primary', 'btn-sm');
                    button.innerHTML = selectedText;
                    button.onclick = (event: Event) => { this.emitUIInteraction({ Outfit: { id: selectedValue }}); };

                    let delButton = document.createElement('button');
                    delButton.classList.add('btn-close', 'btn-close-white');
                    delButton.setAttribute('style', 'padding: 0em;');
                    delButton.onclick = (event: Event) => { div.remove(); };

                    div.appendChild(button);
                    div.appendChild(delButton);
                    outfitDockContainer.appendChild(div);
                };
            }
        }

        let avatarColorSelect = document.getElementById('avatarColor-select') as HTMLSelectElement;
        let avatarColorPick = document.getElementById('avatarColor-color') as HTMLInputElement;
        if (avatarColorSelect !== null && avatarColorPick !== null) {
            avatarColorPick.oninput = (event: Event) => {
                let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(avatarColorPick.value);
                let colorR = parseInt(result[1], 16);
                let colorG = parseInt(result[2], 16);
                let colorB = parseInt(result[3], 16);
                this.emitUIInteraction({ AvatarColor: { id: avatarColorSelect.value, color: colorR + ',' + colorG + ',' + colorB }});
            };
        }

        let avatarColorResetButton = document.getElementById('avatarColor-reset');
        if (avatarColorResetButton !== null) {
            avatarColorResetButton.onclick = (event: Event) => {
                this.emitUIInteraction({ AvatarColor: { reset: '' }});
            };
        }

        let forwardLockSwitch = document.getElementById('mvn-forwardLock-switch') as HTMLInputElement;
        let forwardCap = document.getElementById('forward-axis-limit') as HTMLInputElement;
        if (forwardLockSwitch !== null) {
            forwardLockSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ Character: {lockForward: forwardLockSwitch.checked ? 1 : 0 }});
            };
        }
        if (forwardCap !== null) {
            forwardCap.onchange = (event: Event) => {
                this.emitUIInteraction({ Character: {forwardLimit: forwardCap.value}});
            };
        }


        let compatibilityCheckSwitch = document.getElementById('compatibilityCheck-switch') as HTMLInputElement;
        if (compatibilityCheckSwitch !== null) {
            compatibilityCheckSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ ModularCharacterPart: { Compatibility: compatibilityCheckSwitch.checked ? 1 : 0 }});
            };
        }

        let customOutfitHeadSelect  = document.getElementById('custom-outfit-head-select') as HTMLSelectElement;
        let customOutfitHairSelect  = document.getElementById('custom-outfit-hair-select') as HTMLSelectElement;
        let customOutfitUpperSelect = document.getElementById('custom-outfit-upper-select') as HTMLSelectElement;
        let customOutfitLowerSelect = document.getElementById('custom-outfit-lower-select') as HTMLSelectElement;
        let customOutfitFeetSelect  = document.getElementById('custom-outfit-feet-select') as HTMLSelectElement;
        let customOutfitAccSelect   = document.getElementById('custom-outfit-acc-select') as HTMLSelectElement;
        if (customOutfitHeadSelect !== null && customOutfitHeadSelect !== null && customOutfitUpperSelect !== null &&
            customOutfitLowerSelect !== null && customOutfitFeetSelect !== null && customOutfitAccSelect !== null) {
            customOutfitHeadSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ ModularCharacterPart: { id: customOutfitHeadSelect.value }});
            };

            customOutfitHairSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ ModularCharacterPart: { id: customOutfitHairSelect.value }});
            };

            customOutfitUpperSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ ModularCharacterPart: { id: customOutfitUpperSelect.value }});
            };

            customOutfitLowerSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ ModularCharacterPart: { id: customOutfitLowerSelect.value }});
            };

            customOutfitFeetSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ ModularCharacterPart: { id: customOutfitFeetSelect.value }});
            };

            customOutfitAccSelect.onchange = (event: Event) => {
                let selectedOptions = '';
                for (let i = 0; i < customOutfitAccSelect.options.length; i++) {
                    if (customOutfitAccSelect.options[i].selected) {
                        selectedOptions += customOutfitAccSelect.options[i].value + '/';
                    }
                }
                this.emitUIInteraction({ ModularCharacterPart: { id: selectedOptions }});
            };

            let modularInfoCopy = document.getElementById('modular-info-copy');
            modularInfoCopy.onclick = (event: Event) => {
                let clipText = '';
                if (customOutfitHeadSelect.value != 'none') {
                    clipText += `emitUIInteraction({ ModularCharacterPart: { id: '${customOutfitHeadSelect.value}' }});\r\n`;
                }
                if (customOutfitHairSelect.value != 'none') {
                    clipText += `emitUIInteraction({ ModularCharacterPart: { id: '${customOutfitHairSelect.value}' }});\r\n`;
                }
                if (customOutfitUpperSelect.value != 'none') {
                    clipText += `emitUIInteraction({ ModularCharacterPart: { id: '${customOutfitUpperSelect.value}' }});\r\n`;
                }
                if (customOutfitLowerSelect.value != 'none') {
                    clipText += `emitUIInteraction({ ModularCharacterPart: { id: '${customOutfitLowerSelect.value}' }});\r\n`;
                }
                if (customOutfitFeetSelect.value != 'none') {
                    clipText += `emitUIInteraction({ ModularCharacterPart: { id: '${customOutfitFeetSelect.value}' }});\r\n`;
                }
                if (customOutfitAccSelect.value != 'none') {
                    let selectedOptions = '';
                    for (let i = 0; i < customOutfitAccSelect.options.length; i++) {
                        if (customOutfitAccSelect.options[i].selected) {
                            selectedOptions += customOutfitAccSelect.options[i].value + '/';
                        }
                    }
                    if (selectedOptions.length > 0) {
                        clipText += `emitUIInteraction({ ModularCharacterPart: { id: '${selectedOptions}' }});\r\n`;
                    }
                }
                navigator.clipboard.writeText(clipText).then(() => {
                    console.log('Outfit info copied to clipboard');
                }).catch((err) => {
                    console.log(`Failed to copy Outfit info to clipboard. ${err}`);
                });
            };
        }

        let customOutfitPresetFilename = document.getElementById('custom-outfit-preset-filename-text') as HTMLInputElement;
        let customOutfitPresetSave = document.getElementById('custom-outfit-preset-save');
        if (customOutfitPresetFilename !== null && customOutfitPresetSave !== null) {
            customOutfitPresetSave.onclick = (event: Event) => {
                let filename = customOutfitPresetFilename.value;
                if (filename == '') {
                    alert('Please fill in the preset name');
                    return;
                }
                this.emitUIInteraction({ ModularCharacterPart: { savePreset: filename }});
            };
        }

        let customOutfitPresetSelect = document.getElementById('custom-outfit-preset-select') as HTMLSelectElement;
        let customOutfitPresetApply = document.getElementById('custom-outfit-preset-apply');
        if (customOutfitPresetSelect !== null && customOutfitPresetApply !== null) {
            customOutfitPresetApply.onclick = (event: Event) => {
                this.emitUIInteraction({ ModularCharacterPart: { loadPreset: customOutfitPresetSelect.value }});
            };
        }

        let logoPlaceSelect = document.getElementById('logo-place-select') as HTMLSelectElement;
        let logoSelect  = document.getElementById('logo-select') as HTMLSelectElement;
        let logoOffsetX = document.getElementById('logo-offsetX') as HTMLInputElement;
        let logoOffsetY = document.getElementById('logo-offsetY') as HTMLInputElement;
        let logoScale   = document.getElementById('logo-scale') as HTMLInputElement;
        let logoRotate  = document.getElementById('logo-rotate') as HTMLInputElement;
        if (logoPlaceSelect !== null && logoSelect !== null && logoOffsetX !== null && logoOffsetY !== null && logoScale !== null && logoRotate) {
            let logoChange = () => {
                this.emitUIInteraction({ CustomLogo: { Part: logoPlaceSelect.value, FileName: logoSelect.value, X: logoOffsetX.value, Y: logoOffsetY.value, Scale: logoScale.value, Rotate: logoRotate.value }});
            };

            logoSelect.onchange = (event: Event) => {
                logoChange();
            };
            logoOffsetX.onchange = (event: Event) => {
                logoChange();
            };
            logoOffsetY.onchange = (event: Event) => {
                logoChange();
            };
            logoScale.onchange = (event: Event) => {
                logoChange();
            };
            logoRotate.onchange = (event: Event) => {
                logoChange();
            };
        }

        let logoFile = document.getElementById('custom-logo-file') as HTMLInputElement;
        let logoFileText = document.getElementById('custom-logo-file-text');
        let logoFileButton = document.getElementById('custom-logo-file-button');
        let logoFileUploadButton = document.getElementById('custom-logo-file-upload');
        if (logoFile !== null && logoFileText !== null && logoFileButton !== null && logoFileUploadButton !== null) {
            logoFile.onchange = (event: Event) => {
                logoFileText.innerHTML = logoFile.value.split(/(\\|\/)/g).pop();
            };

            logoFileButton.onclick = (event: Event) => {
                logoFile.click();
            };

            logoFileUploadButton.onclick = (event: Event) => {
                if (logoFile.files.length > 0) {
                    let file = logoFile.files[0];
                    if (file.type.match('image/png')) {
                        try {
                            var formData = new FormData();
                            formData.append('customLogoFile', file);

                            var xhr = new XMLHttpRequest();
                            xhr.open('POST', '/upload/logo', true);
                            xhr.onreadystatechange = ((request: XMLHttpRequest, event: Event): any => {
                                if (request.readyState === 4) {
                                    if (request.status === 200) {
                                        alert('Upload Successful.');
                                        this.requestStreamerResponse();
                                    } else {
                                        alert('Upload Failure: ' + request.status + ' (' + request.statusText + ').');
                                    }
                                }
                            }).bind(this, xhr);
                            xhr.send(formData);
                        } catch (error) {
                            console.log(error);
                        }
                    } else {
                        alert('This in not a valid image file. Only JPG and PNG files are allowed.');
                    }
                }
            };
        }

        /*******************************************************************************
         * Camera Framing
         ******************************************************************************/
        let cameraSelect = document.getElementById('camera-select') as HTMLSelectElement;
        if (cameraSelect !== null) {
            cameraSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Camera: { id: cameraSelect.value }});

                let joystickDiv = document.getElementById('joystickDiv');
                let cameraTransform = document.getElementById('camera-transform');
                if (joystickDiv !== null && cameraTransform !== null) {
                    joystickDiv.style.display = cameraSelect.value == '4' ? 'flex' : 'none';
                    cameraTransform.style.display = cameraSelect.value == '4' ? 'block' : 'none';
                }
            };
        }

        let cameraLocationXNum = document.getElementById('camera-location-x') as HTMLInputElement;
        let cameraLocationYNum = document.getElementById('camera-location-y') as HTMLInputElement;
        let cameraLocationZNum = document.getElementById('camera-location-z') as HTMLInputElement;
        if (cameraLocationXNum !== null && cameraLocationYNum !== null && cameraLocationZNum !== null) {
            let cameraLocationChange = () => {
                this.emitUIInteraction({ Camera: { location: cameraLocationXNum.value + ',' + cameraLocationYNum.value + ',' + cameraLocationZNum.value }});
            };
            cameraLocationXNum.onchange = (event: Event) => {
                cameraLocationChange();
            };
            cameraLocationYNum.onchange = (event: Event) => {
                cameraLocationChange();
            };
            cameraLocationZNum.onchange = (event: Event) => {
                cameraLocationChange();
            };
        }

        let cameraRotationPNum = document.getElementById('camera-rotation-p') as HTMLInputElement;
        let cameraRotationYNum = document.getElementById('camera-rotation-y') as HTMLInputElement;
        let cameraRotationRNum = document.getElementById('camera-rotation-r') as HTMLInputElement;
        if (cameraRotationPNum !== null && cameraRotationYNum !== null && cameraRotationRNum !== null) {
            let cameraRotationChange = () => {
                this.emitUIInteraction({ Camera: { rotation: cameraRotationPNum.value + ',' + cameraRotationYNum.value + ',' + cameraRotationRNum.value }});
            };
            cameraRotationPNum.onchange = (event: Event) => {
                cameraRotationChange();
            };
            cameraRotationYNum.onchange = (event: Event) => {
                cameraRotationChange();
            };
            cameraRotationRNum.onchange = (event: Event) => {
                cameraRotationChange();
            };
        }

        let characterLocationXNum = document.getElementById('character-location-x') as HTMLInputElement;
        let characterLocationYNum = document.getElementById('character-location-y') as HTMLInputElement;
        let characterLocationZNum = document.getElementById('character-location-z') as HTMLInputElement;
        if (characterLocationXNum !== null && characterLocationYNum !== null && characterLocationZNum !== null) {
            let characterLocationChange = () => {
                this.emitUIInteraction({ Character: { location: characterLocationXNum.value + ',' + characterLocationYNum.value + ',' + characterLocationZNum.value }});
            };
            characterLocationXNum.onchange = (event: Event) => {
                characterLocationChange();
            };
            characterLocationYNum.onchange = (event: Event) => {
                characterLocationChange();
            };
            characterLocationZNum.onchange = (event: Event) => {
                characterLocationChange();
            };
        }

        let characterRotationPNum = document.getElementById('character-rotation-p') as HTMLInputElement;
        let characterRotationYNum = document.getElementById('character-rotation-y') as HTMLInputElement;
        let characterRotationRNum = document.getElementById('character-rotation-r') as HTMLInputElement;
        if (characterRotationPNum !== null && characterRotationYNum !== null && characterRotationRNum !== null) {
            let characterRotationChange = () => {
                this.emitUIInteraction({ Character: { rotation: characterRotationPNum.value + ',' + characterRotationYNum.value + ',' + characterRotationRNum.value }});
            };
            characterRotationPNum.onchange = (event: Event) => {
                characterRotationChange();
            };
            characterRotationYNum.onchange = (event: Event) => {
                characterRotationChange();
            };
            characterRotationRNum.onchange = (event: Event) => {
                characterRotationChange();
            };
        }

        let characterMoveBlendTimeNum = document.getElementById('character-move-blendtime') as HTMLInputElement;
        if (characterMoveBlendTimeNum !== null) {
            characterMoveBlendTimeNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Character: { moveBlendTime: characterMoveBlendTimeNum.value }});
            };
        }

        let characterRotateBlendTimeNum = document.getElementById('character-rotate-blendtime') as HTMLInputElement;
        if (characterRotateBlendTimeNum !== null) {
            characterRotateBlendTimeNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Character: { rotateBlendTime: characterRotateBlendTimeNum.value }});
            };
        }

        // FreeCam mouse event
        let zoomInBtn = document.getElementById('zoomInBtn');
        if (zoomInBtn !== null) {
            zoomInBtn.addEventListener('mousedown', (event: Event) => {
                this.emitUIInteraction({ FreeCamMove: 'Forward_1' });
            });
            zoomInBtn.addEventListener('mouseup', (event: Event) => {
                this.emitUIInteraction({ FreeCamMove: 'Forward_0' });
            });
            zoomInBtn.addEventListener('mouseout', (event: Event) => {
                this.emitUIInteraction({ FreeCamMove: 'Forward_0' });
            });
        }

        let zoomOutBtn = document.getElementById('zoomOutBtn');
        if (zoomOutBtn !== null) {
            zoomOutBtn.addEventListener('mousedown', (event: Event) => {
                this.emitUIInteraction({ FreeCamMove: 'Backward_1' });
            });
            zoomOutBtn.addEventListener('mouseup', (event: Event) => {
                this.emitUIInteraction({ FreeCamMove: 'Backward_0' });
            });
            zoomOutBtn.addEventListener('mouseout', (event: Event) => {
                this.emitUIInteraction({ FreeCamMove: 'Backward_0' });
            });
        }

        let cameraInfoCopy = document.getElementById('camera-info-copy');
        if (cameraInfoCopy !== null) {
            cameraInfoCopy.onclick = (event: Event) => {
                let characterInfo = '';
                function tmp_mcloudResponse(data: string) {
                    let jsonObj = JSON.parse(data);
                    if (jsonObj.response == 'characterResponse') {
                        let characterLocationValues = jsonObj.characterLocation.split(' ').map((coord: string) => { return parseFloat(coord.split('=')[1]); }).join(',');
                        let characterRotationValues = jsonObj.characterRotation.split(' ').map((coord: string) => { return parseFloat(coord.split('=')[1]); }).join(',');
                        characterInfo = `emitUIInteraction({ Character: { location: '${characterLocationValues}', rotation: '${characterRotationValues}' }});`;
                        this.emitUIInteraction({ ResponseRequest: 'cameraResponse' });
                    }
                    else if (jsonObj.response == 'cameraResponse') {
                        let cameraLocationValues = jsonObj.cameraLocation.split(' ').map((coord: string) => { return parseFloat(coord.split('=')[1]); }).join(',');
                        let cameraRotationValues = jsonObj.cameraRotation.split(' ').map((coord: string) => { return parseFloat(coord.split('=')[1]); }).join(',');
                        let cameraInfo = `emitUIInteraction({ Camera: { location: '${cameraLocationValues}', rotation: '${cameraRotationValues}' }});`;
                        navigator.clipboard.writeText(characterInfo + '\r\n' + cameraInfo).then(function() {
                            console.log('Camera info copied to clipboard');
                            let feedback = document.getElementById('copy-feedback');
                            feedback.style.display = 'inline';
                            setTimeout(() => { feedback.style.display = 'none'; }, 2000); // Hide feedback message after 2 seconds
                        }, function() {
                            console.log('Failed to copy camera info to clipboard');
                        });

                        this._pixelStreaming.removeResponseEventListener('infocopy_mcloud_response');
                    }
                }
                const tmp_mcloudResponseBinded = tmp_mcloudResponse.bind(this);
                this._pixelStreaming.addResponseEventListener('infocopy_mcloud_response', (data:string) => tmp_mcloudResponseBinded(data));
                this.emitUIInteraction({ ResponseRequest: 'characterResponse' });
            };
        }

        /*******************************************************************************
         * Animation
         ******************************************************************************/

        /*mcloud
            favorites
                animation: [
                    {character : charactername, list: [{type: , name: , commands: }, ...}
                ]
            showSsmlPopup*/
        const refreshAnimationFavoritesPanel = (activeTab: string = '') => {
            try {
                let rootDiv = document.getElementById('animation-favorites-tab-panel');
                if (rootDiv !== null) {
                    rootDiv.innerHTML = '';

                    let savedData = localStorage.getItem('mcloud');
                    let jsonObj = (savedData !== null) ? JSON.parse(savedData) : null;
                    if (jsonObj?.favorites?.animation?.length > 0) {
                        let ul = document.createElement('ul');
                        ul.classList.add('nav', 'nav-tabs', 'text-white', 'h6');

                        let div = document.createElement('div');
                        div.classList.add('tab-content');

                        rootDiv.appendChild(ul);
                        rootDiv.appendChild(div);

                        let activateIndex = 0;
                        jsonObj.favorites.animation.forEach(function(item: any, index: any) {
                            if (activeTab == item.character) {
                                activateIndex = index;
                            }
                        });

                        jsonObj.favorites.animation.forEach(function(item: any, index: any) {
                            let li = document.createElement('li');
                            li.classList.add('nav-item');

                            let button = document.createElement('button');
                            button.classList.add('nav-link');
                            if (activateIndex == index) button.classList.add('active');
                            button.setAttribute('data-bs-toggle', 'tab');
                            button.setAttribute('data-bs-target', `#animation-favorites-${item.character}-container`);
                            button.innerHTML = item.character;

                            li.appendChild(button);
                            ul.appendChild(li);

                            let tabPaneDiv = document.createElement('div');
                            tabPaneDiv.classList.add('tab-pane', 'fade');
                            if (activateIndex == index) tabPaneDiv.classList.add('show', 'active');
                            tabPaneDiv.id = `animation-favorites-${item.character}-container`;

                            div.appendChild(tabPaneDiv);

                            let loopAnimDiv = document.createElement('div');
                            loopAnimDiv.id = `animation-favorites-${item.character}-loopanim-container`;

                            let triggerAnimDiv = document.createElement('div');
                            triggerAnimDiv.id = `animation-favorites-${item.character}-triggeranim-container`;

                            let expressionDiv = document.createElement('div');
                            expressionDiv.id = `animation-favorites-${item.character}-expression-container`;

                            item.list.forEach(function(child: any) {
                                let btnDiv = document.createElement('div');
                                btnDiv.setAttribute('style', 'display:inline-block; margin:0px 5px 5px 0px;');

                                let actButton = document.createElement('button');
                                actButton.classList.add('btn', 'btn-primary', 'btn-sm');
                                actButton.innerHTML = child.name;
                                actButton.onclick = (event: Event) => {
                                    try {
                                        this.ts_eval.run.call(this, 'this.' + child.commands);
                                    } catch (error) {
                                        console.error(error);
                                    }
                                };

                                let delButton = document.createElement('button');
                                delButton.classList.add('btn-close', 'btn-close-white');
                                delButton.setAttribute('style', 'padding: 0em;');
                                delButton.onclick = (event: Event) => {
                                    try {
                                        let jsonObj = JSON.parse(localStorage.getItem('mcloud'));
                                        for (let i = 0; i < jsonObj.favorites.animation.length; i++) {
                                            let _item = jsonObj.favorites.animation[i]; // the reference of i
                                            if (_item.character == item.character) {
                                                _item.list = _item.list.filter((_child: any) => !(_child.type == child.type && _child.name == child.name));
                                                if (_item.list.length == 0) {
                                                    jsonObj.favorites.animation.splice(i, 1);
                                                }
                                            }
                                        }
                                        localStorage.setItem('mcloud', JSON.stringify(jsonObj));
                                        refreshAnimationFavoritesPanel(item.character);
                                    } catch (error) { console.error(error); }
                                };

                                btnDiv.appendChild(actButton);
                                btnDiv.appendChild(delButton);

                                switch (child.type) {
                                    case 'loopAnimation':
                                        loopAnimDiv.appendChild(btnDiv);
                                        break;
                                    case 'triggerAnimation':
                                        triggerAnimDiv.appendChild(btnDiv);
                                        break;
                                    case 'expression':
                                        expressionDiv.appendChild(btnDiv);
                                        break;
                                }
                            }, this);

                            if (loopAnimDiv.childNodes.length > 0) {
                                let h6 = document.createElement('h6');
                                h6.classList.add('text-white');
                                h6.innerHTML = 'Looping Animation';
                                tabPaneDiv.appendChild(h6);
                                tabPaneDiv.appendChild(loopAnimDiv);
                            }
                            if (triggerAnimDiv.childNodes.length > 0) {
                                let h6 = document.createElement('h6');
                                h6.classList.add('text-white');
                                h6.innerHTML = 'Triggered Animation';
                                tabPaneDiv.appendChild(h6);
                                tabPaneDiv.appendChild(triggerAnimDiv);
                            }
                            if (expressionDiv.childNodes.length > 0) {
                                let h6 = document.createElement('h6');
                                h6.classList.add('text-white');
                                h6.innerHTML = 'Facial Expression';
                                tabPaneDiv.appendChild(h6);
                                tabPaneDiv.appendChild(expressionDiv);
                            }
                        }, this);
                    }
                }
            } catch (error) { console.log(error); }
        };

        const addAnimationFavorites = (characterName: string, type: string, name: string, commands: string) => {
            if (!this.isConnected) {
                return;
            }

            let savedData = localStorage.getItem('mcloud');
            let jsonObj = (savedData !== null) ? JSON.parse(savedData) : new Object;
            if (!jsonObj.hasOwnProperty('favorites')) {
                jsonObj.favorites = {};
            }
            if (!jsonObj.favorites.hasOwnProperty('animation')) {
                jsonObj.favorites.animation = [];
            }
            if (!jsonObj.favorites.animation.some((item: any) => item.character == characterName)) {
                jsonObj.favorites.animation.push({ character: characterName, list: [] });
            }
            for (let item of jsonObj.favorites.animation) {
                if (item.character == characterName) {
                    if (!item.list.some((child: any) => child.type == type && child.name == name)) {
                        item.list.push({ type: type, name: name, commands: commands});
                        localStorage.setItem('mcloud', JSON.stringify(jsonObj));
                    }
                    break;
                }
            }
        };
        refreshAnimationFavoritesPanel();

        let loopAnimationSelect = document.getElementById('loopAnimation-select') as HTMLSelectElement;
        if (loopAnimationSelect !== null) {
            loopAnimationSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Animation: { id: loopAnimationSelect.value, isBlendForth: 1 }});
            };

            let loopAnimationDock = document.getElementById('loopAnimation-dock');
            if (loopAnimationDock !== null) {
                loopAnimationDock.onclick = (event: Event) => {
                    let selectedText = loopAnimationSelect.options[loopAnimationSelect.selectedIndex].text;
                    let selectedValue = loopAnimationSelect.options[loopAnimationSelect.selectedIndex].value;
                    let characterSelect = document.getElementById('character-select') as HTMLSelectElement;
                    if (characterSelect !== null) {
                        let characterName = characterSelect.options[characterSelect.selectedIndex].text;
                        addAnimationFavorites(characterName, 'loopAnimation', selectedText, `emitUIInteraction({ Animation: { id: ${selectedValue}, isBlendForth: 1 }});`);
                        refreshAnimationFavoritesPanel(characterName);
                    }
                }
            }
        }

        let triggerAnimationSelect = document.getElementById('triggerAnimation-select') as HTMLSelectElement;
        let triggerAnimationButton = document.getElementById('triggerAnimation-button');
        if (triggerAnimationSelect !== null && triggerAnimationButton !== null) {
            triggerAnimationButton.onclick = (event: Event) => {
                this.emitUIInteraction({ Animation: { id: triggerAnimationSelect.value, isBlendForth: 1, isBlendBack: 1 }});
            };

            let triggerAnimationDock = document.getElementById('triggerAnimation-dock');
            if (triggerAnimationDock !== null) {
                triggerAnimationDock.onclick = (event: Event) => {
                    let selectedText = triggerAnimationSelect.options[triggerAnimationSelect.selectedIndex].text;
                    let selectedValue = triggerAnimationSelect.options[triggerAnimationSelect.selectedIndex].value;
                    let characterSelect = document.getElementById('character-select') as HTMLSelectElement;
                    if (characterSelect !== null) {
                        let characterName = characterSelect.options[characterSelect.selectedIndex].text;
                        addAnimationFavorites(characterName, 'triggerAnimation', selectedText, `emitUIInteraction({ Animation: { id: ${selectedValue}, isBlendForth: 1, isBlendBack: 1 }});`);
                        refreshAnimationFavoritesPanel(characterName);
                    }
                }
            }
        }

        let expressionSelect = document.getElementById('expression-select') as HTMLSelectElement;
        if (expressionSelect !== null) {
            expressionSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Expression: { id: expressionSelect.value }});
            };

            let expressionDock = document.getElementById('expression-dock');
            if (expressionDock !== null) {
                expressionDock.onclick = (event: Event) => {
                    let selectedText = expressionSelect.options[expressionSelect.selectedIndex].text;
                    let selectedValue = expressionSelect.options[expressionSelect.selectedIndex].value;
                    let characterSelect = document.getElementById('character-select') as HTMLSelectElement;
                    if (characterSelect !== null) {
                        let characterName = characterSelect.options[characterSelect.selectedIndex].text;
                        addAnimationFavorites(characterName, 'expression', selectedText, `emitUIInteraction({ Expression: { id: ${selectedValue} }});`);
                        refreshAnimationFavoritesPanel(characterName);
                    }
                }
            }
        }

        let autoBlinkSwitch = document.getElementById('autoblink-switch') as HTMLInputElement;
        if (autoBlinkSwitch !== null) {
            autoBlinkSwitch.onchange = (event: Event) => {
                autoBlinkSwitch.checked ? this.emitUIInteraction({ AutoBlink: { enabled: 1 }}) : this.emitUIInteraction({ AutoBlink: { enabled: 0 }});
            };
        }

        /*******************************************************************************
         * LookAt
         ******************************************************************************/
        let lookAtCamSelect = document.getElementById('lookAt-select') as HTMLSelectElement;
        let lookAtOptionsContainer = document.getElementById('lookAtOptions-container');
        let lookAtOptionsCustomContainer = document.getElementById('lookAtOptions-custom-container');
        if (lookAtCamSelect !== null && lookAtOptionsContainer !== null && lookAtOptionsCustomContainer !== null) {
            lookAtCamSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { lookAtTargetType: lookAtCamSelect.value }});

                lookAtOptionsContainer.style.display = lookAtCamSelect.value == '0' ? 'none' : 'grid';
                lookAtOptionsCustomContainer.style.display = lookAtCamSelect.value == '2' ? 'grid' : 'none';
            };

            let lookAtCamOffsetXRange = document.getElementById('LookAtCamOffsetX_Range') as HTMLInputElement;
            let lookAtCamOffsetXNum   = document.getElementById('LookAtCamOffsetX_num') as HTMLInputElement;
            let lookAtCamOffsetYRange = document.getElementById('LookAtCamOffsetY_Range') as HTMLInputElement;
            let lookAtCamOffsetYNum   = document.getElementById('LookAtCamOffsetY_num') as HTMLInputElement;
            let lookAtCamLimitAngleHorizontalNum = document.getElementById('lookAtCamLimitAngle-horizontal-num') as HTMLInputElement;
            let lookAtCamLimitAngleVerticalNum   = document.getElementById('lookAtCamLimitAngle-vertical-num') as HTMLInputElement;
            let lookAtCamBlendTimeNum            = document.getElementById('lookAtCamBlendTime-num') as HTMLInputElement;
            let lookAtCamLockPitch               = document.getElementById('lookAtCamLockPitch') as HTMLInputElement;
            let lookAtCamEyeOnly                 = document.getElementById('lookAtCamEyeOnly') as HTMLInputElement;
            let lookAtCamCustomRotateYaw         = document.getElementById('lookAtCam-custom-rotate-yaw') as HTMLInputElement;
            let lookAtCamCustomRotatePitch       = document.getElementById('lookAtCam-custom-rotate-pitch') as HTMLInputElement;
            let lookAtCamCustomRotateEyeSpeed    = document.getElementById('lookAtCam-custom-rotate-eye-speed') as HTMLInputElement;
            let lookAtCamCustomRotateHeadSpeed   = document.getElementById('lookAtCam-custom-rotate-head-speed') as HTMLInputElement;
            let lookAtCamCustomRotateBodySpeed   = document.getElementById('lookAtCam-custom-rotate-body-speed') as HTMLInputElement;

            lookAtCamOffsetXRange.oninput = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { offset: lookAtCamOffsetXRange.value + ',' + lookAtCamOffsetYRange.value }});
                lookAtCamOffsetXNum.value = lookAtCamOffsetXRange.value;
            };
            lookAtCamOffsetYRange.oninput = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { offset: lookAtCamOffsetXRange.value + ',' + lookAtCamOffsetYRange.value }});
                lookAtCamOffsetYNum.value = lookAtCamOffsetYRange.value;
            };
            lookAtCamLimitAngleHorizontalNum.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { limitAngle: lookAtCamLimitAngleHorizontalNum.value + ',' + lookAtCamLimitAngleVerticalNum.value }});
            };
            lookAtCamLimitAngleVerticalNum.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { limitAngle: lookAtCamLimitAngleHorizontalNum.value + ',' + lookAtCamLimitAngleVerticalNum.value }});
            };
            lookAtCamBlendTimeNum.onchange = (event: Event) => {
                let second = Number(lookAtCamBlendTimeNum.value) / 1000;
                this.emitUIInteraction({ LookAtCam: { blendTime: second }});
            };
            lookAtCamLockPitch.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { lockPitch: lookAtCamLockPitch.checked ? 1 : 0 }});
            };
            lookAtCamEyeOnly.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { eyeOnly: lookAtCamEyeOnly.checked ? 1 : 0 }});
            };
            lookAtCamCustomRotateYaw.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { customRotateYaw: lookAtCamCustomRotateYaw.value }});
            };
            lookAtCamCustomRotatePitch.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { customRotatePitch: lookAtCamCustomRotatePitch.value }});
            };
            lookAtCamCustomRotateEyeSpeed.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { customRotateEyeSpeed: lookAtCamCustomRotateEyeSpeed.value }});
            };
            lookAtCamCustomRotateHeadSpeed.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { customRotateHeadSpeed: lookAtCamCustomRotateHeadSpeed.value }});
            };
            lookAtCamCustomRotateBodySpeed.onchange = (event: Event) => {
                this.emitUIInteraction({ LookAtCam: { customRotateBodySpeed: lookAtCamCustomRotateBodySpeed.value }});
            };
        }

        /*******************************************************************************
         * Background
         ******************************************************************************/
        let backgroundSelect = document.getElementById('background-select') as HTMLSelectElement;
        if (backgroundSelect !== null) {
            backgroundSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Background: { id: backgroundSelect.value }});
            };

            let bgDock = document.getElementById('bg-file-dock');
            let bgDockContainer = document.getElementById('bg-file-dock-container');
            if (bgDock !== null && bgDockContainer !== null) {
                bgDock.onclick = (event: Event) => {
                    if (!this.isConnected) {
                        return;
                    }

                    let selectedText = backgroundSelect.options[backgroundSelect.selectedIndex].text;
                    let selectedValue = backgroundSelect.options[backgroundSelect.selectedIndex].value;
                    if (Array.prototype.some.call(bgDockContainer.childNodes, (child: HTMLDivElement) => child.id == selectedText)) {
                        return;
                    }

                    let div = document.createElement('div');
                    div.setAttribute('style', 'display:inline-block; margin:0px 5px 5px 0px;');
                    div.id = selectedText;

                    let button = document.createElement('button');
                    button.classList.add('btn', 'btn-primary', 'btn-sm');
                    button.innerHTML = selectedText;
                    button.onclick = (event: Event) => { this.emitUIInteraction({ Background: { id: selectedValue }}); };

                    let delButton = document.createElement('button');
                    delButton.classList.add('btn-close', 'btn-close-white');
                    delButton.setAttribute('style', 'padding: 0em;');
                    delButton.onclick = (event: Event) => { div.remove(); };

                    div.appendChild(button);
                    div.appendChild(delButton);
                    bgDockContainer.appendChild(div);
                };
            }
        }

        let bgFile = document.getElementById('bg-file') as HTMLInputElement;
        let bgFileText = document.getElementById('bg-file-text');
        let bgFileButton = document.getElementById('bg-file-button');
        let bgFileUploadButton = document.getElementById('bg-file-upload');
        if (bgFile !== null && bgFileText !== null && bgFileButton !== null && bgFileUploadButton !== null) {
            bgFile.onchange = (event: Event) => {
                bgFileText.innerHTML = bgFile.value.split(/(\\|\/)/g).pop();
            };

            bgFileButton.onclick = (event: Event) => {
                bgFile.click();
            };

            bgFileUploadButton.onclick = (event: Event) => {
                if (bgFile.files.length > 0) {
                    let file = bgFile.files[0];
                    if (file.type.match('image/jpeg') || file.type.match('image/png') || file.type.match('video/mp4')) {
                        try {
                            let formData = new FormData();
                            formData.append('bgFile', file);

                            let xhr = new XMLHttpRequest();
                            xhr.open('POST', '/upload/bg', true);
                            xhr.onreadystatechange = ((request: XMLHttpRequest, event: Event): any => {
                                if (request.readyState === 4) {
                                    if (request.status === 200) {
                                        alert('Upload Successful.');
                                        this.requestStreamerResponse();
                                    } else {
                                        alert('Upload Failure: ' + request.status + ' (' + request.statusText + ').');
                                    }
                                }
                            }).bind(this, xhr);
                            xhr.send(formData);
                        } catch (error) {
                            console.log(error);
                        }
                    } else {
                        alert('This in not a valid image file. Only JPG and PNG files are allowed.');
                    }
                }
            };
        }

        let backgroundStyleSelect = document.getElementById('background-style-select') as HTMLSelectElement;
        if (backgroundStyleSelect !== null) {
            backgroundStyleSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Background: { backgroundStyle: backgroundStyleSelect.value }});
            };
        }

        let backgroundModelSelect = document.getElementById('background-mode-select') as HTMLSelectElement;
        if (backgroundModelSelect !== null) {
            backgroundModelSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Background: { backgroundMode: backgroundModelSelect.value }});
            };
        }

        let backgroundBlendTimeNum = document.getElementById('background-blendtime') as HTMLInputElement;
        if (backgroundBlendTimeNum !== null) {
            backgroundBlendTimeNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Background: { blendTime: backgroundBlendTimeNum.value }});
            };
        }

        let backgroundColorPick = document.getElementById('background-color') as HTMLInputElement;
        if (backgroundColorPick !== null) {
            backgroundColorPick.oninput = (event: Event) => {
                let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(backgroundColorPick.value);
                let colorR = parseInt(result[1], 16);
                let colorG = parseInt(result[2], 16);
                let colorB = parseInt(result[3], 16);
                this.emitUIInteraction({ Background: { singleColor: colorR + ',' + colorG + ',' + colorB }});
            };
        }

        let resWidthInput = document.getElementById('resolution-width') as HTMLInputElement;
        let resHeightInput = document.getElementById('resolution-height') as HTMLInputElement;
        let resolutionButton = document.getElementById('resolution-button') as HTMLInputElement;
        if (resWidthInput !== null && resHeightInput !== null && resolutionButton !== null) {
            resolutionButton.onclick = (event: Event) => {
                this.emitUIInteraction({ ScreenResolution: { width: resWidthInput.value, height: resHeightInput.value }});
            };
        }

        /*******************************************************************************
         * Lighting
         ******************************************************************************/
        let lightSelect = document.getElementById('lightset-select') as HTMLSelectElement;
        if (lightSelect !== null) {
            lightSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Light: { id: lightSelect.value }});
                this.requestStreamerResponse();
            };
        }

        let singleLightSelect = document.getElementById('light-select') as HTMLSelectElement;
        if (singleLightSelect !== null) {
            singleLightSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Light: { lightSetConfig:{ id: singleLightSelect.value, refresh: 1 }}});
            };
        }

        let lightingFollowSwitch = document.getElementById('lightingFollow-switch') as HTMLInputElement;
        if (lightingFollowSwitch !== null) {
            lightingFollowSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ Light: { followCharacter: lightingFollowSwitch.checked ? 1 : 0}});
            };
        }

        let lightEnable = document.getElementById('light-switch') as HTMLInputElement;
        let lightIntensity = document.getElementById('light-intensity') as HTMLInputElement;
        let lightColor = document.getElementById('light-color') as HTMLInputElement;
        let lightLocX = document.getElementById('light-loc-X') as HTMLInputElement;
        let lightLocY = document.getElementById('light-loc-Y') as HTMLInputElement;
        let lightLocZ = document.getElementById('light-loc-Z') as HTMLInputElement;
        let lightRotX = document.getElementById('light-rot-X') as HTMLInputElement;
        let lightRotY = document.getElementById('light-rot-Y') as HTMLInputElement;
        let lightRotZ = document.getElementById('light-rot-Z') as HTMLInputElement;
        let lightGizmoEnable = document.getElementById('light-gizmo-switch') as HTMLInputElement;
        if (singleLightSelect !== null ) {
            if (lightEnable !== null) {
                lightEnable.onchange = (event: Event) => {
                    this.emitUIInteraction({ Light: { lightSetConfig:{ id: singleLightSelect.value, visible: lightEnable.checked ? 1 : 0, refresh: 0 }}});
                };
            }
            if (lightIntensity !== null) {
                lightIntensity.onchange = (event: Event) => {
                    this.emitUIInteraction({ Light: { lightSetConfig:{ id: singleLightSelect.value, intensity: lightIntensity.value, refresh: 0 }}});
                };
            }

            if (lightLocX !== null && lightLocY !== null && lightLocZ !== null) {
                let lightLocChange = () => {
                    this.emitUIInteraction({ Light: { lightSetConfig: { id: singleLightSelect.value, location: lightLocX.value + ',' + lightLocY.value + ',' + lightLocZ.value, refresh: 0 }}});
                };
                lightLocX.onchange = (event: Event) => {
                    lightLocChange();
                };
                lightLocY.onchange = (event: Event) => {
                    lightLocChange();
                };
                lightLocZ.onchange = (event: Event) => {
                    lightLocChange();
                };
            }
            if (lightRotX !== null && lightRotY !== null && lightRotZ !== null) {
                let lightRotChange = () => {
                    this.emitUIInteraction({ Light: {lightSetConfig: { id: singleLightSelect.value, rotation: lightRotX.value + ',' + lightRotY.value + ',' + lightRotZ.value, refresh: 0 }}});
                }

                lightRotX.onchange = (event: Event) => {
                    lightRotChange();
                };
                lightRotY.onchange = (event: Event) => {
                    lightRotChange();
                };
                lightRotZ.onchange = (event: Event) => {
                    lightRotChange();
                };
            }
            if (lightColor !== null) {
                lightColor.oninput = (event: Event) => {
                    let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(lightColor.value);
                    let colorR = parseInt(result[1], 16);
                    let colorG = parseInt(result[2], 16);
                    let colorB = parseInt(result[3], 16);
                    this.emitUIInteraction({ Light: { lightSetConfig:{ id: singleLightSelect.value, color: colorR + ',' + colorG + ',' + colorB, refresh: 0 }}});
                };
            }
            if (lightGizmoEnable !== null) {
                lightGizmoEnable.onchange = (event: Event) => {
                    this.emitUIInteraction({ Light: {gizmoEnable: lightGizmoEnable.checked ? 1 : 0 }});
                };
            }
        }

        let vrmLightPosX = document.getElementById('vrm-light-x') as HTMLInputElement;
        let vrmLightPosY = document.getElementById('vrm-light-y') as HTMLInputElement;
        let vrmLightPosZ = document.getElementById('vrm-light-z') as HTMLInputElement;
        if (vrmLightPosX !== null && vrmLightPosY !== null && vrmLightPosZ !== null){
            let vrmLightChange = () => {
                this.emitUIInteraction({ VRMLight: {source: {x: vrmLightPosX.value, y: vrmLightPosY.value, z: vrmLightPosZ.value}}})
            };
            vrmLightPosX.onchange = (event: Event) => {
                vrmLightChange();
            };
            vrmLightPosY.onchange = (event: Event) => {
                vrmLightChange();
            };
            vrmLightPosZ.onchange = (event: Event) => {
                vrmLightChange();
            };
        }

        /*******************************************************************************
         * Effect
         ******************************************************************************/
        let envParticleSelect = document.getElementById('env-particle-select') as HTMLSelectElement;
        let triggerParticleSelect = document.getElementById('trigger-particle-select') as HTMLSelectElement;
        let enableParticle  = document.getElementById('trigger-particle');
        let particleOffsetX = document.getElementById('particle-offsetX') as HTMLInputElement;
        let particleOffsetY = document.getElementById('particle-offsetY') as HTMLInputElement;
        let particleOffsetZ = document.getElementById('particle-offsetZ') as HTMLInputElement;
        if (triggerParticleSelect !== null && enableParticle !== null) {
            enableParticle.onclick = (event: Event) => {
                this.emitUIInteraction({ Particle: { id: triggerParticleSelect.value, enable: 1, X: particleOffsetX.value, Y: particleOffsetY.value, Z: particleOffsetZ.value }});
            };
        }

        if (envParticleSelect !== null) {
            envParticleSelect.onchange = (event: Event) => {
                for (let i = 0; i < envParticleSelect.options.length; i++) {
                    this.emitUIInteraction({ Particle: { id: envParticleSelect.options[i].value, enable: envParticleSelect.options[i].selected ? 1 : 0 }});
                }

            };
        }

        /*******************************************************************************
         * Speech
         ******************************************************************************/
        let audioStreamContainer = document.getElementById('audiosource-stream-container');
        if (audioStreamContainer !== null) {
            let audioStreamMicRadio  = document.getElementById('audio-stream-mic-radio') as HTMLInputElement;
            let audioStreamFileRadio = document.getElementById('audio-stream-file-radio') as HTMLInputElement;
            let audioStreamUrlRadio  = document.getElementById('audio-stream-url-radio') as HTMLInputElement;
            let usemicCheckbox       = document.getElementById('usemic-tgl') as HTMLInputElement;
            usemicCheckbox.onchange = (event: Event) => {
                this.stopAudio();
                if (usemicCheckbox.checked) {
                    let callbackFn = (success: boolean) => {
                        if (!success) {
                            alert('Unable to access your Microphone.');
                            usemicCheckbox.checked = false;
                        }
                    }
                    this.startMic(callbackFn);
                } else {
                    this.stopMic();
                }
            };

            let audioStreamFile           = document.getElementById('audio-stream-file') as HTMLInputElement;
            let audioStreamFileText       = document.getElementById('audio-stream-file-text');
            let audioStreamFileButton     = document.getElementById('audio-stream-file-button');
            let audioStreamFilePlayButton = document.getElementById('audio-stream-file-play-button');
            let audioStreamFileStopButton = document.getElementById('audio-stream-file-stop-button');
            audioStreamFile.onchange = (event: Event) => {
                audioStreamFileText.innerHTML = audioStreamFile.value.split(/(\\|\/)/g).pop();
            };

            audioStreamFileButton.onclick = (event: Event) => {
                audioStreamFile.click();
            };

            audioStreamFilePlayButton.onclick = (event: Event) => {
                this.stopAudio();
                if (audioStreamFile.files.length) {
                    let objectURL = URL.createObjectURL(audioStreamFile.files[0]);
                    this.playAudio(objectURL);
                    URL.revokeObjectURL(objectURL);
                }
            };

            audioStreamFileStopButton.onclick = (event: Event) => {
                this.stopAudio();
            };

            let audioStreamUrlText       = document.getElementById('audio-stream-url-text') as HTMLInputElement;
            let audioStreamUrlPlayButton = document.getElementById('audio-stream-url-play-button');
            let audioStreamUrlStopButton = document.getElementById('audio-stream-url-stop-button');
            audioStreamUrlPlayButton.onclick = (event: Event) => {
                this.stopAudio();
                if (audioStreamUrlText.value.length > 0) {
                    this.playAudio(audioStreamUrlText.value);
                }
            };

            audioStreamUrlStopButton.onclick = (event: Event) => {
                this.stopAudio();
            };

            let audioStreamSourceChange = () => {
                this.stopAudio();
                let audioStreamMicContainer  = document.getElementById('audio-stream-mic-container');
                let audioStreamFileContainer = document.getElementById('audio-stream-file-container');
                let audioStreamUrlContainer  = document.getElementById('audio-stream-url-container');
                audioStreamMicRadio.checked  ? audioStreamMicContainer.classList.remove('div-disabled')  : audioStreamMicContainer.classList.add('div-disabled');
                audioStreamFileRadio.checked ? audioStreamFileContainer.classList.remove('div-disabled') : audioStreamFileContainer.classList.add('div-disabled');
                audioStreamUrlRadio.checked  ? audioStreamUrlContainer.classList.remove('div-disabled')  : audioStreamUrlContainer.classList.add('div-disabled');

                if (audioStreamMicRadio.checked) {
                    usemicCheckbox.onchange(null);
                } else {
                    this.stopMic();
                }
            }

            audioStreamMicRadio.onchange = (event: Event) => {
                audioStreamSourceChange();
            };
            audioStreamFileRadio.onchange = (event: Event) => {
                audioStreamSourceChange();
            };
            audioStreamUrlRadio.onchange = (event: Event) => {
                audioStreamSourceChange();
            };
        }

        let audiofileContainer = document.getElementById('audiosource-files-container');
        if (audiofileContainer !== null) {
            let playAudioButton   = document.getElementById('play-audio-button');
            let stopAudioButton   = document.getElementById('stop-audio-button');
            let audioSelect       = document.getElementById('audio-select') as HTMLSelectElement;

            playAudioButton.onclick = (event: Event) => {
                if (audioSelect.value !== '') {
                    let animIDWhenTalk = (document.getElementById('anim-when-talk') as HTMLInputElement)?.value;
                    let animIDAfterTalk = (document.getElementById('anim-after-talk') as HTMLInputElement)?.value;
                    this.emitUIInteraction({ Audio: { playFile: audioSelect.value, animIDTalk: animIDWhenTalk, animIDAfter: animIDAfterTalk }});
                }
            };

            stopAudioButton.onclick = (event: Event) => {
                this.emitUIInteraction({ Audio: { reset: '' }});
            };

            let audioFile = document.getElementById('audio-file') as HTMLInputElement;
            let audioFileText = document.getElementById('audio-file-text');
            let audioFileButton = document.getElementById('audio-file-button');
            let audioFileUploadButton = document.getElementById('audio-file-upload');
            audioFile.onchange = (event: Event) => {
                audioFileText.innerHTML = audioFile.value.split(/(\\|\/)/g).pop();
            };

            audioFileButton.onclick = (event: Event) => {
                audioFile.click();
            };

            audioFileUploadButton.onclick = (event: Event) => {
                if (audioFile.files.length > 0) {
                    let file = audioFile.files[0];
                    // mp3 media type is 'audio/mpeg'
                    if (file.type.match('audio/wav') || file.type.match('audio/mpeg')) {
                        try {
                            let formData = new FormData();
                            formData.append('audioFile', file);

                            let xhr = new XMLHttpRequest();
                            xhr.open('POST', '/upload/audio', true);
                            xhr.onreadystatechange = ((request: XMLHttpRequest, event: Event): any => {
                                if (request.readyState === 4) {
                                    if (request.status === 200) {
                                        alert('Upload Successful.');
                                        this.requestStreamerResponse();
                                    } else {
                                        alert('Upload Failure: ' + request.status + ' (' + request.statusText + ').');
                                    }
                                }
                            }).bind(this, xhr);
                            xhr.send(formData);
                        } catch (error) {
                            console.log(error);
                        }
                    } else {
                        alert('This in not a valid audio file. Only WAV and MP3 files are allowed.');
                    }
                }
            };

            let captionFile = document.getElementById('audio-caption-file') as HTMLInputElement;
            let captionFileText = document.getElementById('audio-caption-file-text');
            let captionFileButton = document.getElementById('audio-caption-file-button');
            let captionFileUploadButton = document.getElementById('audio-caption-file-upload');
            captionFile.onchange = (event: Event) => {
                captionFileText.innerHTML = captionFile.value.split(/(\\|\/)/g).pop();
            };

            captionFileButton.onclick = (event: Event) => {
                captionFile.click();
            };

            captionFileUploadButton.onclick = (event: Event) => {
                if (captionFile.files.length > 0) {
                    let file = captionFile.files[0];
                    if (file.type.match('text/plain')) {
                        try {
                            var formData = new FormData();
                            formData.append('audioFile', file);

                            var xhr = new XMLHttpRequest();
                            xhr.open('POST', '/upload/audio', true);
                            xhr.onreadystatechange = ((request: XMLHttpRequest, event: Event): any => {
                                if (xhr.readyState === 4) {
                                    if (xhr.status === 200) {
                                        alert('Upload Successful.');
                                        this.requestStreamerResponse();
                                    } else {
                                        alert('Upload Failure: ' + request.status + ' (' + request.statusText + ').');
                                    }
                                }
                            }).bind(this, xhr);
                            xhr.send(formData);
                        } catch (error) {
                            console.log(error);
                        }
                    } else {
                        alert('This in not a valid caption file. Only TEXT file is allowed.');
                    }
                }
            };
        }

        let ttsContainer = document.getElementById('audiosource-tts-container');
        if (ttsContainer) {
            let ttsEngineControl   = document.getElementById('tts-engine-control') as HTMLSelectElement;
            let ttsLanguageControl = document.getElementById('tts-language-control') as HTMLSelectElement;
            let ttsVoiceControl    = document.getElementById('tts-voice-control') as HTMLSelectElement;
            let ttsSpeedNum        = document.getElementById('tts-speed-num') as HTMLInputElement;

            ttsEngineControl.onchange = (event: Event) => {
                let ttsTabPanel = document.getElementById('tts-tab-panel');
                if (ttsTabPanel) {
                    ttsTabPanel.classList.add('div-disabled');
                }
                this.emitUIInteraction({ TextToSpeech: { engine: ttsEngineControl.value }});
            };

            ttsLanguageControl.onchange = (event: Event) => {
                let selectedLangCode = ttsLanguageControl.options[ttsLanguageControl.selectedIndex].value;
                Array.prototype.forEach.call(ttsVoiceControl.options, function(option: HTMLOptionElement) {
                    option.hidden = (option.getAttribute('langCode') != selectedLangCode);
                });
                // select first
                for (var i = 0; i < ttsVoiceControl.options.length; i++) {
                    if (ttsVoiceControl.options[i].getAttribute('langCode') == selectedLangCode) {
                        ttsVoiceControl.value = ttsVoiceControl.options[i].value;
                        break;
                    }
                }
                this.emitUIInteraction({ TextToSpeech: { voiceConfig: { voiceId: ttsVoiceControl.value }}});
            };

            ttsVoiceControl.onchange = (event: Event) => {
                this.emitUIInteraction({ TextToSpeech: { voiceConfig: { voiceId: ttsVoiceControl.value }}});
            };

            ttsSpeedNum.onchange = (event: Event) => {
                this.emitUIInteraction({ TextToSpeech: { voiceConfig: { voiceSpeed: ttsSpeedNum.value }}});
            };

            let ssmlSwitch = document.getElementById('ssml-switch') as HTMLInputElement;
            if (ssmlSwitch !== null) {
                ssmlSwitch.onchange = (event: Event) => {
                ssmlSwitch.checked ? ttsSpeedNum.classList.add('div-disabled') : ttsSpeedNum.classList.remove('div-disabled');
                if (ssmlSwitch.checked) {
                    let popup = 'false';
                    let savedData = localStorage.getItem('mcloud');
                    let jsonObj = (savedData !== null) ? JSON.parse(savedData) : new Object;
                    if (jsonObj.hasOwnProperty('showSsmlPopup')) {
                        popup = jsonObj.showSsmlPopup;
                    }
                    if (popup != 'false') {
                        let ssmlModel = document.getElementById('ssmlModel');
                        ssmlModel.addEventListener('hidden.bs.modal', (event: Event) => {
                            let ssmlModelSwitch = document.getElementById('ssmlModel-switch') as HTMLInputElement;
                            let savedData = localStorage.getItem('mcloud');
                            let jsonObj = (savedData !== null) ? JSON.parse(savedData) : new Object;
                            if (!jsonObj.hasOwnProperty('showSsmlPopup')) {
                                jsonObj.showSsmlPopup = ssmlModelSwitch.checked ? 'false' : 'true';
                            }
                            localStorage.setItem('mcloud', JSON.stringify(jsonObj));
                        }, {once: true});
                        let bsmodel = new Modal(ssmlModel);
                        bsmodel.show();
                    }
                }
                };
            }

            let ttsTextSubmitBtn = document.getElementById('tts-text-submit-button');
            ttsTextSubmitBtn.onclick = (event: Event) => {
                this.stopAudio();
                let value = (document.getElementById('tts-text-area') as HTMLTextAreaElement)?.value.trim();
                if (value.length == 0) {
                    //alert('The input text is empty.');
                    return;
                }                
                let splitting = (document.getElementById('text-splitting-switch') as HTMLInputElement)?.checked ? 1 : 0;
                let secondTextValue = (document.getElementById('tts-second-text-area') as HTMLTextAreaElement)?.value.trim();
                let ssml = (document.getElementById('ssml-switch') as HTMLInputElement)?.checked ? 1 : 0;
                let savefile = (document.getElementById('tts-saveas-radio') as HTMLInputElement)?.checked ? 1 : 0;
                if (savefile == 1) {
                    let filename = (document.getElementById('tts-saveas-filename-text') as HTMLInputElement)?.value;
                    if (filename == '') {
                        alert('Please fill in the file name');
                        return;
                    }
                    let secondTextValue = (document.getElementById('tts-second-text-area') as HTMLTextAreaElement)?.value.trim();

                    if (splitting == 1) {
                            const punctuationRegex = /[^。！？?!,.，]+[。！？?!,.，]?/g;
                            const matches = value.match(punctuationRegex);
                            if (!matches || matches.length === 0) {
                                console.log("無法切段，直接送出整段文字");
                                this.emitUIInteraction({ TextToSpeech: { synthesis: { text: value, isSsml: ssml, synthesizeToFile: savefile, filename: filename,secondText: secondTextValue }}});
                                return;
                            }

                            const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));
                            const self = this;

                            // 每切一段就發送一次
                            (async () => {
                                let buffer = '';
                                let count = 0;
                                let segmentIndex = 1;

                                for (const match of matches) {
                                    buffer += match;
                                    if (/[。！？?!,.，]/.test(match)) {
                                        count++;
                                    }

                                    if (count >= 2) {
                                        const segment = buffer.trim();
                                        const indexedFilename = `${filename}${segmentIndex}`;
                                        console.log(`Segment ${segmentIndex}:`, segment);
                                        self.emitUIInteraction({
                                            TextToSpeech: {
                                                synthesis: {
                                                    text: segment,
                                                    isSsml: ssml,
                                                    synthesizeToFile: savefile,
                                                    filename: indexedFilename,
                                                    secondText: secondTextValue
                                                }
                                            }
                                        });

                                        await delay(1000); // 延遲後處理下一段
                                        buffer = '';
                                        count = 0;
                                        segmentIndex++;
                                    }
                                }

                                // 處理最後不足 2 個標點的殘段
                                if (buffer.trim().length > 0) {
                                    const indexedFilename = `${filename}${segmentIndex}`;
                                    console.log(`Final Segment ${segmentIndex}:`, buffer.trim());
                                    self.emitUIInteraction({
                                        TextToSpeech: {
                                            synthesis: {
                                                text: buffer.trim(),
                                                isSsml: ssml,
                                                synthesizeToFile: savefile,
                                                filename: indexedFilename,
                                                secondText: secondTextValue
                                            }
                                        }
                                    });
                                }
                            })();
                    }
                    else{
                        this.emitUIInteraction({ TextToSpeech: { synthesis: { text: value, isSsml: ssml, synthesizeToFile: savefile, filename: filename,secondText: secondTextValue }}});
                    }
                }
                else {
                    let stream = (document.getElementById('stream-switch') as HTMLInputElement)?.checked ? 1 : 0;
                    let animIDWhenTalk = (document.getElementById('anim-when-talk') as HTMLInputElement)?.value;
                    let animIDAfterTalk = (document.getElementById('anim-after-talk') as HTMLInputElement)?.value;
                    this.emitUIInteraction({ TextToSpeech: { synthesis: { text: value, isSsml: ssml, useStream: stream, secondText: secondTextValue, animIDTalk: animIDWhenTalk, animIDAfter: animIDAfterTalk }}});
                }
            };

            let ttsTextStopBtn = document.getElementById('tts-text-stop-button');
            ttsTextStopBtn.onclick = (event: Event) => {
                this.emitUIInteraction({ Audio: { reset: '' }});
            };

            let ttsChatForm = document.getElementById('tts-chat-form');
            let ttsChatMicLangControl = document.getElementById('tts-chat-mic-lang-control');
            let ttsOpenaiVoiceLabel   = document.getElementById('tts-chat-openairealtime-voice-label');
            let ttsOpenaiVoiceControl = document.getElementById('tts-chat-openairealtime-voice-control') as HTMLSelectElement;
            let ttsChatModelControl = document.getElementById('tts-chat-model-control') as HTMLSelectElement;

            if (ttsChatModelControl) {
                // 初始化聊天机器人模型选项（如果还没有选项的话）
                if (ttsChatModelControl.options.length <= 1) { // 只有"None"选项或没有选项
                    ttsChatModelControl.innerHTML = ''; // 清空现有选项
                    const defaultModels = [
                        { name: 'ChatGPT', id: TTS_CHATBOT_ID.ChatGPT.toString() },
                        { name: 'Azure OpenAI', id: TTS_CHATBOT_ID.AzureOpenAI.toString() },
                        { name: '通义千问 (TongyiQianwen)', id: TTS_CHATBOT_ID.TongyiQianwen.toString() },
                        { name: 'OpenAI Realtime', id: TTS_CHATBOT_ID.OpenAIRealtime.toString() }
                    ];

                    defaultModels.forEach((model) => {
                        ttsChatModelControl.add(new Option(model.name, model.id));
                    });

                    // 默认选择Azure OpenAI
                    ttsChatModelControl.value = TTS_CHATBOT_ID.AzureOpenAI.toString();
                }

                ttsChatModelControl.onchange = (event: Event) => {
                let isOpenAIRealtime = ttsChatModelControl.value == TTS_CHATBOT_ID.OpenAIRealtime.toString();
                let isTongyiQianwen = ttsChatModelControl.value == TTS_CHATBOT_ID.TongyiQianwen.toString();

                ttsChatForm.classList.toggle('div-disabled', isOpenAIRealtime);
                ttsChatMicLangControl.classList.toggle('div-disabled', isOpenAIRealtime);
                ttsOpenaiVoiceLabel.style.display = isOpenAIRealtime ? 'inline' : 'none';
                ttsOpenaiVoiceControl.style.display = isOpenAIRealtime ? 'inline' : 'none';

                // 发送聊天机器人模型切换指令 - 尝试多种字段名
                console.log('发送模型切换指令: model=' + ttsChatModelControl.value);
                this.emitUIInteraction({ Chatbot: { model: ttsChatModelControl.value }});
                this.emitUIInteraction({ Chatbot: { chatbotModel: ttsChatModelControl.value }});
                this.emitUIInteraction({ Chatbot: { modelId: ttsChatModelControl.value }});
                this.emitUIInteraction({ Chatbot: { chatbotModelId: ttsChatModelControl.value }});

                // 显示模型切换提示
                if (isTongyiQianwen) {
                    console.log('已切换到通义千问模型');
                    // 可以在这里添加特定于通义千问的配置
                }
            };
            } else {
                console.warn('tts-chat-model-control element not found');
            }

            if (ttsOpenaiVoiceControl) {
                ttsOpenaiVoiceControl.onchange = (event: Event) => {
                    // Reset session. Voice cannot be changed during the session once the model has responded with audio at least once
                    this.emitUIInteraction({ Chatbot: { chatRequest: { type: "session.new" }}});

                    this.emitUIInteraction({ Chatbot: { chatRequest: { type: "session.update", voice: ttsOpenaiVoiceControl.value }}});
                };
            } else {
                console.warn('tts-openai-voice-control元素未找到');
            }

            let ttsChatMicButton = document.getElementById('tts-chat-mic-button') as HTMLInputElement;
            ttsChatMicButton.onclick = (event: Event) => {
                if (ttsChatModelControl.value == TTS_CHATBOT_ID.OpenAIRealtime.toString()) {
                    /*OpenAI realtime(mono PCM16 at 24kHz).*/
                    this.micRecorderInterface.sampleRate  = 24000;
                    this.micRecorderInterface.numChannels = 1;
                    this.micRecorderInterface.onError = (event: any) => {
                        alert(`Microphone recording error detected: ${event.error}`);
                    };
                    this.micRecorderInterface.onStart = () => {
                        let micButton = document.getElementById('tts-chat-mic-button');
                        if (micButton) {
                            micButton.style.backgroundImage = "url('/images/mic-anim.gif')";
                        }
                    }
                    this.micRecorderInterface.onStop = () => {
                        let micButton = document.getElementById('tts-chat-mic-button');
                        if (micButton) {
                            micButton.style.backgroundImage = "url('/images/mic.gif')";
                        }

                        let uint8Array = this.micRecorderInterface.getByteArray();
                        while (uint8Array.length > 0) {
                            var subArray = uint8Array.slice(0, 8192);
                            uint8Array = uint8Array.slice(8192);
                            var base64string = window.btoa(String.fromCharCode.apply(null, subArray));
                            this.emitUIInteraction({ Chatbot: { chatRequest: { type: "audioBuffer.append", audio: base64string }}});
                        }
                        this.emitUIInteraction({ Chatbot: { chatRequest: { type: "audioBuffer.commit" }}});
                    }

                    if (this.micRecorderInterface.isRecording()) {
                        this.micRecorderInterface.stop();
                    } else {
                        this.micRecorderInterface.start();
                    }
                } else {
                    console.log('🎤 尝试使用语音识别功能');
                    console.log('🔍 speechRecognition对象状态:', !!this.speechRecognition);

                    if (!this.speechRecognition) {
                        console.error('❌ 语音识别不可用 - speechRecognition对象为null');
                        console.error('❌ 可能的原因:');
                        console.error('   1. 浏览器不支持语音识别API');
                        console.error('   2. 语音识别初始化失败');
                        console.error('   3. 不是HTTPS环境');
                        console.error('   4. 浏览器版本过低');

                        // 重新尝试初始化语音识别
                        console.log('🔄 尝试重新初始化语音识别...');
                        this.setupSpeechRecognition();

                        if (!this.speechRecognition) {
                            this.showSpeechRecognitionError('语音识别功能不可用。请确保使用Chrome或Edge浏览器，并在HTTPS环境下访问。');
                        } else {
                            console.log('✅ 重新初始化成功，开始语音识别');
                            this.startSpeechRecognitionWithPermissionCheck();
                        }
                    } else {
                        if (this.isRecognizing) {
                            console.log('停止语音识别');
                            this.speechRecognition.stop();
                        } else {
                            console.log('开始语音识别');
                            this.startSpeechRecognitionWithPermissionCheck();
                        }
                    }
                }
            };

            let ttsChatHistorySwitch = document.getElementById('tts-chat-history-switch') as HTMLInputElement;
            if (ttsChatHistorySwitch) {
                console.log('History开关元素找到，绑定事件处理器');
                ttsChatHistorySwitch.onchange = (event: Event) => {
                    console.log('History开关状态改变:', ttsChatHistorySwitch.checked);
                    let chatOverlay = document.getElementById('chatOverlay');
                    if (chatOverlay) {
                        chatOverlay.style.display = ttsChatHistorySwitch.checked ? 'block' : 'none';
                        console.log('chatOverlay显示状态设置为:', chatOverlay.style.display);
                    } else {
                        console.warn('chatOverlay元素未找到，尝试重新创建');
                        // 尝试重新创建chatOverlay
                        this.setupChatHistoryOverlay();
                        // 再次尝试获取
                        chatOverlay = document.getElementById('chatOverlay');
                        if (chatOverlay) {
                            chatOverlay.style.display = ttsChatHistorySwitch.checked ? 'block' : 'none';
                            console.log('重新创建后，chatOverlay显示状态设置为:', chatOverlay.style.display);
                        } else {
                            console.error('重新创建chatOverlay失败');
                        }
                    }
                }
            } else {
                console.warn('tts-chat-history-switch元素未找到');
            }

            let ttsChatSubmitBtn = document.getElementById('tts-chat-submit-button');
            if (ttsChatSubmitBtn) {
                ttsChatSubmitBtn.onclick = (event: Event) => {
                this.stopAudio();
                let value = (document.getElementById('tts-chat-area') as HTMLTextAreaElement)?.value.trim();
                if (value.length == 0) {
                    //alert('The input text is empty.');
                    return;
                }

                let ttsChatModelControl = document.getElementById('tts-chat-model-control') as HTMLSelectElement;
                let selectedModel = ttsChatModelControl ? ttsChatModelControl.value : '1';

                // 在后台添加字数限制指令，但不显示给用户
                let backendPrompt = value + "。请限制100字内回答,。";

                // 🆕 显示"正在思考中"到 Caption
                this.sendTextToCaption("正在思考中...", 10); // 显示10秒，足够AI回复

                // 如果选择的是通义千问，直接调用API
                if (selectedModel === TTS_CHATBOT_ID.TongyiQianwen.toString()) {
                    // 对通义千问也添加字数限制
                    this.callTongyiQianwenDirectly(backendPrompt, ttsVoiceControl.value, ttsSpeedNum.value);
                } else {
                    // 其他模型使用原来的后端逻辑，发送带限制的prompt
                    this.emitUIInteraction({ TextToSpeech: { voiceConfig: { voiceId: ttsVoiceControl.value, voiceSpeed: ttsSpeedNum.value }}});
                    this.emitUIInteraction({ Chatbot: { chatRequest: { prompt: backendPrompt }}});
                }

                // 在聊天界面只显示用户的原始输入，不显示字数限制指令
                let chatOverlay = document.getElementById('chatOverlay');
                if (chatOverlay) {
                    chatOverlay.innerHTML += '<span class="chatSpan"><img src="/images/question.png" style="height:1em;" />&nbsp;' + value + '</span>';
                    chatOverlay.scrollTop = chatOverlay.scrollHeight;
                }
                };
            } else {
                console.warn('tts-chat-submit-button元素未找到');
            }

            let ttsChatStopBtn = document.getElementById('tts-chat-stop-button');
            if (ttsChatStopBtn) {
                ttsChatStopBtn.onclick = (event: Event) => {
                    this.emitUIInteraction({ Audio: { reset: '' }});
                };
            } else {
                console.warn('tts-chat-stop-button元素未找到');
            }
        }

        let audioVolume = document.getElementById('audio-volume') as HTMLInputElement;
        if (audioVolume) {
            audioVolume.onchange = (event: Event) => {
                this.emitUIInteraction({ Audio: { volume: audioVolume.value }});
            };
        }

        /*******************************************************************************
         * Caption
         ******************************************************************************/
        let captionContainer = document.getElementById('caption-container');
        if (captionContainer) {
            let captionSwitch = document.getElementById('caption-switch') as HTMLInputElement;
            let captionClearBtn = document.getElementById('caption-clear-button');
            let captionAutoClearSwitch = document.getElementById('caption-auto-clear-switch') as HTMLInputElement;
            let captionMarginLeftNum = document.getElementById('caption-marginLeft') as HTMLInputElement;
            let captionMarginRightNum = document.getElementById('caption-marginRight') as HTMLInputElement;
            let captionMarginUpNum = document.getElementById('caption-marginUp') as HTMLInputElement;
            let captionMarginDownNum = document.getElementById('caption-marginDown') as HTMLInputElement;
            let captionPaddingNum = document.getElementById('caption-padding') as HTMLInputElement;
            captionSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { enabled: captionSwitch.checked ? 1 : 0 }});

                let ttsSecondTextArea = document.getElementById('tts-second-text-area') as HTMLInputElement;
                if (ttsSecondTextArea !== null) {
                    ttsSecondTextArea.disabled = !captionSwitch.checked;
                }
                let ttsSecondTextWarning = document.getElementById('tts-second-text-warning');
                if (ttsSecondTextWarning !== null) {
                    ttsSecondTextWarning.style.display = captionSwitch.checked ? 'none' : 'inline';
                }
                let captionCustomTextArea = document.getElementById('caption-custom-text-area') as HTMLInputElement;
                if (captionCustomTextArea !== null) {
                    captionCustomTextArea.disabled = !captionSwitch.checked;
                }
                let captionCustomTextWarning = document.getElementById('caption-custom-text-warning');
                if (captionCustomTextWarning !== null) {
                    captionCustomTextWarning.style.display = captionSwitch.checked ? 'none' : 'inline';
                }
            };

            captionClearBtn.onclick = (event: Event) => {
                this.emitUIInteraction({ Caption: { clearText: 1 }});
            };

            captionAutoClearSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { autoClear: captionAutoClearSwitch.checked ? 1 : 0 }});
            };

            captionMarginLeftNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { marginLeft: captionMarginLeftNum.value }});
            };

            captionMarginRightNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { marginRight: captionMarginRightNum.value }});
            };

            captionMarginUpNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { marginUp: captionMarginUpNum.value }});
            };

            captionMarginDownNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { marginDown: captionMarginDownNum.value }});
            };

            captionPaddingNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { padding: captionPaddingNum.value }});
            };

            let captionBGSwitch = document.getElementById('caption-bg-switch') as HTMLInputElement;
            let captionBGSelect = document.getElementById('caption-background-select') as HTMLSelectElement;
            let captionBgFile = document.getElementById('caption-bg-file') as HTMLInputElement;
            let captionBgFileText = document.getElementById('caption-bg-file-text');
            let captionBgFileButton = document.getElementById('caption-bg-file-button');
            let captionBgFileUploadButton = document.getElementById('caption-bg-file-upload');
            captionBGSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { showBG: captionBGSwitch.checked ? 1 : 0 }});
            };

            captionBGSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { image: captionBGSelect.value }});
            };

            captionBgFile.onchange = (event: Event) => {
                captionBgFileText.innerHTML = captionBgFile.value.split(/(\\|\/)/g).pop();
            };

            captionBgFileButton.onclick = (event: Event) => {
                captionBgFile.click();
            };

            captionBgFileUploadButton.onclick = (event: Event) => {
                if (captionBgFile.files.length > 0) {
                    let file = captionBgFile.files[0];
                    if (file.type.match('image/jpeg') || file.type.match('image/png')) {
                        try {
                            var formData = new FormData();
                            formData.append('captionBgFile', file);

                            var xhr = new XMLHttpRequest();
                            xhr.open('POST', '/upload/caption', true);
                            xhr.onreadystatechange = ((request: XMLHttpRequest, event: Event): any => {
                                if (xhr.readyState === 4) {
                                    if (xhr.status === 200) {
                                        alert('Upload Successful.');
                                        this.requestStreamerResponse();
                                    } else {
                                        alert('Upload Failure: ' + xhr.status + ' (' + xhr.statusText + ').');
                                    }
                                }
                            }).bind(this, xhr);
                            xhr.send(formData);
                        } catch (error) {
                            console.log(error);
                        }
                    } else {
                        alert('This in not a valid image file. Only JPG and PNG files are allowed.');
                    }
                }
            };

            let captionFontSelect = document.getElementById('caption-font-select') as HTMLSelectElement;
            let captionFontSizeNum = document.getElementById('caption-font-size') as HTMLInputElement;
            let captionColorButton = document.getElementById('caption-font-color-button');
            let captionColorPick = document.getElementById('caption-font-color-pick') as HTMLInputElement;
            captionFontSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { fontID: captionFontSelect.value }});
            };

            captionFontSizeNum.onchange = (event: Event) => {
                this.emitUIInteraction({ Caption: { fontSize: captionFontSizeNum.value }});
            };

            captionColorButton.onclick = (event: Event) => {
                captionColorPick.click();
            };

            captionColorPick.oninput = (event: Event) => {
                captionColorButton.style.backgroundColor = captionColorPick.value;
                let result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(captionColorPick.value);
                let colorR = parseInt(result[1], 16);
                let colorG = parseInt(result[2], 16);
                let colorB = parseInt(result[3], 16);
                this.emitUIInteraction({ Caption: { fontColor: colorR + ',' + colorG + ',' + colorB }});
            };

            let captionCustomTextSubmitBtn = document.getElementById('caption-custom-text-submit-button');
            let captionCustomTextArea = document.getElementById('caption-custom-text-area') as HTMLInputElement;
            let captionCustomTextTime = document.getElementById('caption-custom-text-time') as HTMLInputElement;
            captionCustomTextSubmitBtn.onclick = (event: Event) => {
                this.emitUIInteraction({ Caption: { custom: { text: captionCustomTextArea.value.trim(), time: captionCustomTextTime.value  }}});
            };
        }

        /*******************************************************************************
         * LipSync
         ******************************************************************************/
        let lipsyncCheckbox = document.getElementById('lipsync-tgl') as HTMLInputElement;
        if (lipsyncCheckbox !== null) {
            lipsyncCheckbox.onchange = (event: Event) => {
                this.emitUIInteraction({ LipSync: { enabled: lipsyncCheckbox.checked ? 1 : 0 }});
            }
        }

        let audioOffsetNum = document.getElementById('lipsync-audio-offset-num') as HTMLInputElement;
        if (audioOffsetNum !== null) {
            audioOffsetNum.onchange = (event: Event) => {
                this.emitUIInteraction({ LipSync: { audioOffset: audioOffsetNum.value }});
            };
        }

        let bsSourceSelect = document.getElementById('bs-source-select') as HTMLSelectElement;
        if (bsSourceSelect !== null) {
            bsSourceSelect.onchange = (event: Event) => {
                this.stopAudio();

                let stapState = document.getElementById('stap-state');
                let stapModel = document.getElementById('stap-embedded-model') as HTMLSelectElement;
                let stapServerIP = document.getElementById('stapServerIP') as HTMLInputElement;
                let stapServerPort = document.getElementById('stapServerPort') as HTMLInputElement;
                if (stapState !== null) {
                    while (stapState.classList.length > 0) {
                        stapState.classList.remove(stapState.classList.item(0));
                    }
                    if (bsSourceSelect.value == '2') {
                        stapState.classList.add('spinner');
                    } else {
                        stapState.classList.add('circle-grey');
                    }
                    stapState.style.display = bsSourceSelect.value == '2' ? null : 'none';
                }

                stapModel.disabled = (bsSourceSelect.value != '0' && bsSourceSelect.value != '3');
                stapServerIP.disabled = (bsSourceSelect.value != '0');
                stapServerPort.disabled = (bsSourceSelect.value != '0');

                this.emitUIInteraction({ STAP: { serverEndpoint: stapServerIP.value + ':' + stapServerPort.value }});
                this.emitUIInteraction({ LipSync: { bsSource: bsSourceSelect.value }});
            };
        }

        let bsSmoothingModeSelect = document.getElementById('bs-smooth-mode-select') as HTMLSelectElement;
        if (bsSmoothingModeSelect !== null) {
            bsSmoothingModeSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ LipSync: { bsSmoothingMode: bsSmoothingModeSelect.value }});
            }
        }
        let bsSmoothingFramesize = document.getElementById('bs-smooth-framesize') as HTMLInputElement;
        if (bsSmoothingFramesize !== null) {
            bsSmoothingFramesize.onchange = (event: Event) => {
                this.emitUIInteraction({ LipSync: { bsSmoothingFrameSize: bsSmoothingFramesize.value }});
            }
        }
        
        let bsSmoothingAlpha = document.getElementById('bs-smooth-alpha') as HTMLInputElement;
        if (bsSmoothingAlpha !== null) {
            bsSmoothingAlpha.onchange = (event: Event) => {
                this.emitUIInteraction({ LipSync: { bsSmoothingAlpha: bsSmoothingAlpha.value }});
            }
        }

        let mhcMinDelayNum = document.getElementById('mhc-min-delay') as HTMLInputElement;
        if (mhcMinDelayNum !== null) {
            mhcMinDelayNum.onchange = (event: Event) => {
                this.emitUIInteraction({ MHCTalker: { minDelay: mhcMinDelayNum.value }});
            };
        }

        let stapModel = document.getElementById('stap-embedded-model') as HTMLSelectElement;
        if (stapModel !== null) {
            stapModel.onchange = (event: Event) => {
                this.emitUIInteraction({ STAP: { modelID: stapModel.value }});
            };
        }

        /*******************************************************************************
         * Record
         ******************************************************************************/
        let recordFramerateNum = document.getElementById('record-framerate') as HTMLInputElement;
        if (recordFramerateNum !== null) {
            recordFramerateNum.onchange = (event: Event) => {
                this.emitUIInteraction({ VideoRecord: { frameRate: recordFramerateNum.value }});
            };
        }

        let downloadRecordCheckbox = document.getElementById('download-record-checkbox') as HTMLInputElement;
        if (downloadRecordCheckbox !== null) {
            downloadRecordCheckbox.onchange = (event: Event) => {
                this.emitUIInteraction({ VideoRecord: { uploadFileToS3: downloadRecordCheckbox.checked ? 1 : 0 }});
            };
        }

        let startRecordButton    = document.getElementById('start-record-button') as HTMLInputElement;
        let stopRecordButton     = document.getElementById('stop-record-button') as HTMLInputElement;
        let startPngRecordButton = document.getElementById('start-png-record-button') as HTMLInputElement;
        let stopPngRecordButton  = document.getElementById('stop-png-record-button') as HTMLInputElement;
        if (startRecordButton !== null && stopRecordButton !== null && startPngRecordButton !== null && stopPngRecordButton !== null) {
            startRecordButton.onclick = (event: Event) => {
                stopRecordButton.disabled = false;
                startRecordButton.disabled = true;
                let alphaValue = (document.getElementById('alpha-record-checkbox') as HTMLInputElement)?.checked ? 1 : 0;
                let watermarkValue = (document.getElementById('watermark-record-checkbox') as HTMLInputElement)?.checked ? 1 : 0;
                this.emitUIInteraction({ VideoRecord: { record: 1, filename: 'Example', withAlpha: alphaValue, withWatermark: watermarkValue }});
            };

            stopRecordButton.onclick = (event: Event) => {

                stopRecordButton.disabled = true;
                this.emitUIInteraction({ VideoRecord: { record: 0 }});
            };

            startPngRecordButton.onclick = (event: Event) => {
                stopPngRecordButton.disabled = false;
                startPngRecordButton.disabled = true;
                let alphaValue = (document.getElementById('alpha-record-checkbox') as HTMLInputElement)?.checked ? 1 : 0;
                let watermarkValue = (document.getElementById('watermark-record-checkbox') as HTMLInputElement)?.checked ? 1 : 0;
                this.emitUIInteraction({ PNGSave: { enable: 1, withAlpha: alphaValue, withWatermark: watermarkValue }});
            };

            stopPngRecordButton.onclick = (event: Event) => {
                startPngRecordButton.disabled = false;
                stopPngRecordButton.disabled = true;
                this.emitUIInteraction({ PNGSave: { enable: 0 }});
            };
        }

        /****MRQ Start***/
        let performanceCaptureButton = document.getElementById('performance-capture-button') as HTMLInputElement;
        let stopPerformancRecordButton  = document.getElementById('stop-performancerecord-button') as HTMLInputElement;
        if (performanceCaptureButton !== null && stopPerformancRecordButton !== null) {
            performanceCaptureButton.onclick = (event: Event) => {
                let MRQtimecodeFile = document.getElementById('MRQtimecode-file') as HTMLInputElement;
                if (MRQtimecodeFile !== null && MRQtimecodeFile.files.length) {
                    stopPerformancRecordButton.disabled = false;
                    performanceCaptureButton.disabled = true;
                    let objectURL = URL.createObjectURL(MRQtimecodeFile.files[0]);
                    this.performanceCapturetimecodePlay(objectURL);
                    URL.revokeObjectURL(objectURL);
                    this.emitUIInteraction({ TriggerTTSRecord: { enable: 1 }});
                 }
            };

             stopPerformancRecordButton.onclick = (event: Event) => {
                performanceCaptureButton.disabled = false;
                stopPerformancRecordButton.disabled = true;
                this.emitUIInteraction({ TriggerTTSRecord: { enable: 0 }});
            };
        }

        let startRenderingButton = document.getElementById('start-render-button') as HTMLInputElement;
        if (startRenderingButton !== null) {
            startRenderingButton.onclick = (event: Event) => {
                let MRQModeSelect = document.getElementById('MRQMode-select') as HTMLSelectElement;
                if (MRQModeSelect !== null) {
                    if (Number(MRQModeSelect.value) == 0) {
                        this.emitUIInteraction({ TriggerMRQ: { enable: 1 }});
                    }
                    else if (Number(MRQModeSelect.value) == 1) {
                        let MRQtimecodeFile = document.getElementById('MRQtimecode-file') as HTMLInputElement;
                        if (MRQtimecodeFile !== null && MRQtimecodeFile.files.length) {
                            let objectURL = URL.createObjectURL(MRQtimecodeFile.files[0]);
                            this.MRQtimecodePlay(objectURL);
                            URL.revokeObjectURL(objectURL);
                            this.emitUIInteraction({ TriggerMRQ: { enable: 1 }});
                        }
                    }
                }
            };
        }

        let renderTime = document.getElementById('render-time') as HTMLInputElement;
        if (renderTime !== null) {
            renderTime.onchange = (event: Event) => {
                this.emitUIInteraction({ MRQ: { Duration: renderTime.value }});
            };
        }

        let MRQConfigSelect = document.getElementById('MRQConfig-select') as HTMLSelectElement;
        if (MRQConfigSelect !== null) {
            MRQConfigSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ MRQ: { Config: MRQConfigSelect.value }});
            };
        }
        /****MRQ End***/

        /*******************************************************************************
         * Mocap
         ******************************************************************************/
        let operationSelect = document.getElementById('operation-select') as HTMLSelectElement;
        if (operationSelect !== null) {
            operationSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ MotionSource: { id: operationSelect.value }});
            };
        }

        let arkitCorrectionSwitch = document.getElementById('arkit-correction-switch') as HTMLInputElement;
        if (arkitCorrectionSwitch !== null) {
            arkitCorrectionSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ ARKit: { correctionEnabled: arkitCorrectionSwitch.checked ? 1 : 0 }});
            };
        }

        let arkitHeadRotationSwitch = document.getElementById('arkit-head-rotation-switch') as HTMLInputElement;
        if (arkitHeadRotationSwitch !== null) {
            arkitHeadRotationSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ ARKit: { headRotationEnabled: arkitHeadRotationSwitch.checked ? 1 : 0 }});
            };
        }

        let boundScaleNum = document.getElementById('bound-scale') as HTMLInputElement;
        if (boundScaleNum !== null) {
            boundScaleNum.onchange = (event: Event) => {
                this.emitUIInteraction({ BoundScale: { value: boundScaleNum.value }});
            };
        }

        /*******************************************************************************
         * Streaming Output
         ******************************************************************************/
        let ndiOutputSwitch = document.getElementById('ndi-output-switch') as HTMLInputElement;
        if (ndiOutputSwitch !== null) {
            ndiOutputSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ NDIOutput: { standardChannel: ndiOutputSwitch.checked ? 1 : 0, alpha: 0 }});
            };
        }

        let ndiOutputAlphaBGSwitch = document.getElementById('ndi-output-switch-alpha-bg') as HTMLInputElement;
        if (ndiOutputAlphaBGSwitch !== null) {
            ndiOutputAlphaBGSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ NDIOutput: { standardChannel: ndiOutputAlphaBGSwitch.checked ? 1 : 0, alpha: 1 }});
            };
        }

        let ndiOutputOWLSwitch = document.getElementById('ndi-output-owl-switch') as HTMLInputElement;
        if (ndiOutputOWLSwitch !== null) {
            ndiOutputOWLSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ NDIOutput: { OWLChannel: ndiOutputOWLSwitch.checked ? 1 : 0, alpha: 0}});
            };
        }

        let ndiOutputOWLAlphaBGSwitch = document.getElementById('ndi-output-owl-switch-alpha-bg') as HTMLInputElement;
        if (ndiOutputOWLAlphaBGSwitch !== null) {
            ndiOutputOWLAlphaBGSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ NDIOutput: { OWLChannel: ndiOutputOWLAlphaBGSwitch.checked ? 1 : 0, alpha: 1}});
            };
        }

        let spoutOutputSwitch = document.getElementById('spout-output-switch') as HTMLInputElement;
        if (spoutOutputSwitch !== null) {
            spoutOutputSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ SpoutOutput: { enabled: spoutOutputSwitch.checked ? 1 : 0, alpha: 0 }});
            };
        }

        let spoutOutputAlphaBGSwitch = document.getElementById('spout-output-switch-alpha-bg') as HTMLInputElement;
        if (spoutOutputAlphaBGSwitch !== null) {
            spoutOutputAlphaBGSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ SpoutOutput: { enabled: spoutOutputAlphaBGSwitch.checked ? 1 : 0, alpha: 1 }});
            };
        }

        /*******************************************************************************
         * Advance
         ******************************************************************************/
        let keyboardInputCheckbox = document.getElementById('keyboard-input-tgl') as HTMLInputElement;
        if (keyboardInputCheckbox !== null) {
            // init
            keyboardInputCheckbox.checked = this._pixelStreaming.config.isFlagEnabled(Flags.KeyboardInput);

            keyboardInputCheckbox.onchange = (event: Event) => {
                this._pixelStreaming.config.setFlagEnabled(Flags.KeyboardInput, keyboardInputCheckbox.checked);
            };
        }

        let handleMouseMove: (event: MouseEvent) => void;
        let handleMouseDown: (event: MouseEvent) => void;
        let handleMouseUp: (event: MouseEvent) => void;
        let isThrottled = false;
        let mouseInputCheckbox = document.getElementById('mouse-input-tgl') as HTMLInputElement;
        if (mouseInputCheckbox !== null) {
            mouseInputCheckbox.onchange = (event: Event) => {
                this.emitUIInteraction({ MouseInput: { enabled: mouseInputCheckbox.checked ? 1 : 0 }});

                if (mouseInputCheckbox.checked) {

                    handleMouseMove = (event: MouseEvent) => {
                        if (!isThrottled) {
                            this.emitUIInteraction({
                                MouseInput: {
                                    action: 'mouseMove',
                                    x: event.movementX,
                                    y: event.movementY,
                                }
                            });

                            isThrottled = true;
                            setTimeout(() => {
                                isThrottled = false;
                            }, 100);
                        }
                    };

                    handleMouseDown = (event: MouseEvent) => {
                        this.emitUIInteraction({
                            MouseInput: {
                                action: 'mouseDown',
                                button: event.button,
                                x: event.clientX,
                                y: event.clientY,
                            }
                        });
                    };

                    handleMouseUp = (event: MouseEvent) => {
                        this.emitUIInteraction({
                            MouseInput: {
                                action: 'mouseUp',
                                button: event.button,
                                x: event.clientX,
                                y: event.clientY,
                            }
                        });
                    };

                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mousedown', handleMouseDown);
                    document.addEventListener('mouseup', handleMouseUp);
                } else {

                    if (handleMouseMove) {
                        document.removeEventListener('mousemove', handleMouseMove);
                    }
                    if (handleMouseDown) {
                        document.removeEventListener('mousedown', handleMouseDown);
                    }
                    if (handleMouseUp) {
                        document.removeEventListener('mouseup', handleMouseUp);
                    }
                }
            }
        }



        let maxFpsNum = document.getElementById('max-fps') as HTMLInputElement;
        if (maxFpsNum !== null) {
            maxFpsNum.onchange = (event: Event) => {
                this.emitUIInteraction({ MaxFPS: { value: maxFpsNum.value }});
            };
        }

        let sideBySideDepthContainer = document.getElementById('sideBySideDepth-container');
        if (sideBySideDepthContainer) {
            let sideBySideDepthSwitch = document.getElementById('sideBySideDepth-switch') as HTMLInputElement;
            sideBySideDepthSwitch.onchange = (event: Event) => {
                this.emitUIInteraction({ SideBySideDepth: { enable: sideBySideDepthSwitch.checked ? 1 : 0 }});

                if (sideBySideDepthSwitch.checked) {
                    let backgroundModelSelect = document.getElementById('background-mode-select') as HTMLInputElement;
                    if (backgroundModelSelect !== null) {
                        backgroundModelSelect.value = 'Composure';
                        backgroundModelSelect.disabled = true;
                        this.emitUIInteraction({ Background: { backgroundMode: backgroundModelSelect.value }});
                    }
                } else {
                    backgroundModelSelect.disabled = false;
                }
            };

            let depthSpaceStart = document.getElementById('sideBySideDepth-depthSpaceStart-text') as HTMLInputElement;
            let depthSpaceScale = document.getElementById('sideBySideDepth-depthSpaceScale-text') as HTMLInputElement;
            let depthSpaceColorOffset = document.getElementById('sideBySideDepth-depthSpaceColorOffset-text') as HTMLInputElement;
            let grayscaleClampAvatarMin = document.getElementById('sideBySideDepth-grayscaleClampAvatarMin-text') as HTMLInputElement;
            let grayscaleClampAvatarMax = document.getElementById('sideBySideDepth-grayscaleClampAvatarMax-text') as HTMLInputElement;
            let grayscaleClampBackground = document.getElementById('sideBySideDepth-grayscaleClampBackground-text') as HTMLInputElement;

            let sideBySideDepthChange = () => {
                this.emitUIInteraction({ SideBySideDepth: { depthSpaceStart: depthSpaceStart.value, depthSpaceScale: depthSpaceScale.value, depthSpaceColorOffset: depthSpaceColorOffset.value,
                    grayscaleClampAvatarMin: grayscaleClampAvatarMin.value, grayscaleClampAvatarMax: grayscaleClampAvatarMax.value, grayscaleClampBackground: grayscaleClampBackground.value }});
            };

            depthSpaceStart.onchange          = (event: Event) => { sideBySideDepthChange(); }
            depthSpaceScale.onchange          = (event: Event) => { sideBySideDepthChange(); }
            depthSpaceColorOffset.onchange    = (event: Event) => { sideBySideDepthChange(); }
            grayscaleClampAvatarMin.onchange  = (event: Event) => { sideBySideDepthChange(); }
            grayscaleClampAvatarMax.onchange  = (event: Event) => { sideBySideDepthChange(); }
            grayscaleClampBackground.onchange = (event: Event) => { sideBySideDepthChange(); }
        }

        let chairSelect = document.getElementById('chair-select') as HTMLSelectElement;
        if (chairSelect !== null) {
            chairSelect.onchange = (event: Event) => {
                this.emitUIInteraction({ Chair: { id: chairSelect.value }});
            };
        }

        let scalabilityResetButton = document.getElementById('scalability-reset-button');
        let scalabilityAutoButton = document.getElementById('scalability-auto-button');
        let scalabilityAntiAliasing = document.getElementById('scalability-AntiAliasing') as HTMLSelectElement;
        let scalabilityPostProcess = document.getElementById('scalability-PostProcess') as HTMLSelectElement;
        let scalabilityShadow = document.getElementById('scalability-Shadow') as HTMLSelectElement;
        let scalabilityTexture = document.getElementById('scalability-Texture') as HTMLSelectElement;
        let scalabilityEffects = document.getElementById('scalability-Effects') as HTMLSelectElement;
        if (scalabilityResetButton !== null && scalabilityAutoButton !== null &&
            scalabilityAntiAliasing !== null && scalabilityPostProcess !== null && scalabilityShadow !== null &&
            scalabilityTexture !== null && scalabilityEffects !== null) {
            scalabilityResetButton.onclick = (event: Event) => {
                this.emitUIInteraction({ Scalability: { Reset: 1 }});
                scalabilityAntiAliasing.value = '4';
                scalabilityPostProcess.value = '4';
                scalabilityShadow.value = '4';
                scalabilityTexture.value = '4';
                scalabilityEffects.value = '4';
            }

            scalabilityAutoButton.onclick = (event: Event) => {
                this.emitUIInteraction({ Scalability: { Auto: 1 }});
            }

            scalabilityAntiAliasing.onchange = (event: Event) => {
                this.emitUIInteraction({ Scalability: { AntiAliasing: scalabilityAntiAliasing.value }});
            }

            scalabilityPostProcess.onchange = (event: Event) => {
                this.emitUIInteraction({ Scalability: { PostProcess: scalabilityPostProcess.value }});
            }

            scalabilityShadow.onchange = (event: Event) => {
                this.emitUIInteraction({ Scalability: { Shadow: scalabilityShadow.value }});
            }

            scalabilityTexture.onchange = (event: Event) => {
                this.emitUIInteraction({ Scalability: { Texture: scalabilityTexture.value }});
            }

            scalabilityEffects.onchange = (event: Event) => {
                this.emitUIInteraction({ Scalability: { Effects: scalabilityEffects.value }});
            }
        }

        let foregroundContainer = document.getElementById('foreground-container');
        if (foregroundContainer) {
            let foregroundAddButton = document.getElementById('foreground-add-button');
            foregroundAddButton.onclick = (event: Event) => {

                let foregroundSizeX = document.getElementById('foreground-sizeX') as HTMLInputElement;
                let foregroundSizeY = document.getElementById('foreground-sizeY') as HTMLInputElement;
                let foregroundPositionX = document.getElementById('foreground-positionX') as HTMLInputElement;
                let foregroundPositionY = document.getElementById('foreground-positionY') as HTMLInputElement;
                let foregroundImage = document.getElementById('foreground-image-select') as HTMLInputElement;
                let foregroundSelect = document.getElementById('foreground-id-select') as HTMLSelectElement;
                let foregroundOrder = document.getElementById('foreground-order') as HTMLSelectElement;
                let foregroundAddID = foregroundImage.value;
                let isDuplicate = false;
                let suffix = 1;
                let newID = foregroundAddID;
                while (true) {
                    for (let i = 0; i < foregroundSelect.options.length; i++) {
                        if (foregroundSelect.options[i].value === newID) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    if (!isDuplicate) {
                        break;
                    }
                    newID = foregroundAddID + suffix;
                    suffix++;
                    isDuplicate = false;
                }

                this.emitUIInteraction({ Foreground: { action: 'add', id: newID, sizeX: foregroundSizeX.value, sizeY: foregroundSizeY.value, positionX: foregroundPositionX.value, positionY: foregroundPositionY.value, image: foregroundImage.value, order: foregroundOrder.value }});

                let option = document.createElement('option');
                option.text = newID;
                option.value = newID;
                foregroundSelect.add(option);

            }

            let foregroundBlendtime = document.getElementById('foreground-blendTime-num') as HTMLInputElement;
            if (foregroundBlendtime !== null) {
                foregroundBlendtime.onchange = (event: Event) => {
                    this.emitUIInteraction({ Foreground: { Blendtime: foregroundBlendtime.value }});
                }
            }

            let foregroundMovespeed = document.getElementById('foreground-moveSpeed-num') as HTMLInputElement;
            if (foregroundMovespeed !== null) {
                foregroundMovespeed.onchange = (event: Event) => {
                    this.emitUIInteraction({ Foreground: { Movespeed: foregroundMovespeed.value }});
                }
            }

            let foregroundRemoveButton = document.getElementById('foreground-remove-button');
            if (foregroundRemoveButton !== null) {
                foregroundRemoveButton.onclick = (event: Event) => {
                    let foregroundSelect = document.getElementById('foreground-id-select') as HTMLSelectElement;
                    let id = foregroundSelect.value;
                    this.emitUIInteraction({ Foreground: { action: 'remove', id: id }});

                    for (let i = 0; i < foregroundSelect.options.length; i++) {
                        if (foregroundSelect.options[i].value == id) {
                            foregroundSelect.remove(i);
                            break;
                        }
                    }

                    this.emitUIInteraction({ Foreground: { action: 'query', id: foregroundSelect.value }});
                }
            }

            let foregroundShowButton = document.getElementById('foreground-show-button');
            if (foregroundShowButton !== null) {
                foregroundShowButton.onclick = (event: Event) => {
                    let foregroundSelect = document.getElementById('foreground-id-select') as HTMLSelectElement;
                    let id = foregroundSelect.value;
                    this.emitUIInteraction({ Foreground: { action: 'show', id: id }});
                }
            }

            let foregroundHideButton = document.getElementById('foreground-hide-button');
            if (foregroundHideButton !== null) {
                foregroundHideButton.onclick = (event: Event) => {
                    let foregroundSelect = document.getElementById('foreground-id-select') as HTMLSelectElement;
                    let id = foregroundSelect.value;
                    this.emitUIInteraction({ Foreground: { action: 'hide', id: id }});
                }
            }

            let foregroundRemoveAllButton = document.getElementById('foreground-remove-all-button');
            if (foregroundRemoveAllButton !== null) {
                foregroundRemoveAllButton.onclick = (event: Event) => {
                    this.emitUIInteraction({ Foreground: { action: 'removeAll' }});

                    let foregroundSelect = document.getElementById('foreground-id-select') as HTMLSelectElement;
                    while (foregroundSelect.options.length > 0) {
                        foregroundSelect.remove(0);
                    }
                }
            }

            let foregroundModifyID = document.getElementById('foreground-id-select') as HTMLSelectElement;
            let foregroundSizeX = document.getElementById('foreground-sizeX') as HTMLInputElement;
            let foregroundSizeY = document.getElementById('foreground-sizeY') as HTMLInputElement;
            let foregroundPositionX = document.getElementById('foreground-positionX') as HTMLInputElement;
            let foregroundPositionY = document.getElementById('foreground-positionY') as HTMLInputElement;
            let foregroundOrder = document.getElementById('foreground-order') as HTMLSelectElement;

            foregroundModifyID.onchange = (event: Event) => {
                this.emitUIInteraction({ Foreground: { action: 'query', id: foregroundModifyID.value }});
            }

            foregroundSizeX.onchange = (event: Event) => {
                this.emitUIInteraction({ Foreground: { action: 'modify', id: foregroundModifyID.value, sizeX: foregroundSizeX.value, sizeY: foregroundSizeY.value, positionX: foregroundPositionX.value, positionY: foregroundPositionY.value, order: foregroundOrder.value }});
            }

            foregroundSizeY.onchange = (event: Event) => {
                this.emitUIInteraction({ Foreground: { action: 'modify', id: foregroundModifyID.value, sizeX: foregroundSizeX.value, sizeY: foregroundSizeY.value, positionX: foregroundPositionX.value, positionY: foregroundPositionY.value, order: foregroundOrder.value }});
            }

            foregroundPositionX.onchange = (event: Event) => {
                this.emitUIInteraction({ Foreground: { action: 'modify', id: foregroundModifyID.value, sizeX: foregroundSizeX.value, sizeY: foregroundSizeY.value, positionX: foregroundPositionX.value, positionY: foregroundPositionY.value, order: foregroundOrder.value }});
            }

            foregroundPositionY.onchange = (event: Event) => {
                this.emitUIInteraction({ Foreground: { action: 'modify', id: foregroundModifyID.value, sizeX: foregroundSizeX.value, sizeY: foregroundSizeY.value, positionX: foregroundPositionX.value, positionY: foregroundPositionY.value, order: foregroundOrder.value }});
            }

            foregroundOrder.onchange = (event: Event) => {
                this.emitUIInteraction({ Foreground: { action: 'modify', id: foregroundModifyID.value, sizeX: foregroundSizeX.value, sizeY: foregroundSizeY.value, positionX: foregroundPositionX.value, positionY: foregroundPositionY.value, order: foregroundOrder.value }});
            }

            let foregroundFile = document.getElementById('foreground-file') as HTMLInputElement;
            let foregroundFileText = document.getElementById('foreground-file-text');
            let foregroundFileButton = document.getElementById('foreground-file-button');
            let foregroundFileUploadButton = document.getElementById('foreground-file-upload');
            if (foregroundFile !== null && foregroundFileText !== null && foregroundFileButton !== null && foregroundFileUploadButton !== null) {
                foregroundFile.onchange = (event: Event) => {
                    foregroundFileText.innerHTML = foregroundFile.value.split(/(\\|\/)/g).pop();
                };

                foregroundFileButton.onclick = (event: Event) => {
                    foregroundFile.click();
                };

                foregroundFileUploadButton.onclick = (event: Event) => {
                    if (foregroundFile.files.length > 0) {
                        let file = foregroundFile.files[0];
                        if (file.type.match('image/jpeg') || file.type.match('image/png')) {
                            try {
                                var formData = new FormData();
                                formData.append('foregroundFile', file);

                                var xhr = new XMLHttpRequest();
                                xhr.open('POST', '/upload/foreground', true);
                                xhr.onreadystatechange = ((request: XMLHttpRequest, event: Event): any => {
                                    if (xhr.readyState === 4) {
                                        if (xhr.status === 200) {
                                            alert('Upload Successful.');
                                            this.requestStreamerResponse();
                                        } else {
                                            alert('Upload Failure: ' + xhr.status + ' (' + xhr.statusText + ').');
                                        }
                                    }
                                }).bind(this, xhr);
                                xhr.send(formData);
                            } catch (error) {
                                console.log(error);
                            }
                        } else {
                            alert('This in not a valid image file. Only JPG and PNG files are allowed.');
                        }
                    }
                };
            }
        }

        console.log('✅ HTML事件设置完成');
    }

    beforeunload() {
        /*let kickButton = document.getElementById('kick-other-players-button');
        if (kickButton) {
            kickButton.onclick(null);
        }*/
    }

    requestStreamerResponse() {
        this.emitUIInteraction({ ResponseRequest: 'all' });
    }

    sortSelect(selElem: HTMLSelectElement) {
        let selValue = selElem.value;
        let arr = new Array();
        for (let i = 0; i < selElem.options.length; i++) {
            arr[i] = selElem.options[i];
        }

        arr.sort(function(a,b) {
            return (a.text > b.text) ? 1 : ((a.text < b.text) ? -1 : 0);
        });

        for (let i = 0; i < arr.length; i++) {
            selElem.options[i] = arr[i];
        }
        selElem.value = selValue;
    }

    joystickStartDrag(event: MouseEvent) {
        let joystickHandle = document.getElementById('joystickHandle');
        this.joystickOffset = {
            x: joystickHandle.offsetLeft - event.clientX,
            y: joystickHandle.offsetTop - event.clientY
        };
        this.joystickIsDragging = true;
    }

    joystickEndDrag(event: MouseEvent) {
        if (this.joystickIsDragging) {
            // clear text selection
            let sel = window.getSelection() ? window.getSelection() : document.getSelection();
            if (sel) {
                if (sel.removeAllRanges) {
                    sel.removeAllRanges();
                } else if (sel.empty) {
                    sel.empty();
                }
            }

            this.joystickIsDragging = false;
            // return to center
            let joystickHandle = document.getElementById('joystickHandle');
            joystickHandle.style.left = '50%';
            joystickHandle.style.top = '50%';

            this.emitUIInteraction({ FreeCamMove: 'Right_0' });
            this.emitUIInteraction({ FreeCamMove: 'Left_0' });
            this.emitUIInteraction({ FreeCamMove: 'Down_0' });
            this.emitUIInteraction({ FreeCamMove: 'Up_0' });
        }
    }

    joystickDragging(event: MouseEvent) {
        if (this.joystickIsDragging) {
            let joystickContainer = document.getElementById('joystickContainer');
            let joystickFrame = document.getElementById('joystickFrame');
            let joystickHandle = document.getElementById('joystickHandle');
            let xCenter = joystickContainer.offsetWidth / 2;
            let yCenter = joystickContainer.offsetHeight / 2;
            let xMax = xCenter - joystickHandle.offsetWidth / 2;
            let yMax = yCenter - joystickHandle.offsetHeight / 2;
            let xThreshold = joystickFrame.offsetWidth / 3;
            let yThreshold = joystickFrame.offsetHeight / 3;

            let x = event.clientX + this.joystickOffset.x - xCenter;
            let y = event.clientY + this.joystickOffset.y - yCenter;
            let distance = Math.sqrt(x * x + y * y);
            if (distance > xCenter) {
                x *= xCenter / distance;
                y *= yCenter / distance;
            }
            x = Math.max(Math.min(x, xMax), -xMax);
            y = Math.max(Math.min(y, yMax), -yMax);

            if (x > xThreshold) {
                this.emitUIInteraction({ FreeCamMove: 'Left_1' });
            } else if (x < -xThreshold) {
                this.emitUIInteraction({ FreeCamMove: 'Right_1' });
            } else {
                this.emitUIInteraction({ FreeCamMove: 'Right_0' });
                this.emitUIInteraction({ FreeCamMove: 'Left_0' });
            }

            if (y > yThreshold) {
                this.emitUIInteraction({ FreeCamMove: 'Up_1' });
            } else if (y < -yThreshold) {
                this.emitUIInteraction({ FreeCamMove: 'Down_1' });
            } else {
                this.emitUIInteraction({ FreeCamMove: 'Down_0' });
                this.emitUIInteraction({ FreeCamMove: 'Up_0' });
            }

            joystickHandle.style.left = `${xCenter + x}px`;
            joystickHandle.style.top  = `${yCenter + y}px`;
        }
    }

    setupChatHistoryOverlay() {
        console.log('开始创建chatOverlay');
        let playerElement = document.getElementById('playercontainer');
        console.log('playercontainer元素:', playerElement);

        if (playerElement) {
            // 检查是否已经存在chatOverlay
            let existingOverlay = document.getElementById('chatOverlay');
            if (existingOverlay) {
                console.log('chatOverlay已存在，跳过创建');
                return;
            }

            let chatOverlay = document.createElement('div');
            chatOverlay.id = 'chatOverlay';
            chatOverlay.style.display = 'none';
            chatOverlay.style.zIndex = '30';
            chatOverlay.style.position = 'absolute';
            chatOverlay.style.width = '30%';
            chatOverlay.style.height = '100%';
            chatOverlay.style.backgroundColor = 'rgba(100, 100, 100, 0.7)';
            chatOverlay.style.overflowY = 'scroll';
            chatOverlay.style.padding = '10px';
            chatOverlay.style.boxSizing = 'border-box';
            chatOverlay.style.top = '0';
            chatOverlay.style.left = '0';

            playerElement.appendChild(chatOverlay);
            console.log('chatOverlay创建成功，元素:', chatOverlay);

            // 验证是否能通过ID找到
            let verifyOverlay = document.getElementById('chatOverlay');
            console.log('验证创建的chatOverlay:', verifyOverlay);
        } else {
            console.warn('playercontainer元素未找到，无法创建chatOverlay');
            // 列出所有可能的容器元素
            console.log('当前页面中的容器元素:');
            console.log('- playercontainer:', document.getElementById('playercontainer'));
            console.log('- player:', document.getElementById('player'));
            console.log('- stream-container:', document.getElementById('stream-container'));
        }
    }

    setupSpeechRecognition() {
        console.log('🎤 正在初始化语音识别...');
        console.log('webkitSpeechRecognition支持:', 'webkitSpeechRecognition' in window);
        console.log('SpeechRecognition支持:', 'SpeechRecognition' in window);
        console.log('当前协议:', window.location.protocol);
        console.log('当前域名:', window.location.hostname);
        console.log('用户代理:', navigator.userAgent);
        console.log('vendor:', navigator.vendor);

        // 增强的浏览器检测
        const isChrome = /Chrome/.test(navigator.userAgent) && /Google Inc/.test(navigator.vendor);
        const isEdge = /Edg/.test(navigator.userAgent);
        const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
        const isFirefox = /Firefox/.test(navigator.userAgent);

        console.log('🔍 浏览器检测结果:', { isChrome, isEdge, isSafari, isFirefox });

        // 检查多种语音识别API
        const SpeechRecognition = (<any>window).SpeechRecognition || (<any>window).webkitSpeechRecognition;

        console.log('📡 SpeechRecognition API可用性:', !!SpeechRecognition);
        console.log('📡 SpeechRecognition构造函数:', SpeechRecognition);

        // 放宽浏览器限制，先尝试创建语音识别对象
        if (SpeechRecognition) {
            try {
                console.log('🔧 尝试创建语音识别对象...');
                this.speechRecognition = new SpeechRecognition();
                this.speechRecognition.continuous = true; // Set continuous identification mode
                this.speechRecognition.interimResults = true; // Set the result first in the output.
                console.log('✅ 语音识别对象创建成功');
                console.log('🎯 语音识别对象详情:', this.speechRecognition);

                // 检查浏览器兼容性警告
                if (!isChrome && !isEdge) {
                    console.warn('⚠️ 当前浏览器可能不完全支持语音识别功能，建议使用Chrome或Edge浏览器');
                }
            } catch (error) {
                console.error('❌ 创建语音识别对象失败:', error);
                this.speechRecognition = null;
                this.showSpeechRecognitionError('语音识别初始化失败: ' + error.message);
                return;
            }

            this.speechRecognition.onstart = (event: Event) => {
                this.final_transcript = "";
                this.isRecognizing = true;
                let micButton = document.getElementById('tts-chat-mic-button');
                if (micButton) {
                    micButton.style.backgroundImage = 'url("/images/mic-anim.gif")';
                }
            };

            this.speechRecognition.onend = (event: Event) => {
                this.isRecognizing = false;
                let micButton = document.getElementById('tts-chat-mic-button');
                if (micButton) {
                    micButton.style.backgroundImage = 'url("/images/mic.gif")';
                }
                let submitBtn = document.getElementById('tts-chat-submit-button');
                if (submitBtn !== null) {
                    submitBtn.click();
                }
            };

            this.speechRecognition.onerror = (event: any) => {
                // 替换alert为更友好的错误提示
                console.error(`Speech recognition error detected: ${event.error}`);
                
                // 创建一个临时错误提示元素，而不是使用alert
                const errorMessage = document.createElement('div');
                errorMessage.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: rgba(255, 0, 0, 0.7);
                    color: white;
                    padding: 10px 20px;
                    border-radius: 5px;
                    z-index: 10000;
                    font-size: 14px;
                    max-width: 80%;
                    text-align: center;
                `;
                
                // 针对不同类型的错误显示不同的消息
                if (event.error === 'aborted') {
                    errorMessage.textContent = '语音识别被中断，正在尝试恢复...';
                    // 自动恢复
                    setTimeout(() => {
                        try {
                            if (!this.isRecognizing) {
                                let langControl = document.getElementById('tts-chat-mic-lang-control');
                                if (langControl) {
                                    this.speechRecognition.lang = (langControl as HTMLSelectElement).value;
                                }
                                this.speechRecognition.start();
                            }
                        } catch (e) {
                            console.error('恢复语音识别失败:', e);
                        }
                    }, 2000);
                } else if (event.error === 'not-allowed') {
                    errorMessage.textContent = '请允许访问麦克风，否则无法使用语音功能';
                } else if (event.error === 'network') {
                    errorMessage.textContent = '网络连接问题导致语音识别失败';
                } else {
                    errorMessage.textContent = `语音识别错误: ${event.error}`;
                }
                
                // 添加到页面
                document.body.appendChild(errorMessage);
                
                // 3秒后自动移除错误提示
                setTimeout(() => {
                    if (document.body.contains(errorMessage)) {
                        document.body.removeChild(errorMessage);
                    }
                }, 3000);
            };

            this.speechRecognition.onresult = (event: any) => {
                let interim_transcript = '';
                // Loop through the results from the speech recognition object.
                for (let i = event.resultIndex; i < event.results.length; ++i) {
                    // If the result item is Final, add it to Final Transcript, Else add it to Interim transcript
                    if (event.results[i].isFinal) {
                        this.final_transcript += event.results[i][0].transcript;
                    } else {
                        interim_transcript += event.results[i][0].transcript;
                    }
                }
                if (interim_transcript.trim().length > 0) {
                    let textControl = document.getElementById('tts-chat-area') as HTMLTextAreaElement;
                    if (textControl) {
                        textControl.value = interim_transcript;
                    }

                    // 将识别到的文字显示在 Caption 上（如果开启的话）
                   // this.sendTextToCaption(interim_transcript, 1); // 显示1秒，因为是实时更新
                }
            };
        } else {
            console.error('❌ 浏览器不支持语音识别API');
            console.error('❌ 详细信息:', {
                SpeechRecognition: !!(<any>window).SpeechRecognition,
                webkitSpeechRecognition: !!(<any>window).webkitSpeechRecognition,
                isChrome,
                isEdge,
                userAgent: navigator.userAgent,
                vendor: navigator.vendor
            });
            this.speechRecognition = null;

            // 显示详细的错误信息
            let errorMessage = '语音识别功能不可用。';
            if (!SpeechRecognition) {
                errorMessage += '您的浏览器不支持语音识别API。';
            }
            errorMessage += '请使用最新版本的Chrome或Edge浏览器，并确保在HTTPS环境下访问。';

            this.showSpeechRecognitionError(errorMessage);
        }
    }

    /**
     * 显示语音识别错误信息
     */
    showSpeechRecognitionError(message: string) {
        console.error('语音识别错误:', message);

        // 创建一个友好的错误提示
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff4444;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-size: 14px;
            max-width: 400px;
            text-align: center;
        `;
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                document.body.removeChild(errorDiv);
            }
        }, 3000);
    }

    mcloudResponse(data: string) {
        let jsonObj: any = null;
        try {
            jsonObj = JSON.parse(data);
        } catch (error) {
            alert("Failed to parse mcloud response string. " + error);
            return;
        }

        console.groupCollapsed(jsonObj.response);
        console.log(data);
        console.groupEnd();

        // 添加通用的DOM元素安全设置函数
        const safeSetValue = (elementId: string, value: any, propertyName: string = 'value') => {
            try {
                const element = document.getElementById(elementId) as any;
                if (element !== null && element[propertyName] !== undefined) {
                    element[propertyName] = value || '';
                } else {
                    console.warn(`元素 ${elementId} 不存在或不支持属性 ${propertyName}`);
                }
            } catch (error) {
                console.error(`设置元素 ${elementId} 的 ${propertyName} 属性时出错:`, error);
            }
        };
        switch (jsonObj.response) {
            case 'a2fResponse': {
                switch (jsonObj.event) {
                    case 'A2F_SERVER_CONNECT_FAIL':
                        alert(jsonObj.errorMsg);
                        break;
                }
                break;
            }
            case 'stapResponse': {
                let stapState = document.getElementById('stap-state');
                if (stapState !== null) {
                    while (stapState.classList.length > 0) {
                        stapState.classList.remove(stapState.classList.item(0));
                    }
                }

                switch (jsonObj.event) {
                    case 'STAP_SERVER_CONNECTED': {
                        stapState.classList.add('circle-lime');
                        break;
                    }
                    case 'STAP_SERVER_CONNECT_FAIL': {
                        stapState.classList.add('circle-red');
                        alert(jsonObj.errorMsg);
                        break;
                    }
                    case 'STAP_SERVER_DISCONNECTED':
                        break;
                    case 'STAP_MODEL_AVAILABLE_LIST': {
                        let stapModelSelect = document.getElementById('stap-embedded-model') as HTMLSelectElement;
                        if (stapModelSelect !== null) {
                            stapModelSelect.innerHTML = '';
                            jsonObj.modelList.forEach(function(item: any) {
                                stapModelSelect.add(new Option(item.name, item.id));
                            });
                            safeSetValue('stap-embedded-model', jsonObj.currentModelId);
                        }
                        break;
                    }
                }
                break;
            }
            case 'characterResponse': {
                let charSelect = document.getElementById('character-select') as HTMLSelectElement;
                if (charSelect !== null) {
                    try {
                        charSelect.innerHTML = '';
                        jsonObj.characterList.forEach(function(item: any) {
                            charSelect.add(new Option(item.name, item.id));
                        });
                        charSelect.value = jsonObj.currentCharacterId || '';

                        let metahumanOptions = document.getElementById('metahuman-options');
                        if (metahumanOptions !== null) {
                            metahumanOptions.style.display = charSelect.value === '314' ? 'block' : 'none';
                        }
                    } catch (error) {
                        console.error('设置角色选择器时出错:', error);
                    }
                }

                let vrmCharSelect = document.getElementById('vrm-char-select') as HTMLSelectElement;
                if (vrmCharSelect !== null) {
                    vrmCharSelect.innerHTML = '';
                    jsonObj.vrmList.forEach(function(item: any) {
                        vrmCharSelect.add(new Option(item.name, item.name));
                    });
                }

                safeSetValue('bound-scale', jsonObj.boundScale);

                let autoBlinkSwitch = document.getElementById('autoblink-switch') as HTMLInputElement;
                if (autoBlinkSwitch !== null) {
                    try {
                        autoBlinkSwitch.checked = (jsonObj.autoBlink == 1);
                    } catch (error) {
                        console.error('设置autoBlinkSwitch时出错:', error);
                    }
                }

                safeSetValue('character-move-blendtime', jsonObj.moveBlendTime);
                safeSetValue('character-rotate-blendtime', jsonObj.rotateBlendTime);

                let characterLocationX = document.getElementById('character-location-x') as HTMLInputElement;
                let characterLocationY = document.getElementById('character-location-y') as HTMLInputElement;
                let characterLocationZ = document.getElementById('character-location-z') as HTMLInputElement;
                if (characterLocationX !== null && characterLocationY !== null && characterLocationZ !== null && jsonObj.characterLocation) {
                    try {
                        let locationArray = jsonObj.characterLocation.split(' ');
                        if (locationArray.length == 3) {
                            safeSetValue('character-location-x', parseFloat(locationArray[0].split('=')[1]).toString());
                            safeSetValue('character-location-y', parseFloat(locationArray[1].split('=')[1]).toString());
                            safeSetValue('character-location-z', parseFloat(locationArray[2].split('=')[1]).toString());
                        }
                    } catch (error) {
                        console.error('解析角色位置数据时出错:', error);
                    }
                }

                let characterRotateP = document.getElementById('character-rotation-p') as HTMLInputElement;
                let characterRotateY = document.getElementById('character-rotation-y') as HTMLInputElement;
                let characterRotateR = document.getElementById('character-rotation-r') as HTMLInputElement;
                if (characterRotateP !== null && characterRotateY !== null && characterRotateR !== null && jsonObj.characterRotation) {
                    try {
                        let rotationArray = jsonObj.characterRotation.split(' ');
                        if (rotationArray.length == 3) {
                            safeSetValue('character-rotation-p', parseFloat(rotationArray[0].split('=')[1]).toString());
                            safeSetValue('character-rotation-y', parseFloat(rotationArray[1].split('=')[1]).toString());
                            safeSetValue('character-rotation-r', parseFloat(rotationArray[2].split('=')[1]).toString());
                        }
                    } catch (error) {
                        console.error('解析角色旋转数据时出错:', error);
                    }
                }
                break;
            }
            /* Metahuman Start */
            case 'metahumanResponse': {
                let metahumanSelect_2 = document.getElementById('metahuman-select') as HTMLSelectElement | null;
                if (metahumanSelect_2) {
                metahumanSelect_2.innerHTML = '';

                jsonObj.list.forEach((item: { name: string; id: string }) => {
                    metahumanSelect_2.add(new Option(item.name, item.id));
                });

                metahumanSelect_2.value = jsonObj.currentId === 'none' ? 'None' : jsonObj.currentId || '';
                this.sortSelect(metahumanSelect_2);
                }
                break;
            }

            case 'MHCharResponse': {
                let MHCharSelect_1 = document.getElementById('MHChar-select') as HTMLSelectElement | null;
                if (MHCharSelect_1) {
                    MHCharSelect_1.innerHTML = '';

                    jsonObj.list.forEach((item: { name: string; id: string; available: string }) => {
                        let newOption = new Option(item.name, item.id);
                        if (item.available !== '1') {
                            newOption.disabled = true;
                        }
                        MHCharSelect_1.add(newOption);
                    });

                    MHCharSelect_1.value = jsonObj.currentId || '';
                    this.sortSelect(MHCharSelect_1);

                    if (MHCharSelect_1.options.length === 0) {
                        MHCharSelect_1.add(new Option('None'));
                    }
                }
                break;
            }
            /* Metahuman End */
            case 'fakeShadowResponse': {
                let shadowEnable  = document.getElementById('shadow-switch') as HTMLInputElement;
                let shadowAngle   = document.getElementById('shadow-angle-text') as HTMLInputElement;
                let shadowLength  = document.getElementById('shadow-length-text') as HTMLInputElement;
                let shadowOpacity = document.getElementById('shadow-opacity-text') as HTMLInputElement;
                let shadowOffset  = document.getElementById('shadow-offset-text') as HTMLInputElement;
                let shadowBlur    = document.getElementById('shadow-blur-text') as HTMLInputElement;
                if (shadowEnable !== null && shadowAngle !== null && shadowLength !== null &&
                    shadowOpacity !== null && shadowOffset !== null && shadowBlur !== null) {
                    shadowEnable.checked = (jsonObj.enable == 1);
                    safeSetValue('fake-shadow-angle', jsonObj.angle);
                    safeSetValue('fake-shadow-length', jsonObj.length);
                    safeSetValue('fake-shadow-opacity', jsonObj.opacity);
                    safeSetValue('fake-shadow-offset', jsonObj.offset);
                    safeSetValue('fake-shadow-blur', jsonObj.blur);
                }
                break;
            }
            case 'outfitResponse': {
                let outfitSelect = document.getElementById('outfit-select') as HTMLSelectElement;
                if (outfitSelect !== null) {
                    outfitSelect.innerHTML = '';
                    jsonObj.outfitList.forEach(function(item: any) {
                        let newOption = new Option(item.name, item.id);
                        if (item.available != '1') {
                            newOption.disabled = true;
                        }
                        outfitSelect.add(newOption);
                    });
                    safeSetValue('outfit-select', jsonObj.currentOutfitId);

                    this.sortSelect(outfitSelect);

                    if (outfitSelect.options.length == 0) {
                        outfitSelect.add(new Option('None'));
                    }
                }
                break;
            }
            case 'modularPartsResponse': {
                switch (jsonObj.event) {
                    case 'MODULAR_PARTS_PROPERTIES_INFO': {
                        let customOutfitHeadSelect  = document.getElementById('custom-outfit-head-select') as HTMLSelectElement;
                        let customOutfitHairSelect  = document.getElementById('custom-outfit-hair-select') as HTMLSelectElement;
                        let customOutfitUpperSelect = document.getElementById('custom-outfit-upper-select') as HTMLSelectElement;
                        let customOutfitLowerSelect = document.getElementById('custom-outfit-lower-select') as HTMLSelectElement;
                        let customOutfitFeetSelect  = document.getElementById('custom-outfit-feet-select') as HTMLSelectElement;
                        let customOutfitAccSelect   = document.getElementById('custom-outfit-acc-select') as HTMLSelectElement;
                        if (customOutfitHeadSelect !== null && customOutfitHeadSelect !== null && customOutfitUpperSelect !== null &&
                            customOutfitLowerSelect !== null && customOutfitFeetSelect !== null && customOutfitAccSelect !== null) {
                            // Remove all options
                            customOutfitHeadSelect.innerHTML  = '';
                            customOutfitHairSelect.innerHTML  = '';
                            customOutfitUpperSelect.innerHTML = '';
                            customOutfitLowerSelect.innerHTML = '';
                            customOutfitFeetSelect.innerHTML  = '';
                            customOutfitAccSelect.innerHTML   = '';
                            // Update options
                            const func = function(SelectElemt: HTMLSelectElement, list: any) {
                                let sortedList = list.sort((a: any, b: any) => a.name.localeCompare(b.name));
                                sortedList.forEach(function(item: any) {
                                    let newOption = new Option(item.name, item.id);
                                    newOption.disabled = (item.available == 0);
                                    newOption.selected = (item.inUse == 1);
                                    SelectElemt.add(newOption);
                                });
                                if (SelectElemt.options.length == 0) {
                                    SelectElemt.add(new Option('None'));
                                }
                            }.bind(this);

                            func(customOutfitHeadSelect, jsonObj.modularHeadList);
                            func(customOutfitHairSelect, jsonObj.modularHairList);
                            func(customOutfitUpperSelect, jsonObj.modularUpperList);
                            func(customOutfitLowerSelect, jsonObj.modularLowerList);
                            func(customOutfitFeetSelect, jsonObj.modularFeetList);
                            func(customOutfitAccSelect, jsonObj.modularAccList);
                        }

                        let customOutfitPresetSelect  = document.getElementById('custom-outfit-preset-select') as HTMLSelectElement;
                        if (customOutfitPresetSelect !== null) {
                            var selectedValue = customOutfitPresetSelect.value;
                            customOutfitPresetSelect.innerHTML = '';
                            jsonObj.modularPresetList.forEach(function(item: any) {
                                let newOption = new Option(item.name, item.id);
                                newOption.selected = (item.id == selectedValue);
                                customOutfitPresetSelect.add(newOption);
                            });

                            this.sortSelect(customOutfitPresetSelect);

                            if (customOutfitPresetSelect.options.length == 0) {
                                customOutfitPresetSelect.add(new Option('None'));
                            }
                        }
                        break;
                    }
                    case 'MODULAR_PARTS_INCOMPTIBLE_ERROR': {
                        alert(jsonObj.errorMsg);
                        break;
                    }
                }
                break;
            }
            case 'avatarColorResponse': {
                let avatarColorSelect = document.getElementById('avatarColor-select') as HTMLSelectElement;
                if (avatarColorSelect !== null) {
                    avatarColorSelect.innerHTML = '';
                    jsonObj.adjustablePartList.forEach(function(item: any) {
                        avatarColorSelect.add(new Option(item.name, item.id));
                    });
                    // sort by original data
                    //this.sortSelect(avatarColorSelect);

                    if (avatarColorSelect.options.length == 0) {
                        avatarColorSelect.add(new Option('None'));
                    }
                }
                break;
            }
            case 'logoResponse': {
                let logoPlaceSelect = document.getElementById('logo-place-select') as HTMLSelectElement;
                let logoSelect = document.getElementById('logo-select') as HTMLSelectElement;
                if (logoPlaceSelect !== null && logoSelect !== null) {
                    logoPlaceSelect.innerHTML = '';
                    jsonObj.logoPlacementList.forEach(function(item: any) {
                        logoPlaceSelect.add(new Option(item.name, item.id));
                    });
                    logoPlaceSelect.value = jsonObj.currentPlacementId == 'none' ? 'None' : jsonObj.currentPlacementId || '';
                    this.sortSelect(logoPlaceSelect);

                    logoSelect.innerHTML = '';
                    jsonObj.logoList.forEach(function(item: any) {
                        logoSelect.add(new Option(item.name, item.id));
                    });
                    logoSelect.value = jsonObj.currentLogoId == 'none' ? 'None' : jsonObj.currentLogoId || '';
                }
                break;
            }
            case 'cameraResponse': {
                let cameraSelect = document.getElementById('camera-select') as HTMLSelectElement;
                if (cameraSelect !== null) {
                    cameraSelect.innerHTML = '';
                    jsonObj.cameraList.forEach(function(item: any) {
                        cameraSelect.add(new Option(item.name, item.id));
                    });
                    cameraSelect.value = jsonObj.currentCameraId;

                    let joystickDiv = document.getElementById('joystickDiv');
                    let cameraTransform = document.getElementById('camera-transform');
                    if (joystickDiv !== null && cameraTransform !== null) {
                        joystickDiv.style.display = cameraSelect.value == '4' ? 'flex' : 'none';
                        cameraTransform.style.display = cameraSelect.value == '4' ? 'block' : 'none';

                        let locationArray = jsonObj.cameraLocation.split(' ');
                        if (locationArray.length == 3) {
                            (document.getElementById('camera-location-x') as HTMLInputElement).value = parseFloat(locationArray[0].split('=')[1]).toString();
                            (document.getElementById('camera-location-y') as HTMLInputElement).value = parseFloat(locationArray[1].split('=')[1]).toString();
                            (document.getElementById('camera-location-z') as HTMLInputElement).value = parseFloat(locationArray[2].split('=')[1]).toString();
                        }
                        let rotationArray = jsonObj.cameraRotation.split(' ');
                        if (rotationArray.length == 3) {
                            (document.getElementById('camera-rotation-p') as HTMLInputElement).value = parseFloat(rotationArray[0].split('=')[1]).toString();
                            (document.getElementById('camera-rotation-y') as HTMLInputElement).value = parseFloat(rotationArray[1].split('=')[1]).toString();
                            (document.getElementById('camera-rotation-r') as HTMLInputElement).value = parseFloat(rotationArray[2].split('=')[1]).toString();
                        }
                    }
                }
                break;
            }
            case 'holoDisplayResponse': {
                // Handle HoloDisplay camera location
                let holoDisplayCameraLocationX = document.getElementById('holodisplay-camera-location-x') as HTMLInputElement;
                let holoDisplayCameraLocationY = document.getElementById('holodisplay-camera-location-y') as HTMLInputElement;
                let holoDisplayCameraLocationZ = document.getElementById('holodisplay-camera-location-z') as HTMLInputElement;
                if (holoDisplayCameraLocationX !== null && holoDisplayCameraLocationY !== null && holoDisplayCameraLocationZ !== null && jsonObj.location) {
                    let locationArray = jsonObj.location.split(' ');
                    if (locationArray.length == 3) {
                        holoDisplayCameraLocationX.value = parseFloat(locationArray[0].split('=')[1]).toString();
                        holoDisplayCameraLocationY.value = parseFloat(locationArray[1].split('=')[1]).toString();
                        holoDisplayCameraLocationZ.value = parseFloat(locationArray[2].split('=')[1]).toString();
                    }
                }

                // Handle HoloDisplay camera rotation
                let holoDisplayCameraRotationP = document.getElementById('holodisplay-camera-rotation-p') as HTMLInputElement;
                let holoDisplayCameraRotationY = document.getElementById('holodisplay-camera-rotation-y') as HTMLInputElement;
                let holoDisplayCameraRotationR = document.getElementById('holodisplay-camera-rotation-r') as HTMLInputElement;
                if (holoDisplayCameraRotationP !== null && holoDisplayCameraRotationY !== null && holoDisplayCameraRotationR !== null && jsonObj.rotation) {
                    let rotationArray = jsonObj.rotation.split(' ');
                    if (rotationArray.length == 3) {
                        holoDisplayCameraRotationP.value = parseFloat(rotationArray[0].split('=')[1]).toString();
                        holoDisplayCameraRotationY.value = parseFloat(rotationArray[1].split('=')[1]).toString();
                        holoDisplayCameraRotationR.value = parseFloat(rotationArray[2].split('=')[1]).toString();
                    }
                }

                // Handle camera size and FOV if needed
                if (jsonObj.cameraSize) {
                    let holoDisplayCameraSize = document.getElementById('holodisplay-camera-size') as HTMLInputElement;
                    if (holoDisplayCameraSize !== null) {
                        holoDisplayCameraSize.value = jsonObj.cameraSize;
                    }
                }
                
                if (jsonObj.fov) {
                    let holoDisplayCameraFov = document.getElementById('holodisplay-camera-fov') as HTMLInputElement;
                    if (holoDisplayCameraFov !== null) {
                        holoDisplayCameraFov.value = jsonObj.fov;
                    }
                }
                break;
            }
            case 'animationResponse': {
                switch (jsonObj.event) {
                    case 'ANIMATION_PROPERTIES_INFO': {
                        let loopAnimationSelect = document.getElementById('loopAnimation-select') as HTMLSelectElement;
                        if (loopAnimationSelect !== null) {
                            loopAnimationSelect.innerHTML = '';
                            jsonObj.loopAnimationList.forEach(function(item: any) {
                                let newOption = new Option(item.name, item.id);
                                newOption.disabled = item.available != '1' ? true : false;
                                loopAnimationSelect.add(newOption);
                            });
                            safeSetValue('loopAnimation-select', jsonObj.currentLoopAnimationId);

                            this.sortSelect(loopAnimationSelect);

                            if (loopAnimationSelect.options.length == 0) {
                                loopAnimationSelect.add(new Option('None'));
                            }
                        }

                        let triggerAnimationSelect = document.getElementById('triggerAnimation-select') as HTMLSelectElement;
                        if (triggerAnimationSelect !== null) {
                            triggerAnimationSelect.innerHTML = '';
                            jsonObj.triggerAnimationList.forEach(function(item: any) {
                                let newOption = new Option(item.name, item.id);
                                newOption.disabled = item.available != '1' ? true : false;
                                triggerAnimationSelect.add(newOption);
                            });

                            this.sortSelect(triggerAnimationSelect);

                            if (triggerAnimationSelect.options.length == 0) {
                                triggerAnimationSelect.add(new Option('None'));
                            }
                        }
                        break;
                    }
                    case 'ANIMATION_PLAY_START': {
                        console.log('Triggered animation begin: ' + jsonObj.animationId);
                        break;
                    }
                    case 'ANIMATION_PLAY_END': {
                        console.log('Triggered animation end: ' + jsonObj.animationId);
                        break;
                    }
                }
                break;
            }
            case 'expressionResponse': {
                let expressionSelect = document.getElementById('expression-select') as HTMLSelectElement;
                if (expressionSelect !== null) {
                    expressionSelect.innerHTML = '';
                    jsonObj.expressionList.forEach(function(item: any) {
                        expressionSelect.add(new Option(item.name, item.id));
                    });
                    safeSetValue('expression-select', jsonObj.currentExpressionId);

                    if (expressionSelect.options.length == 0) {
                        expressionSelect.add(new Option('None'));
                    }
                }
                break;
            }
            case 'lookAtCamResponse': {
                let lookAtSelet = document.getElementById('lookAt-select') as HTMLSelectElement;
                let lookAtOptionsContainer = document.getElementById('lookAtOptions-container');
                let lookAtOptionsCustomContainer = document.getElementById('lookAtOptions-custom-container');
                if (lookAtSelet !== null && lookAtOptionsContainer !== null && lookAtOptionsCustomContainer !== null) {
                    lookAtSelet.value = jsonObj.lookAtType;
                    lookAtOptionsContainer.style.display = lookAtSelet.value == '0' ? 'none' : 'grid';
                    lookAtOptionsCustomContainer.style.display = lookAtSelet.value == '2' ? 'grid' : 'none';

                    (document.getElementById('LookAtCamOffsetX_Range') as HTMLInputElement).value = jsonObj.offsetX;
                    (document.getElementById('LookAtCamOffsetX_num') as HTMLInputElement).value = jsonObj.offsetX;
                    (document.getElementById('LookAtCamOffsetY_Range') as HTMLInputElement).value = jsonObj.offsetY;
                    (document.getElementById('LookAtCamOffsetY_num') as HTMLInputElement).value = jsonObj.offsetY;
                    (document.getElementById('lookAtCamLimitAngle-horizontal-num') as HTMLInputElement).value = jsonObj.limitAngleHorizontal;
                    (document.getElementById('lookAtCamLimitAngle-vertical-num') as HTMLInputElement).value = jsonObj.limitAngleVertical;
                    (document.getElementById('lookAtCamBlendTime-num') as HTMLInputElement).value = (jsonObj.blendTime * 1000).toString();
                    (document.getElementById('lookAtCamLockPitch') as HTMLInputElement).checked = (jsonObj.lockPitch == 1);
                    (document.getElementById('lookAtCamEyeOnly') as HTMLInputElement).checked = (jsonObj.eyeOnly == 1);

                    (document.getElementById('lookAtCam-custom-rotate-yaw') as HTMLInputElement).value = jsonObj.lookAtCamRotateYaw;
                    (document.getElementById('lookAtCam-custom-rotate-pitch') as HTMLInputElement).value = jsonObj.lookAtCamRotatePitch;
                    (document.getElementById('lookAtCam-custom-rotate-eye-speed') as HTMLInputElement).value = jsonObj.eyeSpeed;
                    (document.getElementById('lookAtCam-custom-rotate-head-speed') as HTMLInputElement).value = jsonObj.headSpeed;
                    (document.getElementById('lookAtCam-custom-rotate-body-speed') as HTMLInputElement).value = jsonObj.bodySpeed;
                }
                break;
            }
            case 'backgroundResponse': {
                let backgroundSelect = document.getElementById('background-select') as HTMLSelectElement;
                if (backgroundSelect !== null) {
                    backgroundSelect.innerHTML = '';
                    jsonObj.backgroundList.forEach(function(item: any) {
                        backgroundSelect.add(new Option(item.name, item.id));
                    });
                    backgroundSelect.value = jsonObj.currentBackgroundId == 'none' ? 'None' : jsonObj.currentBackgroundId || '';

                    this.sortSelect(backgroundSelect);
                }

                let backgroundModeSelect = document.getElementById('background-mode-select') as HTMLSelectElement;
                if (backgroundModeSelect !== null) {
                    backgroundModeSelect.value = jsonObj.backgroundMode;
                }

                let backgroundStyleSelect = document.getElementById('background-style-select') as HTMLSelectElement;
                if (backgroundStyleSelect !== null && jsonObj.backgroundStyle !== undefined) {
                    backgroundStyleSelect.value = jsonObj.backgroundStyle;
                }

                let backgroundBlendtime = document.getElementById('background-blendtime') as HTMLInputElement;
                if (backgroundBlendtime !== null && jsonObj.blendTime !== undefined) {
                    backgroundBlendtime.value = jsonObj.blendTime;
                }

                let holoDisplayBackgroundDistance = document.getElementById('holodisplay-background-distance') as HTMLInputElement;
                if (holoDisplayBackgroundDistance !== null && jsonObj.distance !== undefined) {
                    holoDisplayBackgroundDistance.value = jsonObj.distance;
                }
                break;
            }
            case 'resolutionResponse': {
                let widthInput  = document.getElementById('resolution-width') as HTMLInputElement;
                let heightInput = document.getElementById('resolution-height') as HTMLInputElement;
                if (widthInput !== null && heightInput !== null) {
                    // 添加空值检查和错误处理
                    try {
                        widthInput.value  = jsonObj.width || '';
                        heightInput.value = jsonObj.height || '';
                    } catch (error) {
                        console.error('设置分辨率输入值时出错:', error);
                    }
                }

                let renderWorldSwitch = document.getElementById('render-world-tgl') as HTMLInputElement;
                if (renderWorldSwitch !== null) {
                    try {
                        // 确保元素存在且为正确类型后再设置属性
                        if (renderWorldSwitch && typeof renderWorldSwitch.checked !== 'undefined') {
                            renderWorldSwitch.checked = (jsonObj.renderWorld == 1);
                        } else {
                            console.warn('render-world-tgl 元素不是有效的checkbox元素');
                        }
                    } catch (error) {
                        console.error('设置render-world-tgl状态时出错:', error, '元素:', renderWorldSwitch);
                    }
                }
                break;
            }
            case 'lightResponse': {
                let lightsetSelect = document.getElementById('lightset-select') as HTMLSelectElement;
                if (lightsetSelect !== null) {
                    lightsetSelect.innerHTML = '';
                    jsonObj.lightsetList.forEach(function(item: any) {
                        lightsetSelect.add(new Option(item.name, item.id));
                    });

                    lightsetSelect.value = jsonObj.currentLightsetId;
                }

                let lightingFollowSwitch = document.getElementById('lightingFollow-switch') as HTMLInputElement;
                if (lightingFollowSwitch !== null){
                    lightingFollowSwitch.checked = (jsonObj.followCharacter == 1);
                }

                let lightSelect = document.getElementById('light-select') as HTMLSelectElement;
                if (lightSelect !== null) {
                    lightSelect.innerHTML = '';
                    jsonObj.currentLightsets.forEach(function(item: any) {
                        lightSelect.add(new Option(item.name, item.id));

                    });
                    lightSelect.value = jsonObj.currentLightId;
                }

                let lightEnable = document.getElementById('light-switch') as HTMLInputElement;
                if (lightEnable !== null) {
                    lightEnable.checked = (jsonObj.visible == 1);
                }
                let lightIntensity = document.getElementById('light-intensity') as HTMLInputElement;
                if (lightIntensity !== null) {
                    lightIntensity.value = jsonObj.intensity;
                }

                let lightColor = document.getElementById('light-color') as HTMLInputElement;
                if (lightColor !== null) {
                    const colorParts = jsonObj.color.split(',');
                    const r = Math.round(parseFloat(colorParts[0]) * 255);
                    const g = Math.round(parseFloat(colorParts[1]) * 255);
                    const b = Math.round(parseFloat(colorParts[2]) * 255);
                    const hexColor = '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
                    lightColor.value = hexColor;
                }

                let lightLocX = document.getElementById('light-loc-X') as HTMLInputElement;
                let lightLocY = document.getElementById('light-loc-Y') as HTMLInputElement;
                let lightLocZ = document.getElementById('light-loc-Z') as HTMLInputElement;
                if (lightLocX !== null && lightLocY !== null && lightLocZ !== null) {
                    let loc = jsonObj.location.split(',');
                    lightLocX.value = loc[0];
                    lightLocY.value = loc[1];
                    lightLocZ.value = loc[2];
                }
                let lightRotX = document.getElementById('light-rot-X') as HTMLInputElement;
                let lightRotY = document.getElementById('light-rot-Y') as HTMLInputElement;
                let lightRotZ = document.getElementById('light-rot-Z') as HTMLInputElement;
                if (lightRotX !== null && lightRotY !== null && lightRotZ !== null) {
                    let rot = jsonObj.rotation.split(',');
                    lightRotX.value = rot[0];
                    lightRotY.value = rot[1];
                    lightRotZ.value = rot[2];
                }
                break;
            }
            case 'particleResponse': {
                let envParticleSelect = document.getElementById('env-particle-select') as HTMLSelectElement;
                if (envParticleSelect !== null) {
                    envParticleSelect.innerHTML = '';
                    jsonObj.envParticleList.forEach(function(item: any) {
                        let newOption = new Option(item.name, item.id);
                        newOption.selected = (item.inUse == 1);
                        envParticleSelect.add(newOption);

                    });
                }

                let triggerParticleSelect = document.getElementById('trigger-particle-select') as HTMLSelectElement;
                if (triggerParticleSelect !== null) {
                    triggerParticleSelect.innerHTML = '';
                    jsonObj.triggerParticleList.forEach(function(item: any) {
                        triggerParticleSelect.add(new Option(item.name, item.id));
                    });
                }
                break;
            }
            case 'audioResponse': {
                switch (jsonObj.event) {
                    case 'AUDIO_PLAY_START': {
                        console.log(jsonObj.event);
                        break;
                    }
                    case 'AUDIO_PLAY_END': {
                        console.log(jsonObj.event);
                        break;
                    }
                    case 'AUDIO_PROPERTIES_INFO': {
                        let audioSelect = document.getElementById('audio-select') as HTMLSelectElement;
                        if (audioSelect !== null) {
                            audioSelect.innerHTML = '';
                            jsonObj.fileList.forEach(function(item: any) {
                                audioSelect.add(new Option(item.name, item.id));
                            });

                            this.sortSelect(audioSelect);

                            if (audioSelect.options.length == 0) {
                                audioSelect.add(new Option('None'));
                            }
                        }
                        let audioVolume = document.getElementById('audio-volume') as HTMLInputElement;
                        if (audioVolume !== null) {
                            audioVolume.value = jsonObj.volume;
                        }

                        let animIDWhenTalk = document.getElementById('anim-when-talk') as HTMLInputElement;
                        if (animIDWhenTalk !== null) {
                            animIDWhenTalk.value = jsonObj.animIDWhenTalk;
                        }

                        let animIDAfterTalk = document.getElementById('anim-after-talk') as HTMLInputElement;
                        if (animIDAfterTalk !== null) {
                            animIDAfterTalk.value = jsonObj.animIDAfterTalk;
                        }
                        break;
                    }
                }
                break;
            }
            case 'ttsResponse': {
                switch (jsonObj.event) {
                    case 'TTS_SYNTHESIZE_FILE_ERROR': {
                        alert(jsonObj.errorMsg);
                        break;
                    }
                    case 'TTS_SYNTHESIZE_FILE_SUCCESS': {
                        //alert('TTS synthesize to file succeeded.');
                        console.log(jsonObj.fileid);
                        break;
                    }
                    case 'TTS_SYNTHESIZE_SPEECH_ERROR': {
                        alert(jsonObj.errorMsg);
                        break;
                    }
                    case 'TTS_SYNTHESIZE_SPEECH_SUCCESS': {
                        console.log(jsonObj.sourceText);
                        break;
                    }
                    case 'TTS_AVAILABLE_ENGINES_LIST': {
                        let ttsContainer = document.getElementById('audiosource-tts-container');
                        if (ttsContainer) {
                            let ttsEngineControl   = document.getElementById('tts-engine-control') as HTMLSelectElement;
                            let ttsLanguageControl = document.getElementById('tts-language-control') as HTMLSelectElement;
                            let ttsVoiceControl    = document.getElementById('tts-voice-control') as HTMLSelectElement;
                            ttsEngineControl.innerHTML   = ''; // remove all child
                            ttsLanguageControl.innerHTML = '<option>None</option>';
                            ttsVoiceControl.innerHTML    = '<option>None</option>';

                            jsonObj.engineList.forEach((item: any) => {
                                ttsEngineControl.add(new Option(item.name, item.id));
                            });
                            ttsEngineControl.value = jsonObj.currentEngineId;
                        }
                        break;
                    }
                    case 'TTS_FETCH_AVAILABLE_VOICES_ERROR': {
                        alert(jsonObj.errorMsg);

                        let ttsContainer = document.getElementById('audiosource-tts-container');
                        if (ttsContainer) {
                            let ttsTabPanel = document.getElementById('tts-tab-panel');
                            ttsTabPanel.classList.add('div-disabled');

                            let ttsLanguageControl = document.getElementById('tts-language-control');
                            let ttsVoiceControl    = document.getElementById('tts-voice-control');
                            ttsLanguageControl.innerHTML = '<option>None</option>';
                            ttsVoiceControl.innerHTML    = '<option>None</option>';
                        }
                        break;
                    }
                    case 'TTS_AVAILABLE_VOICES_LIST': {
                        let ttsContainer = document.getElementById('audiosource-tts-container');
                        if (ttsContainer) {
                            let ttsTabPanel = document.getElementById('tts-tab-panel');
                            ttsTabPanel.classList.remove('div-disabled');

                            let ttsEngineControl   = document.getElementById('tts-engine-control') as HTMLSelectElement;
                            let ttsLanguageControl = document.getElementById('tts-language-control') as HTMLSelectElement;
                            let ttsVoiceControl    = document.getElementById('tts-voice-control') as HTMLSelectElement;
                            ttsEngineControl.value = jsonObj.engineId;

                            let ttsSpeedNum = document.getElementById('tts-speed-num') as HTMLInputElement;
                            ttsSpeedNum.value = jsonObj.speed;

                            if (jsonObj.voiceList.length == 0) {
                                ttsLanguageControl.innerHTML = '<option>None</option>';
                                ttsVoiceControl.innerHTML    = '<option>None</option>';
                            }
                            else {
                                // remove all child
                                if (jsonObj.chunkIndex == 0) {
                                    ttsLanguageControl.innerHTML = '';
                                    ttsVoiceControl.innerHTML = '';
                                }

                                let langCodelist: string[] = [];
                                Array.prototype.forEach.call(ttsVoiceControl.options, function(option: HTMLOptionElement) {
                                    langCodelist.push(option.value);
                                });
                                jsonObj.voiceList.forEach((item: any) => {
                                    // unique
                                    if (langCodelist.indexOf(item.languageCode) == -1) {
                                        langCodelist.push(item.languageCode);

                                        let displayname = langCodeToDisplayName(item.languageCode).replace(' - ', ', ');
                                        if (displayname == '') {
                                            displayname = item.languageCode;
                                        }
                                        ttsLanguageControl.appendChild(new Option(displayname, item.languageCode));
                                    }

                                    let displayname = `${item.name} (${item.gender}` + ((item.voiceType !== '') ? `, ${item.voiceType}` : '') + ')';
                                    let opt = new Option(displayname, item.voiceId);
                                    opt.setAttribute('langCode', item.languageCode);
                                    ttsVoiceControl.appendChild(opt);
                                });

                                if (jsonObj.chunkIndex + 1 == jsonObj.chunkCount) {
                                    let currentLangCode = ttsVoiceControl.options[0].getAttribute('langCode');
                                    for (var i = 0; i < ttsVoiceControl.options.length; i++) {
                                        if (ttsVoiceControl.options[i].value == jsonObj.currentVoiceId) {
                                            currentLangCode = ttsVoiceControl.options[i].getAttribute('langCode');
                                            break;
                                        }
                                    }

                                    Array.prototype.forEach.call(ttsLanguageControl.options, function(option: HTMLOptionElement) {
                                        option.selected = (option.value == currentLangCode);
                                    });

                                    Array.prototype.forEach.call(ttsVoiceControl.options, function(option: HTMLOptionElement) {
                                        option.hidden = (option.getAttribute('langCode') != currentLangCode);
                                        option.selected = (option.value == jsonObj.currentVoiceId);
                                    });
                                }
                            }
                            this.sortSelect(ttsLanguageControl);
                        }
                        break;
                    }
                }
                break;
            }
            case 'chatbotResponse': {
                switch (jsonObj.event) {
                    case 'CHATBOT_CHAT_REQUEST_ERROR': {
                        alert(jsonObj.errorMsg);
                        let chatOverlay = document.getElementById('chatOverlay');
                        if (chatOverlay) {
                            chatOverlay.innerHTML += '<span class="chatSpan"><img src="/images/answer.png" style="height:1em;" />&nbsp;' + jsonObj.errorMsg + '</span>';
                            chatOverlay.scrollTop = chatOverlay.scrollHeight;
                        }
                        break;
                    }
                    case 'CHATBOT_CHAT_REQUEST_SUCCESS': {
                        let chatOverlay = document.getElementById('chatOverlay');
                        if (chatOverlay) {
                            chatOverlay.innerHTML += '<span class="chatSpan"><img src="/images/answer.png" style="height:1em;" />&nbsp;' + jsonObj.completion + '</span>';
                            chatOverlay.scrollTop = chatOverlay.scrollHeight;
                        }
                        break;
                    }
                    case 'CHATBOT_AVAILABLE_MODEL_LIST': {
                        let chatModelControl = document.getElementById('tts-chat-model-control') as HTMLSelectElement;
                        if (chatModelControl) {
                            chatModelControl.innerHTML = ''; // remove all child

                            // 定义完整的聊天机器人模型选项（包括通义千问）
                            const allModels = [
                                { name: 'ChatGPT', id: TTS_CHATBOT_ID.ChatGPT.toString() },
                                { name: 'Azure OpenAI', id: TTS_CHATBOT_ID.AzureOpenAI.toString() },
                                { name: '通义千问 (TongyiQianwen)', id: TTS_CHATBOT_ID.TongyiQianwen.toString() },
                                { name: 'OpenAI Realtime', id: TTS_CHATBOT_ID.OpenAIRealtime.toString() }
                            ];

                            // 合并后端提供的模型和我们的完整模型列表
                            let finalModelList = [...allModels];

                            // 如果后端提供了额外的模型，添加到列表中（避免重复）
                            if (jsonObj.modelList && jsonObj.modelList.length > 0) {
                                jsonObj.modelList.forEach((backendModel: any) => {
                                    const exists = allModels.some(model => model.id === backendModel.id);
                                    if (!exists) {
                                        finalModelList.push(backendModel);
                                    }
                                });
                            }

                            finalModelList.forEach((item: any) => {
                                chatModelControl.add(new Option(item.name, item.id));
                            });

                            // 设置当前选中的模型
                            if (jsonObj.currentModelId !== undefined) {
                                chatModelControl.value = jsonObj.currentModelId;
                            } else {
                                // 默认选择Azure OpenAI
                                chatModelControl.value = TTS_CHATBOT_ID.AzureOpenAI.toString();
                            }

                            let ttsChatForm = document.getElementById('tts-chat-form');
                            let ttsChatMicLangControl = document.getElementById('tts-chat-mic-lang-control');
                            let ttsOpenaiVoiceLabel   = document.getElementById('tts-chat-openairealtime-voice-label');
                            let ttsOpenaiVoiceControl = document.getElementById('tts-chat-openairealtime-voice-control');
                            let isOpenAIRealtime = chatModelControl.value == TTS_CHATBOT_ID.OpenAIRealtime.toString();
                            ttsChatForm.classList.toggle('div-disabled', isOpenAIRealtime);
                            ttsChatMicLangControl.classList.toggle('div-disabled', isOpenAIRealtime);
                            ttsOpenaiVoiceLabel.style.display = isOpenAIRealtime ? 'inline' : 'none';
                            ttsOpenaiVoiceControl.style.display = isOpenAIRealtime ? 'inline' : 'none';
                        }
                        break;
                    }
                }
                break;
            }
            case 'captionResponse': {
                switch (jsonObj.event) {
                    case 'CAPTION_PROPERTIES_INFO': {
                        let captionContainer = document.getElementById('caption-container');
                        if (captionContainer) {
                            (document.getElementById('caption-switch') as HTMLInputElement).checked = (jsonObj.enabled == 1);
                            // 使用正确的元素 ID 来设置位置值
                            if (jsonObj.marginLeft !== undefined) (document.getElementById('caption-marginLeft') as HTMLInputElement).value = jsonObj.marginLeft;
                            if (jsonObj.marginRight !== undefined) (document.getElementById('caption-marginRight') as HTMLInputElement).value = jsonObj.marginRight;
                            if (jsonObj.marginUp !== undefined) (document.getElementById('caption-marginUp') as HTMLInputElement).value = jsonObj.marginUp;
                            if (jsonObj.marginDown !== undefined) (document.getElementById('caption-marginDown') as HTMLInputElement).value = jsonObj.marginDown;
                            (document.getElementById('caption-padding') as HTMLInputElement).value = jsonObj.padding;
                            (document.getElementById('caption-font-size') as HTMLInputElement).value = jsonObj.fontSize;
                            (document.getElementById('caption-bg-switch') as HTMLInputElement).checked = (jsonObj.showBG == 1);
                            (document.getElementById('caption-auto-clear-switch') as HTMLInputElement).checked = (jsonObj.autoClear == 1);

                            let captionFontSelect = document.getElementById('caption-font-select') as HTMLSelectElement;
                            captionFontSelect.innerHTML = '';
                            jsonObj.fontList.forEach((item: any) => {
                                captionFontSelect.add(new Option(item.name, item.id));
                            });
                            captionFontSelect.value = jsonObj.currentFontId;

                            let captionBackgroundSelect = document.getElementById('caption-background-select') as HTMLSelectElement;
                            captionBackgroundSelect.innerHTML = '';
                            jsonObj.backgroundList.forEach((item: any) => {
                                captionBackgroundSelect.add(new Option(item.name, item.id));
                            });
                            captionBackgroundSelect.value = jsonObj.currentBackgroundId;

                            this.sortSelect(captionBackgroundSelect);

                            let captionEnabled = (jsonObj.enabled == 1);
                            let ttsSecondTextArea = document.getElementById('tts-second-text-area') as HTMLTextAreaElement;
                            if (ttsSecondTextArea !== null) {
                                ttsSecondTextArea.disabled = !captionEnabled;
                            }
                            let ttsSecondTextWarning = document.getElementById('tts-second-text-warning');
                            if (ttsSecondTextWarning !== null) {
                                ttsSecondTextWarning.style.display = captionEnabled ? 'none' : 'inline';
                            }
                        }

                        let foregroundContainer = document.getElementById('foreground-container');
                        if (foregroundContainer) {
                            let foregroundIDSelect = document.getElementById('foreground-id-select') as HTMLSelectElement;
                            foregroundIDSelect.innerHTML = '';
                            jsonObj.foregroundIDList.forEach((item: any) => {
                                foregroundIDSelect.add(new Option(item.name, item.id));
                            });

                            if (foregroundIDSelect.options.length > 0) {
                                foregroundIDSelect.selectedIndex = 0;
                            }

                            let foregroundImageSelect = document.getElementById('foreground-image-select') as HTMLSelectElement;
                            foregroundImageSelect.innerHTML = '';
                            jsonObj.foregroundList.forEach((item: any) => {
                                foregroundImageSelect.add(new Option(item.name, item.id));
                            });

                            if (foregroundImageSelect.options.length > 0) {
                                foregroundImageSelect.selectedIndex = 0;
                            }
                        }
                        break;
                    }
                    case 'FOREGROUND_VALUE_SYNC': {
                        let foregroundSizeX = document.getElementById('foreground-sizeX') as HTMLInputElement;
                        if (foregroundSizeX !== null) {
                            foregroundSizeX.value = jsonObj.sizeX;
                        }
                        let foregroundSizeY = document.getElementById('foreground-sizeY') as HTMLInputElement;
                        if (foregroundSizeY !== null) {
                            foregroundSizeY.value = jsonObj.sizeY;
                        }
                        let foregroundPositionX = document.getElementById('foreground-positionX') as HTMLInputElement;
                        if (foregroundPositionX !== null) {
                            foregroundPositionX.value = jsonObj.positionX;
                        }
                        let foregroundPositionY = document.getElementById('foreground-positionY') as HTMLInputElement;
                        if (foregroundPositionY !== null) {
                            foregroundPositionY.value = jsonObj.positionY;
                        }
                        let foregroundImage = document.getElementById('foreground-image-select') as HTMLInputElement;
                        if (foregroundImage !== null) {
                            foregroundImage.select = jsonObj.image;
                        }
                        let foregroundOrder = document.getElementById('foreground-order') as HTMLInputElement;
                        if (foregroundOrder !== null) {
                            foregroundOrder.value = jsonObj.order;
                        }
                        break;
                    }
                }
                break;
            }
            case 'lipsyncResponse': {
                let lipsyncCheckbox = document.getElementById('lipsync-tgl') as HTMLInputElement;
                if (lipsyncCheckbox !== null) {
                    lipsyncCheckbox.checked = (jsonObj.enabled == 1);
                }

                let audioOffsetNum = document.getElementById('lipsync-audio-offset-num') as HTMLInputElement;
                if (audioOffsetNum !== null) {
                    audioOffsetNum.value = jsonObj.audioOffset;
                }

                let bsSmoothingModeSelect = document.getElementById('bs-smooth-mode-select') as HTMLSelectElement;
                if (bsSmoothingModeSelect !== null) {
                    bsSmoothingModeSelect.innerHTML = '';

                    // Update options depend on each avatar
                    jsonObj.bsSmoothingModeList.forEach(function(item: any) {
                        bsSmoothingModeSelect.add(new Option(item.name, item.id));
                    });
                    bsSmoothingModeSelect.value = jsonObj.currentBsSmoothingMode;
                }
                
                let bsSmoothingFramesize = document.getElementById('bs-smooth-framesize') as HTMLInputElement;
                if (bsSmoothingFramesize !== null) {
                    bsSmoothingFramesize.value = jsonObj.bsSmoothingFrameSize;
                }
                
                let bsSmoothingAlpha = document.getElementById('bs-smooth-alpha') as HTMLInputElement;
                if (bsSmoothingAlpha !== null) {
                    bsSmoothingAlpha.value = jsonObj.bsSmoothingAlpha;
                }

                let mhcMinDelayInput = document.getElementById('mhc-min-delay') as HTMLInputElement;
                if (mhcMinDelayInput !== null && jsonObj.mhcMinDelay !== undefined) {
                    mhcMinDelayInput.value = jsonObj.mhcMinDelay;
                }
                break;
            }
            case 'recordResponse': {
                switch (jsonObj.event) {
                    case 'RECORD_PROPERTIES_INFO': {
                        let recordFramerate = document.getElementById('record-framerate') as HTMLInputElement;
                        if (recordFramerate !== null) {
                            recordFramerate.value = jsonObj.frameRate;
                        }
                        break;
                    }
                    case 'VIDEO_RECORD_COMPLETE': {
                        let startRecordButton = document.getElementById('start-record-button') as HTMLInputElement;
                        if (startRecordButton !== null) {
                            startRecordButton.disabled = false;
                        }
                        console.log(jsonObj.event);
                        break;
                    }
                    case 'VIDEO_RECORD_DOWNLOAD': {
                        console.log(jsonObj.event);
                        let link = document.createElement('a');
                        link.href = jsonObj.url;
                        link.download = window.location.href.split('/').pop();

                        if (document.createEvent) {
                            let event = new MouseEvent('click', {view: window, 'bubbles': true, 'cancelable': true});
                            link.dispatchEvent(event);
                        } else {
                            link.click();
                        }
                        break;
                    }
                }
                break;
            }
            case 'sideBySideDepthResponse': {
                let sideBySideDepthContainer = document.getElementById('sideBySideDepth-container');
                if (sideBySideDepthContainer !== null) {
                    (document.getElementById('sideBySideDepth-switch') as HTMLInputElement).checked = (jsonObj.enabled == 1);
                    (document.getElementById('sideBySideDepth-depthSpaceStart-text') as HTMLInputElement).value = jsonObj.depthSpaceStart;
                    (document.getElementById('sideBySideDepth-depthSpaceScale-text') as HTMLInputElement).value = jsonObj.depthSpaceScale;
                    (document.getElementById('sideBySideDepth-depthSpaceColorOffset-text') as HTMLInputElement).value = jsonObj.depthSpaceColorOffset;
                    (document.getElementById('sideBySideDepth-grayscaleClampAvatarMin-text') as HTMLInputElement).value = jsonObj.grayscaleClampAvatarMin;
                    (document.getElementById('sideBySideDepth-grayscaleClampAvatarMax-text') as HTMLInputElement).value = jsonObj.grayscaleClampAvatarMax;
                    (document.getElementById('sideBySideDepth-grayscaleClampBackground-text') as HTMLInputElement).value = jsonObj.grayscaleClampBackground;
                }
                break;
            }
            case 'scalabilityResponse': {
                let scalabilityAntiAliasing = document.getElementById('scalability-AntiAliasing') as HTMLSelectElement;
                let scalabilityPostProcess = document.getElementById('scalability-PostProcess') as HTMLSelectElement;
                let scalabilityShadow = document.getElementById('scalability-Shadow') as HTMLSelectElement;
                let scalabilityTexture = document.getElementById('scalability-Texture') as HTMLSelectElement;
                let scalabilityEffects = document.getElementById('scalability-Effects') as HTMLSelectElement;
                if (scalabilityAntiAliasing !== null && scalabilityPostProcess !== null && scalabilityShadow !== null &&
                    scalabilityTexture !== null && scalabilityEffects !== null) {
                    scalabilityAntiAliasing.value = jsonObj.AntiAliasingQuality;
                    scalabilityPostProcess.value = jsonObj.PostProcessQuality;
                    scalabilityShadow.value = jsonObj.ShadowQuality;
                    scalabilityTexture.value = jsonObj.TextureQuality;
                    scalabilityEffects.value = jsonObj.EffectsQuality;
                }
                break;
            }
            case 'versionResponse': {
                let compatibilityCheckContainer = document.getElementById('compatibilityCheck-container');
                let compatibilityCheckSwitch = document.getElementById('compatibilityCheck-switch') as HTMLInputElement;
                if (compatibilityCheckContainer !== null && compatibilityCheckSwitch !== null) {
                    if (jsonObj.version != 'Demo') {
                        compatibilityCheckContainer.classList.add('div-disabled');
                        compatibilityCheckSwitch.checked = true;
                    } else {
                        compatibilityCheckContainer.classList.remove('div-disabled');
                    }
                }

                let watermarkContainer = document.getElementById('watermark-container');
                let watermarkCheckbox = document.getElementById('watermark-record-checkbox') as HTMLInputElement;
                if (watermarkContainer !== null && watermarkCheckbox !== null) {
                    if (jsonObj.version == 'Trial') {
                        watermarkContainer.classList.add('div-disabled')
                        watermarkCheckbox.checked = true;
                    } else {
                        watermarkContainer.classList.remove('div-disabled');
                    }
                }

                if (jsonObj.versionNumber) {
                    let projectName = jsonObj.projectName == 'Standard' ? '' : jsonObj.projectName;
                    document.title = `Momentum Cloud ${projectName} v${jsonObj.versionNumber} ${jsonObj.version} `;
                }
                break;
            }
            /****MRQ Start***/
            case 'MRQResponse': {
                let mrqState = document.getElementById('mrq-state');
                if (mrqState !== null) {
                    mrqState.textContent = jsonObj.MRQState;
                }
                let mrqFrame = document.getElementById('mrq-frame');
                if (mrqFrame !== null) {
                    mrqFrame.textContent = jsonObj.Frame;
                }
                break;
            }

            case 'CaptureDurationResponse': {
                let CaptureDuration = document.getElementById('recording-second');
                if (CaptureDuration !== null) {
                    CaptureDuration.textContent = jsonObj.Duration;
                }
                break;
            }
            /****MRQ End***/
            case 'additionalPropsResponse': {
                let additionalPropsContainer = document.getElementById('additional-props-container');
                if (additionalPropsContainer !== null) {
                    let chairSelect = document.getElementById('chair-select') as HTMLSelectElement;
                    chairSelect.innerHTML = '';
                    jsonObj.chairList.forEach(function (item: any) {
                        chairSelect.add(new Option(item.name, item.id));
                    });
                    chairSelect.value = jsonObj.currentChairId;
                }
                break;
            }

            case 'loadingResponse': {
                switch (jsonObj.event) {
                    case 'CHARACTER_LOADING_COMPLETE': {
                        console.log(`Character ${jsonObj.characterName} loading complete.`);
                        break;
                    }
                    case 'LEVEL_LOADING_COMPLETE': {
                        console.log(`Level loading complete.`);                        
                        break;
                    }
                }
            }
        }
    }

    timecodeClear() {
        // Clear all timer
        if (this.timecodeTimeoutFirstId >= 0) {
            let highestID  = window.setTimeout(function() {}, 0);
            for (let i = highestID; i >= this.timecodeTimeoutFirstId; i--) {
                clearTimeout(i);
            }
            this.timecodeTimeoutFirstId = -1;
        }

        // Clear history commands
        this.timecodeAudioStartCommandQueue = [];
        this.timecodeAudioEndCommandQueue = [];

        // Unregister event listener
        this.pixelStreaming.removeResponseEventListener('timecode_mcloud_response');

        // Stop audio
        this.stopAudio();
        this.emitUIInteraction({ Audio: { reset: '' }});

        // Update UIs
        this.requestStreamerResponse();
    }

    timecodeMCloudResponse(data: string) {
        let jsonObj = JSON.parse(data);
        if (jsonObj.response == 'audioResponse') {
            switch (jsonObj.event) {
                case 'AUDIO_PLAY_START': {
                    if (this.timecodeAudioStartCommandQueue.length) {
                        this.ts_eval.run.call(this, this.timecodeAudioStartCommandQueue.shift());
                    }
                    break;
                }
                case 'AUDIO_PLAY_END': {
                    if (this.timecodeAudioEndCommandQueue.length) {
                        this.ts_eval.run.call(this, this.timecodeAudioEndCommandQueue.shift());
                    }
                    break;
                }
            }
        }
    }

    timecodePlay(csvFile: string) {

        this.timecodeClear();

        fetch(csvFile).then(response => response.text()).then(data => {

            this.pixelStreaming.addResponseEventListener('timecode_mcloud_response', (data:string) => this.timecodeMCloudResponse(data));

            const lines = data.split(/(\r\n|\r|\n)/);

            // Remove Invalid lines
            for (let i = lines.length - 1; i >= 0; i--) {
                // Remove whitespace
                lines[i] = lines[i].trim();
                // Remove comment
                if (lines[i].indexOf('//') == 0) {
                    lines[i] = lines[i].slice(0, lines[i].indexOf('//'));
                }
                // Check format
                if (lines[i].length == 0 || lines[i].indexOf(',') < 0 || lines[i].slice(lines[i].indexOf(',') + 1).length == 0) {
                    lines.splice(i, 1);
                }
            }

            function splitline(line: string) {
                let timecode = line.slice(0, line.indexOf(','));
                let commands = line.slice(line.indexOf(',') + 1);
                return {'timecode': timecode, 'commands': commands};
            }

            function timecodeToMilliseconds(timecode: string) {
                try {
                    let timeArray = timecode.split(':');
                    if (timeArray.length == 3 && !isNaN(Number(timeArray[0])) && !isNaN(Number(timeArray[1])) && !isNaN(Number(timeArray[2]))) {
                        let hours   = parseInt(timeArray[0], 10);
                        let minutes = parseInt(timeArray[1], 10);
                        let seconds = parseFloat(timeArray[2]);
                        let milliseconds = Math.round(seconds * 1000);
                        return (hours * 3600000) + (minutes * 60000) + milliseconds;
                    }
                } catch (error) {
                    console.log(error);
                }
                return null;
            }

            function createTimeoutCommand(commands: string, milliseconds: number) {
                commands = 'this.' + commands.replace(/'/g, '\\\'');
                return 'window.setTimeout(() => { try { eval(\'' + commands + '\'); } catch (error) { console.error(error); }}, ' + milliseconds + ');';
            }

            // Process commands
            let batchcommands = '';
            for (let i = 0; i < lines.length; i++) {
                let result = splitline(lines[i]);
                let milliseconds = timecodeToMilliseconds(result.timecode);
                if (milliseconds !== null && result.commands.length > 0) {
                    batchcommands += createTimeoutCommand(result.commands, milliseconds);
                }
            }

            // Process audio responses
            let audioCommandRegex = /{\s*("|'?)TextToSpeech("|'?)\s*:\s*{\s*("|'?)synthesis("|'?)\s*:|{\s*("|'?)Audio("|'?)\s*:\s*{\s*("|'?)playFile("|'?)\s*:/i;
            for (let i = 0; i < lines.length; i++) {
                let result = splitline(lines[i]);
                let milliseconds = timecodeToMilliseconds(result.timecode);
                // find audio command
                if (milliseconds !== null && result.commands.match(audioCommandRegex) !== null) {
                    this.timecodeAudioStartCommandQueue.push('');
                    this.timecodeAudioEndCommandQueue.push('');

                    // find audio response
                    while (i+1 < lines.length) {
                        result = splitline(lines[i+1]);
                        if (result.timecode.match(/RESPONSE_AUDIO_PLAY_START|RESPONSE_AUDIO_PLAY_END/i)) {
                            milliseconds = timecodeToMilliseconds(result.timecode.slice(result.timecode.indexOf('+') + 1));
                            // play start response
                            if (result.timecode.match(/RESPONSE_AUDIO_PLAY_START/i) !== null) {
                                this.timecodeAudioStartCommandQueue[this.timecodeAudioStartCommandQueue.length - 1] += createTimeoutCommand(result.commands, milliseconds);
                            }
                            // play end response
                            if (result.timecode.match(/RESPONSE_AUDIO_PLAY_END/i) !== null) {
                                this.timecodeAudioEndCommandQueue[this.timecodeAudioEndCommandQueue.length - 1] += createTimeoutCommand(result.commands, milliseconds);
                            }
                            // audio command in response
                            if (result.commands.match(audioCommandRegex) !== null) {
                                this.timecodeAudioStartCommandQueue.push('');
                                this.timecodeAudioEndCommandQueue.push('');
                            }
                            i++;
                        } else {
                            break;
                        }
                    }
                }
            }
            this.timecodeTimeoutFirstId = window.setTimeout(() => { this.ts_eval.run.call(this, batchcommands); }, 100);
        })
        .catch((error: Error) => {
            this.timecodeClear();
            console.error('Error:', error);
        });
    }
    //MRQ timecode Start
    performanceCapturetimecodePlay(csvFile: string) {

        this.timecodeClear();

        fetch(csvFile).then(response => response.text()).then(data => {

            this.pixelStreaming.addResponseEventListener('timecode_mcloud_response', (data:string) => this.timecodeMCloudResponse(data));

            const lines = data.split(/(\r\n|\r|\n)/);

            // Remove Invalid lines
            for (let i = lines.length - 1; i >= 0; i--) {
                // Remove whitespace
                lines[i] = lines[i].trim();
                // Remove comment
                if (lines[i].indexOf('//') == 0) {
                    lines[i] = lines[i].slice(0, lines[i].indexOf('//'));
                }
                // Check format
                if (lines[i].length == 0 || lines[i].indexOf(',') < 0 || lines[i].slice(lines[i].indexOf(',') + 1).length == 0) {
                    lines.splice(i, 1);
                }
            }

            function splitline(line: string) {
                let timecode = line.slice(0, line.indexOf(','));
                let commands = line.slice(line.indexOf(',') + 1);
                return {'timecode': timecode, 'commands': commands};
            }

            function timecodeToMilliseconds(timecode: string) {
                try {
                    let timeArray = timecode.split(':');
                    if (timeArray.length == 3 && !isNaN(Number(timeArray[0])) && !isNaN(Number(timeArray[1])) && !isNaN(Number(timeArray[2]))) {
                        let hours   = parseInt(timeArray[0], 10);
                        let minutes = parseInt(timeArray[1], 10);
                        let seconds = parseFloat(timeArray[2]);
                        let milliseconds = Math.round(seconds * 1000);
                        return (hours * 3600000) + (minutes * 60000) + milliseconds;
                    }
                } catch (error) {
                    console.log(error);
                }
                return null;
            }

            function createTimeoutCommand(commands: string, milliseconds: number) {
                commands = 'this.' + commands.replace(/'/g, '\\\'');
                return 'window.setTimeout(() => { try { eval(\'' + commands + '\'); } catch (error) { console.error(error); }}, ' + milliseconds + ');';
            }

            // Process commands
            let batchcommands = '';
            for (let i = 0; i < lines.length; i++) {
                let result = splitline(lines[i]);
                let milliseconds = timecodeToMilliseconds(result.timecode);
                if (milliseconds !== null && result.commands.length > 0) {
                    if (result.commands.includes("TextToSpeech") || result.commands.includes("playFile")) {
                        batchcommands += createTimeoutCommand(result.commands, milliseconds);
                    }
                }
            }

            // Process audio responses
            let audioCommandRegex = /{\s*("|'?)TextToSpeech("|'?)\s*:\s*{\s*("|'?)synthesis("|'?)\s*:|{\s*("|'?)Audio("|'?)\s*:\s*{\s*("|'?)playFile("|'?)\s*:/i;
            for (let i = 0; i < lines.length; i++) {
                let result = splitline(lines[i]);
                let milliseconds = timecodeToMilliseconds(result.timecode);
                // find audio command
                if (milliseconds !== null && result.commands.match(audioCommandRegex) !== null) {
                    this.timecodeAudioStartCommandQueue.push('');
                    this.timecodeAudioEndCommandQueue.push('');

                    // find audio response
                    while (i+1 < lines.length) {
                        result = splitline(lines[i+1]);
                        if (result.timecode.match(/RESPONSE_AUDIO_PLAY_START|RESPONSE_AUDIO_PLAY_END/i)) {
                            milliseconds = timecodeToMilliseconds(result.timecode.slice(result.timecode.indexOf('+') + 1));
                            // play start response
                            if (result.timecode.match(/RESPONSE_AUDIO_PLAY_START/i) !== null) {
                                this.timecodeAudioStartCommandQueue[this.timecodeAudioStartCommandQueue.length - 1] += createTimeoutCommand(result.commands, milliseconds);
                            }
                            // play end response
                            if (result.timecode.match(/RESPONSE_AUDIO_PLAY_END/i) !== null) {
                                this.timecodeAudioEndCommandQueue[this.timecodeAudioEndCommandQueue.length - 1] += createTimeoutCommand(result.commands, milliseconds);
                            }
                            // audio command in response
                            if (result.commands.match(audioCommandRegex) !== null) {
                                this.timecodeAudioStartCommandQueue.push('');
                                this.timecodeAudioEndCommandQueue.push('');
                            }
                            i++;
                        } else {
                            break;
                        }
                    }
                }
            }
            this.timecodeTimeoutFirstId = window.setTimeout(() => { this.ts_eval.run.call(this, batchcommands); }, 100);
        })
        .catch((error: Error) => {
            this.timecodeClear();
            console.error('Error:', error);
        });
    }

    MRQtimecodePlay(csvFile: string) {
        this.timecodeClear();

        fetch(csvFile).then(response => response.text()).then(data => {
            this.pixelStreaming.addResponseEventListener('timecode_mcloud_response', (data: string) => this.timecodeMCloudResponse(data));

            const lines = data.split(/(\r\n|\r|\n)/);

            // 清理無效行
            for (let i = lines.length - 1; i >= 0; i--) {
                lines[i] = lines[i].trim();
                if (lines[i].indexOf('//') == 0) {
                    lines[i] = lines[i].slice(0, lines[i].indexOf('//'));
                }
                if (lines[i].length == 0 || lines[i].indexOf(',') < 0 || lines[i].slice(lines[i].indexOf(',') + 1).length == 0) {
                    lines.splice(i, 1);
                }
            }

            function splitline(line: string) {
                let timecode = line.slice(0, line.indexOf(','));
                let commands = line.slice(line.indexOf(',') + 1);
                return { 'timecode': timecode, 'commands': commands };
            }

            function timecodeToMilliseconds(timecode: string) {
                try {
                    let timeArray = timecode.split(':');
                    if (timeArray.length == 3 && !isNaN(Number(timeArray[0])) && !isNaN(Number(timeArray[1])) && !isNaN(Number(timeArray[2]))) {
                        let hours = parseInt(timeArray[0], 10);
                        let minutes = parseInt(timeArray[1], 10);
                        let seconds = parseFloat(timeArray[2]);
                        let milliseconds = Math.round(seconds * 1000);
                        return (hours * 3600000) + (minutes * 60000) + milliseconds;
                    }
                } catch (error) {
                    console.log(error);
                }
                return null;
            }

            // 解析時間碼並存儲
            let timecodeEvents: { time: number, command: string, triggered: boolean }[] = [];
            for (let i = 0; i < lines.length; i++) {
                let result = splitline(lines[i]);
                let milliseconds = timecodeToMilliseconds(result.timecode);
                if (milliseconds !== null && result.commands.length > 0) {
                    timecodeEvents.push({ time: milliseconds, command: result.commands, triggered: false });
                }
            }

            // 監聽渲染狀態與 Frame 數
            let mrqState = document.getElementById('mrq-state');
            let mrqFrame = document.getElementById('mrq-frame');
            let MRQConfigSelect = document.getElementById('MRQConfig-select') as HTMLSelectElement;

            let lastExecutedFrame = -1;  // 紀錄上次觸發的幀數，防止重複執行
            let fps = (parseInt(MRQConfigSelect.value) % 2 === 0) ? 25 : 30; // 偶數=25FPS，奇數=30FPS

            // 監測 Frame 數據並觸發事件
            const checkFrameAndTriggerEvents = () => {
                if (mrqState?.textContent === "Rendering" && mrqFrame) {
                    let frameNumber = parseInt(mrqFrame.textContent || "0", 10);
                    if (!isNaN(frameNumber) && frameNumber !== lastExecutedFrame) {
                        lastExecutedFrame = frameNumber; // 更新已執行的幀數

                        let currentTimeMs = (frameNumber / fps) * 1000; // 計算當前時間點（毫秒）

                        // 找出所有應該執行的事件
                        timecodeEvents.forEach(event => {
                            if (!event.triggered && Math.abs(event.time - currentTimeMs) < (1000 / fps)) { // 誤差範圍內觸發
                                let commands = `this.${event.command};`;

                                if (commands.includes("TextToSpeech") || commands.includes("playFile")) {
                                    console.log("Skipped command:", commands); // 不執行這些指令
                                } else {
                                    try {
                                        new Function(commands).call(this); // 或 eval(commands);
                                        console.log("Executed command:", commands);
                                    } catch (error) {
                                        console.error("Error executing command:", error);
                                    }
                                }
                                event.triggered = true;
                            }
                        });
                    }
                }
                requestAnimationFrame(checkFrameAndTriggerEvents.bind(this));// 持續監測
            };

            requestAnimationFrame(checkFrameAndTriggerEvents.bind(this)); // 開始監測幀數

        }).catch((error: Error) => {
            this.timecodeClear();
            console.error('Error:', error);
        });
    }


//MRQ timecode End

    setupTTSTimeCodeEditor() {

        function mCloudAnimationResponse(data: string) {
            let jsonObj = JSON.parse(data);
            if (jsonObj.response == 'animationResponse' && jsonObj.event == 'ANIMATION_PROPERTIES_INFO') {
                let triggerAnimationSelect = document.getElementById('tc-triggerAnimation-select') as HTMLSelectElement;
                if (triggerAnimationSelect !== null) {
                    while (triggerAnimationSelect.options.length > 0) {
                        triggerAnimationSelect.remove(0);
                    }
                    let triggerAnimationDockContainer = document.getElementById('tc-triggerAnimation-dock-container');
                    if (triggerAnimationDockContainer !== null) {
                        triggerAnimationDockContainer.innerHTML = '';
                    }

                    // Update options depend on each avatar
                    jsonObj.triggerAnimationList.forEach(function(item: any) {
                        let newOption = new Option(item.name, item.id);
                        newOption.disabled = item.available != '1' ? true : false;
                        triggerAnimationSelect.add(newOption);
                    });

                    this.sortSelect(triggerAnimationSelect);

                    if (triggerAnimationSelect.options.length == 0) {
                        triggerAnimationSelect.add(new Option('None'));
                    }
                }
            }
        }
        const tmp_mcloudResponseBinded = mCloudAnimationResponse.bind(this);
        this._pixelStreaming.addResponseEventListener('tc_animation_response', tmp_mcloudResponseBinded);

        let triggerAnimationSelect = document.getElementById('tc-triggerAnimation-select') as HTMLSelectElement;
        let triggerAnimationButton = document.getElementById('tc-triggerAnimation-button');
        if (triggerAnimationSelect !== null && triggerAnimationButton !== null) {
            triggerAnimationButton.onclick = (event: Event) => {
                this.emitUIInteraction({ Animation: { id: triggerAnimationSelect.value, isBlendForth: 1, isBlendBack: 1 }});
            };
        }

        let triggerAnimationDock = document.getElementById('tc-triggerAnimation-dock');
        let triggerAnimationDockContainer = document.getElementById('tc-triggerAnimation-dock-container');
        if (triggerAnimationSelect !== null && triggerAnimationDockContainer !== null) {
            triggerAnimationDock.onclick = (event: Event) => {
                if (!this.isConnected) {
                    return;
                }

                var selectedText = triggerAnimationSelect.options[triggerAnimationSelect.selectedIndex].text;
                var selectedValue = triggerAnimationSelect.options[triggerAnimationSelect.selectedIndex].value;
                if (Array.prototype.some.call(triggerAnimationDockContainer.childNodes, (child: HTMLDivElement) => child.id == selectedText)) {
                    return;
                }

                var div = document.createElement('div');
                div.setAttribute('style', 'display:inline-block; margin:0px 5px 5px 0px;');
                div.id = selectedText;

                var button = document.createElement('button');
                button.setAttribute('animId', selectedValue);
                button.classList.add('btn', 'btn-primary', 'btn-sm', 'tc-triggerAnimation-dock');
                button.innerHTML = selectedText;
                button.onclick = (event: Event) => { this.emitUIInteraction({ Animation: { id: selectedValue, isBlendForth: 1, isBlendBack: 1 }}); };

                var delButton = document.createElement('button');
                delButton.classList.add('btn-close', 'btn-close-white');
                delButton.setAttribute('style', 'padding: 0em;');
                delButton.onclick = (event: Event) => { div.remove(); };

                div.appendChild(button);
                div.appendChild(delButton);
                triggerAnimationDockContainer.appendChild(div);
            };
        }

        let processBtn = document.getElementById('tc-process-button');
        if (processBtn !== null) {
            processBtn.onclick = (event: Event) => {
                let link = document.getElementById('tcDownload');
                if (link !== null) {
                    link.remove();
                }

                let text = (document.getElementById('tc-tts-text-area') as HTMLTextAreaElement)?.value.trim();
                if (text.length == 0) {
                    //alert('The input text is empty.');
                    return;
                }

                let symbols = ['。', '.', '；', ';', '！', '!', '？', '?', '\n', '\r\n'];
                let splitString = [];
                while (text.length > 0) {
                    let end = text.length;
                    for (var i = 0; i < text.length; i++) {
                        if (symbols.indexOf(text.charAt(i)) != -1) {
                            end = i + 1;
                            break;
                        }
                    }
                    let substring = text.slice(0, end).trim();
                    if (substring.length > 1) // not only symbols
                    {
                        splitString.push(substring.replace('\r\n', '\\\\r\\\\n').replace('\n', '\\\\n'));
                    }
                    text = text.slice(end);
                }

                let animIds: string[] = [];
                let elements = document.getElementsByClassName('tc-triggerAnimation-dock');
                Array.prototype.forEach.call(elements, function(element: Element) {
                    animIds.push(element.getAttribute('animId'));
                });

                // Returns a random number between min (inclusive) and max (exclusive)
                function getRandomInt(min: number, max: number) {
                    min = Math.ceil(min);
                    max = Math.floor(max);
                    return Math.floor(Math.random() * (max - min) + min); //The maximum is exclusive and the minimum is inclusive
                }

                let content = `00:00:00.000,emitUIInteraction({ TextToSpeech: { synthesis: { text: "${splitString[0]}" }}});\r\n`;
                if (animIds.length > 0) {
                    let animId = animIds[getRandomInt(0, animIds.length)]
                    content += `RESPONSE_AUDIO_PLAY_START+00:00:00.000,emitUIInteraction({ Animation: { id: ${animId}, isBlendForth: 1, isBlendBack: 1 }});\r\n`;
                }
                for (var i = 1; i < splitString.length; i++) {
                    content += `RESPONSE_AUDIO_PLAY_END+00:00:00.000,emitUIInteraction({ TextToSpeech: { synthesis: { text: "${splitString[i]}" }}});\r\n`;
                    if (animIds.length > 0) {
                        let animId = animIds[getRandomInt(0, animIds.length)]
                        content += `RESPONSE_AUDIO_PLAY_START+00:00:00.000,emitUIInteraction({ Animation: { id: ${animId}, isBlendForth: 1, isBlendBack: 1 }});\r\n`;
                    }
                }

                let data = new Blob([content], {type: 'text/plain'});
                link = document.createElement('a');
                link.setAttribute('id', 'tcDownload');
                link.setAttribute('href', URL.createObjectURL(data));
                link.setAttribute('download', 'TtsTimecode.csv');
                link.innerHTML = 'Download';
                processBtn.parentElement.appendChild(link);
            }
        }


    }

    /*******************************************************************************
     * Leap Motion Setup and Control
     ******************************************************************************/
    setupLeapMotion() {
        console.log('🖐️ Setting up Leap Motion...');

        // Check if Leap is available, if not, load it dynamically
        if (typeof (window as any).Leap === 'undefined') {
            console.log('📚 LeapJS library not loaded, loading dynamically...');
            this.loadLeapJSLibrary();
            return;
        }

        console.log('✅ LeapJS library already available');

        // Setup UI event handlers
        this.setupLeapUIHandlers();

        // Initialize Leap Motion controller
        this.initializeLeapController();
    }

    loadLeapJSLibrary() {
        this.updateLeapStatus('Loading library...', 'warning');

        const script = document.createElement('script');
        script.src = 'js/leap-1.1.1.js';
        script.onload = () => {
            console.log('LeapJS library loaded successfully');
            this.updateLeapStatus('Library loaded', 'success');

            // Setup UI event handlers
            this.setupLeapUIHandlers();

            // Initialize Leap Motion controller
            this.initializeLeapController();
        };
        script.onerror = () => {
            console.error('Failed to load LeapJS library');
            this.updateLeapStatus('Library load failed', 'error');
        };

        document.head.appendChild(script);
    }

    setupLeapUIHandlers() {
        // Enable/Disable toggle
        let leapEnableToggle = document.getElementById('leap-enable-tgl') as HTMLInputElement;
        if (leapEnableToggle !== null) {
            leapEnableToggle.onchange = (event: Event) => {
                this.leapEnabled = leapEnableToggle.checked;
                if (this.leapEnabled) {
                    this.startLeapMotion();
                } else {
                    this.stopLeapMotion();
                }
            };
        }

        // Control mode selection
        let leapControlMode = document.getElementById('leap-control-mode') as HTMLSelectElement;
        if (leapControlMode !== null) {
            leapControlMode.onchange = (event: Event) => {
                this.leapControlMode = leapControlMode.value;
                console.log('Leap control mode changed to:', this.leapControlMode);
            };
        }

        // Sensitivity adjustment
        let leapSensitivity = document.getElementById('leap-sensitivity') as HTMLInputElement;
        if (leapSensitivity !== null) {
            leapSensitivity.oninput = (event: Event) => {
                this.leapSensitivity = parseFloat(leapSensitivity.value);
                console.log('Leap sensitivity changed to:', this.leapSensitivity);
            };
        }

        // Outfit swipe toggle
        let leapOutfitSwipeToggle = document.getElementById('leap-outfit-swipe-tgl') as HTMLInputElement;
        if (leapOutfitSwipeToggle !== null) {
            leapOutfitSwipeToggle.onchange = (event: Event) => {
                this.outfitSwipeEnabled = leapOutfitSwipeToggle.checked;
                console.log('Outfit swipe enabled:', this.outfitSwipeEnabled);
            };
        }

        // Swipe sensitivity adjustment
        let leapSwipeSensitivity = document.getElementById('leap-swipe-sensitivity') as HTMLInputElement;
        if (leapSwipeSensitivity !== null) {
            leapSwipeSensitivity.oninput = (event: Event) => {
                this.swipeThreshold = parseFloat(leapSwipeSensitivity.value);
                console.log('Swipe sensitivity changed to:', this.swipeThreshold);
            };
        }
    }

    initializeLeapController() {
        try {
            console.log('🔧 Initializing Leap Motion controller...');

            // Create Leap Motion controller using the working configuration
            this.leapController = new (window as any).Leap.Controller({
                enableGestures: true,
                frameEventName: 'animationFrame'
            });

            console.log('📡 Setting up Leap Motion event listeners...');

            // Setup connection event listeners
            this.leapController.on('connect', () => {
                console.log('🔗 Leap Motion connected to service');
                this.updateLeapStatus('Connected', 'success');
            });

            this.leapController.on('disconnect', () => {
                console.log('❌ Leap Motion disconnected from service');
                this.updateLeapStatus('Disconnected', 'warning');
            });

            this.leapController.on('deviceAttached', (device: any) => {
                console.log('🖐️ Leap Motion device attached:', device);
                this.updateLeapStatus('Device Connected', 'success');
            });

            this.leapController.on('deviceRemoved', (device: any) => {
                console.log('🚫 Leap Motion device removed:', device);
                this.updateLeapStatus('Device Disconnected', 'warning');
            });

            this.leapController.on('ready', () => {
                console.log('✅ Leap Motion service ready');
            });

            // Use controller.loop method (proven to work)
            this.leapController.loop((frame: any) => {
                if (this.leapEnabled) {
                    this.processLeapFrame(frame);
                }
            });

            this.updateLeapStatus('Controller initialized', 'success');
            console.log('✅ Leap Motion controller initialized successfully');

            // Try to connect immediately
            console.log('🔌 Attempting to connect to Leap Motion service...');
            this.leapController.connect();

        } catch (error) {
            console.error('❌ Failed to initialize Leap Motion controller:', error);
            this.updateLeapStatus('Initialization failed', 'error');
        }
    }

    startLeapMotion() {
        if (this.leapController) {
            this.leapController.connect();
            console.log('Leap Motion started');
        } else {
            this.initializeLeapController();
        }
    }

    stopLeapMotion() {
        if (this.leapController) {
            this.leapController.disconnect();
            console.log('Leap Motion stopped');
        }
    }

    processLeapFrame(frame: any) {
        if (!frame || !frame.hands) {
            // Clear hand position history when no hands detected
            this.handPositionHistory = [];
            return;
        }

        const handCount = frame.hands.length;

        // Update hand count display
        this.updateLeapHandData(frame);

        // Always check for swipe gestures regardless of mode (if enabled)
        if (frame.hands.length > 0) {
            this.processSwipeDetection(frame.hands[0]);
        }

        // Process based on control mode
        switch (this.leapControlMode) {
            case 'gesture':
                this.processGestureControl(frame);
                break;
            case 'position':
                this.processPositionControl(frame);
                break;
            case 'animation':
                this.processAnimationControl(frame);
                break;
        }
    }

    processSwipeDetection(hand: any) {
        // Record hand position for swipe detection
        this.recordHandPosition(hand);

        // Detect swipe gestures
        const swipeDirection = this.detectSwipeGesture();
        if (swipeDirection) {
            const gesture = `Swipe ${swipeDirection}`;
            this.handleSwipeGesture(swipeDirection);

            // Update gesture display
            if (gesture !== this.lastGesture) {
                this.lastGesture = gesture;
                this.updateGestureDisplay(gesture);
            }
        }
    }

    processGestureControl(frame: any) {
        if (frame.hands.length === 0) return;

        const hand = frame.hands[0];
        let gesture = 'None';

        // Detect basic gestures (swipe detection is handled separately)
        if (hand.grabStrength > this.gestureThreshold) {
            gesture = 'Fist';
            // Trigger fist gesture action
            this.triggerGestureAction('fist');
        } else if (hand.pinchStrength > this.gestureThreshold) {
            gesture = 'Pinch';
            // Trigger pinch gesture action
            this.triggerGestureAction('pinch');
        } else {
            // Count extended fingers
            const extendedFingers = hand.fingers ? hand.fingers.filter((f: any) => f.extended).length : 0;

            if (extendedFingers === 1) {
                gesture = 'Point';
                this.triggerGestureAction('point');
            } else if (extendedFingers === 2) {
                gesture = 'Peace';
                this.triggerGestureAction('peace');
            } else if (extendedFingers === 5) {
                gesture = 'Open Hand';
                this.triggerGestureAction('open');
            }
        }

        // Update gesture display only if changed (and not a swipe)
        if (gesture !== this.lastGesture && !this.lastGesture.includes('Swipe')) {
            this.lastGesture = gesture;
            this.updateGestureDisplay(gesture);
        }
    }

    processPositionControl(frame: any) {
        if (frame.hands.length === 0) return;

        const hand = frame.hands[0];
        if (!hand.palmPosition) return;

        // Map hand position to camera/character control
        const x = hand.palmPosition[0] * this.leapSensitivity;
        const y = hand.palmPosition[1] * this.leapSensitivity;
        const z = hand.palmPosition[2] * this.leapSensitivity;

        // Send position-based commands to the backend
        this.emitUIInteraction({
            LeapMotion: {
                mode: 'position',
                position: { x: x, y: y, z: z }
            }
        });

        // Update gesture display for position mode (if not showing swipe)
        if (!this.lastGesture.includes('Swipe')) {
            const positionGesture = `Position (${x.toFixed(0)}, ${y.toFixed(0)}, ${z.toFixed(0)})`;
            if (positionGesture !== this.lastGesture) {
                this.lastGesture = positionGesture;
                this.updateGestureDisplay(positionGesture);
            }
        }
    }

    processAnimationControl(frame: any) {
        if (frame.hands.length === 0) return;

        const hand = frame.hands[0];

        // Trigger animations based on hand movements
        if (hand.grabStrength > this.gestureThreshold) {
            this.triggerRandomAnimation();
        }
    }

    triggerGestureAction(gestureType: string) {
        // Prevent rapid firing of the same gesture
        const now = Date.now();
        if (!this.lastGestureTime || now - this.lastGestureTime > 1000) {
            this.lastGestureTime = now;

            // Send gesture command to backend
            this.emitUIInteraction({
                LeapMotion: {
                    mode: 'gesture',
                    gesture: gestureType
                }
            });

            console.log('Leap gesture triggered:', gestureType);
        }
    }

    triggerRandomAnimation() {
        // Get available animations and trigger a random one
        const animSelect = document.getElementById('triggerAnimation-select') as HTMLSelectElement;
        if (animSelect && animSelect.options.length > 1) {
            const randomIndex = Math.floor(Math.random() * (animSelect.options.length - 1)) + 1;
            const animId = animSelect.options[randomIndex].value;

            this.emitUIInteraction({
                Animation: {
                    id: animId,
                    isBlendForth: 1,
                    isBlendBack: 1
                }
            });

            console.log('Leap triggered animation:', animId);
        }
    }

    updateLeapStatus(status: string, type: string) {
        const statusElement = document.getElementById('leap-status');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = type === 'success' ? 'text-success' :
                                     type === 'error' ? 'text-danger' : 'text-warning';
        }
    }

    updateLeapHandData(frame: any) {
        const handsCountElement = document.getElementById('leap-hands-count');
        const leftHandElement = document.getElementById('leap-left-hand');
        const rightHandElement = document.getElementById('leap-right-hand');

        if (handsCountElement) {
            handsCountElement.textContent = frame.hands.length.toString();
        }

        let leftHandInfo = '-';
        let rightHandInfo = '-';

        frame.hands.forEach((hand: any) => {
            const handInfo = `Pos: (${hand.palmPosition[0].toFixed(0)}, ${hand.palmPosition[1].toFixed(0)}, ${hand.palmPosition[2].toFixed(0)})`;

            if (hand.type === 'left') {
                leftHandInfo = handInfo;
            } else if (hand.type === 'right') {
                rightHandInfo = handInfo;
            }
        });

        if (leftHandElement) leftHandElement.textContent = leftHandInfo;
        if (rightHandElement) rightHandElement.textContent = rightHandInfo;
    }

    updateGestureDisplay(gesture: string) {
        const gestureElement = document.getElementById('leap-gesture');
        if (gestureElement) {
            gestureElement.textContent = gesture;
        }
    }

    // Add property for gesture timing
    private lastGestureTime: number = 0;

    /*******************************************************************************
     * Swipe Gesture Detection and Outfit Switching
     ******************************************************************************/
    recordHandPosition(hand: any) {
        if (!hand.palmPosition) return;

        const currentTime = Date.now();
        const position = {
            x: hand.palmPosition[0],
            y: hand.palmPosition[1],
            z: hand.palmPosition[2],
            timestamp: currentTime
        };

        this.handPositionHistory.push(position);

        // Remove old positions outside time window
        this.handPositionHistory = this.handPositionHistory.filter(
            pos => currentTime - pos.timestamp <= this.swipeTimeWindow
        );

        // Limit history length
        if (this.handPositionHistory.length > this.maxHistoryLength) {
            this.handPositionHistory.shift();
        }

        // 添加调试信息（可选）
        if (this.handPositionHistory.length > 1) {
            const lastPos = this.handPositionHistory[this.handPositionHistory.length - 1];
            const prevPos = this.handPositionHistory[this.handPositionHistory.length - 2];
            const deltaX = lastPos.x - prevPos.x;

            // 只在有明显移动时输出调试信息
            if (Math.abs(deltaX) > 5) {
                console.log(`Hand movement: deltaX=${deltaX.toFixed(1)}, positions=${this.handPositionHistory.length}`);
            }
        }
    }

    detectSwipeGesture(): string | null {
        // 降低最小点数要求
        if (this.handPositionHistory.length < 2) return null;

        const currentTime = Date.now();

        // Check cooldown
        if (currentTime - this.lastSwipeTime < this.swipeCooldown) {
            return null;
        }

        const firstPos = this.handPositionHistory[0];
        const lastPos = this.handPositionHistory[this.handPositionHistory.length - 1];

        // Calculate movement
        const deltaX = lastPos.x - firstPos.x;
        const deltaY = lastPos.y - firstPos.y;
        const deltaTime = lastPos.timestamp - firstPos.timestamp;

        // 更宽松的时间要求
        if (deltaTime < 100 || deltaTime > this.swipeTimeWindow) {
            return null;
        }

        // 计算水平和垂直距离
        const horizontalDistance = Math.abs(deltaX);
        const verticalDistance = Math.abs(deltaY);

        // 新的检测逻辑：
        // 1. 水平移动必须大于最小阈值
        // 2. 水平移动必须明显大于垂直移动（至少1.5倍）
        // 3. 计算移动速度，快速移动更容易被识别
        const speed = horizontalDistance / deltaTime * 1000; // 像素/秒

        if (horizontalDistance >= this.swipeThreshold &&
            horizontalDistance > verticalDistance * 1.5 &&
            speed > 30) { // 最小速度：30像素/秒

            console.log(`Swipe detected: deltaX=${deltaX.toFixed(1)}, deltaY=${deltaY.toFixed(1)}, speed=${speed.toFixed(1)}px/s, time=${deltaTime}ms`);

            if (deltaX > 0) {
                return 'Right';
            } else {
                return 'Left';
            }
        }

        return null;
    }

    handleSwipeGesture(direction: string) {
        this.lastSwipeTime = Date.now();

        // Check if outfit swipe is enabled
        if (!this.outfitSwipeEnabled) {
            console.log(`Swipe ${direction} detected but outfit swipe is disabled`);
            return;
        }

        console.log(`Swipe ${direction} detected - switching outfit`);

        if (direction === 'Left') {
            this.switchToPreviousOutfit();
        } else if (direction === 'Right') {
            this.switchToNextOutfit();
        }

        // Clear position history after swipe
        this.handPositionHistory = [];
    }

    switchToNextOutfit() {
        const outfitSelect = document.getElementById('outfit-select') as HTMLSelectElement;
        if (!outfitSelect || outfitSelect.options.length <= 1) {
            console.log('No outfits available to switch');
            return;
        }

        let currentIndex = outfitSelect.selectedIndex;
        let nextIndex = (currentIndex + 1) % outfitSelect.options.length;

        // Skip disabled options
        while (outfitSelect.options[nextIndex].disabled && nextIndex !== currentIndex) {
            nextIndex = (nextIndex + 1) % outfitSelect.options.length;
        }

        if (nextIndex !== currentIndex) {
            outfitSelect.selectedIndex = nextIndex;
            const selectedValue = outfitSelect.value;

            this.emitUIInteraction({ Outfit: { id: selectedValue }});
            console.log(`Switched to next outfit: ${outfitSelect.options[nextIndex].text} (${selectedValue})`);

            // Show visual feedback
            this.showOutfitSwitchFeedback(`➡️ ${outfitSelect.options[nextIndex].text}`);
        }
    }

    switchToPreviousOutfit() {
        const outfitSelect = document.getElementById('outfit-select') as HTMLSelectElement;
        if (!outfitSelect || outfitSelect.options.length <= 1) {
            console.log('No outfits available to switch');
            return;
        }

        let currentIndex = outfitSelect.selectedIndex;
        let prevIndex = currentIndex === 0 ? outfitSelect.options.length - 1 : currentIndex - 1;

        // Skip disabled options
        while (outfitSelect.options[prevIndex].disabled && prevIndex !== currentIndex) {
            prevIndex = prevIndex === 0 ? outfitSelect.options.length - 1 : prevIndex - 1;
        }

        if (prevIndex !== currentIndex) {
            outfitSelect.selectedIndex = prevIndex;
            const selectedValue = outfitSelect.value;

            this.emitUIInteraction({ Outfit: { id: selectedValue }});
            console.log(`Switched to previous outfit: ${outfitSelect.options[prevIndex].text} (${selectedValue})`);

            // Show visual feedback
            this.showOutfitSwitchFeedback(`⬅️ ${outfitSelect.options[prevIndex].text}`);
        }
    }

    showOutfitSwitchFeedback(message: string) {
        // Update the gesture display to show outfit switch feedback
        const gestureElement = document.getElementById('leap-gesture');
        if (gestureElement) {
            const originalText = gestureElement.textContent;
            gestureElement.textContent = message;
            gestureElement.style.color = '#4CAF50';
            gestureElement.style.fontWeight = 'bold';

            // Reset after 2 seconds
            setTimeout(() => {
                gestureElement.textContent = originalText;
                gestureElement.style.color = '';
                gestureElement.style.fontWeight = '';
            }, 2000);
        }
    }

    /*******************************************************************************
     * Voice Assistant Setup
     ******************************************************************************/
    setupVoiceAssistant() {
        console.log('Setting up Voice Assistant...');

        // Load voice assistant scripts dynamically
        this.loadVoiceAssistantScripts();
    }

    loadVoiceAssistantScripts() {
        const scripts = [
            'js/keyword-wakeup.js',
            'js/auto-conversation.js',
            'js/voice-assistant-coordinator.js'
        ];

        let loadedCount = 0;
        const totalScripts = scripts.length;

        scripts.forEach((scriptSrc, index) => {
            const script = document.createElement('script');
            script.src = scriptSrc;
            script.onload = () => {
                loadedCount++;
                console.log(`Voice Assistant script loaded: ${scriptSrc}`);

                if (loadedCount === totalScripts) {
                    console.log('All Voice Assistant scripts loaded successfully');
                    this.initializeVoiceAssistant();
                }
            };
            script.onerror = () => {
                console.error(`Failed to load Voice Assistant script: ${scriptSrc}`);
                loadedCount++;

                if (loadedCount === totalScripts) {
                    console.log('Voice Assistant script loading completed (some may have failed)');
                    this.initializeVoiceAssistant();
                }
            };

            document.head.appendChild(script);
        });
    }

    initializeVoiceAssistant() {
        console.log('Initializing Voice Assistant features...');

        // Check if voice assistant functions are available
        if (typeof (window as any).initializeKeywordWakeup === 'function') {
            try {
                (window as any).initializeKeywordWakeup();
                console.log('Keyword wakeup initialized');
            } catch (error) {
                console.warn('Failed to initialize keyword wakeup:', error);
            }
        }

        if (typeof (window as any).initializeAutoConversation === 'function') {
            try {
                (window as any).initializeAutoConversation();
                console.log('Auto conversation initialized');
            } catch (error) {
                console.warn('Failed to initialize auto conversation:', error);
            }
        }

        if (typeof (window as any).initializeVoiceAssistantCoordinator === 'function') {
            try {
                (window as any).initializeVoiceAssistantCoordinator();
                console.log('Voice assistant coordinator initialized');
            } catch (error) {
                console.warn('Failed to initialize voice assistant coordinator:', error);
            }
        }

        console.log('Voice Assistant initialization completed');
    }

    /**
     * 设置视频窗口通信
     */
    setupVideoWindowCommunication() {
        console.log('设置视频窗口通信');

        // 监听来自视频窗口的消息
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'REQUEST_AUDIO_PERMISSION') {
                console.log('收到视频窗口的音频权限请求');

                // 利用主屏幕的用户授权（点击播放按钮），直接授予权限
                console.log('利用主屏幕授权，直接授予权限');
                (event.source as Window).postMessage({type: 'AUDIO_PERMISSION_GRANTED'}, '*');
            }
        });
    }

    /**
     * 设置视频播放器
     */
    setupVideoPlayer() {
        console.log('设置视频播放器');

        // 设置关闭按钮事件
        const closeVideoBtn = document.getElementById('close-video-btn');
        if (closeVideoBtn) {
            closeVideoBtn.onclick = () => {
                const videoContainer = document.getElementById('embedded-video-container');
                const video = document.getElementById('embedded-video') as HTMLVideoElement;

                if (videoContainer) {
                    videoContainer.classList.add('hidden');
                }

                if (video) {
                    video.pause();
                    video.currentTime = 0;
                }

                console.log('教学视频已关闭');
            };
            console.log('视频关闭按钮事件已绑定');
        } else {
            console.warn('视频关闭按钮未找到');
        }
    }

    /**
     * 视频关键词映射表
     */
    private videoKeywordMap = {
        // 蹲距式起跑相关关键词
        '蹲距式起跑': 'videos/squat_start_technique.mp4',
        '起跑': 'videos/squat_start_technique.mp4',
        '起跑姿势': 'videos/squat_start_technique.mp4',
        '起跑动作': 'videos/squat_start_technique.mp4',
        '蹲距式': 'videos/squat_start_technique.mp4',
        '短跑起跑': 'videos/squat_start_technique.mp4',
        '田径起跑': 'videos/squat_start_technique.mp4',
        '正确姿势': 'videos/squat_start_technique.mp4',
        '起跑技巧': 'videos/squat_start_technique.mp4',
        '起跑要领': 'videos/squat_start_technique.mp4',
        '起跑方法': 'videos/squat_start_technique.mp4',
        '如何起跑': 'videos/squat_start_technique.mp4',
        '起跑教学': 'videos/squat_start_technique.mp4',
        '起跑训练': 'videos/squat_start_technique.mp4',
        'starting position': 'videos/squat_start_technique.mp4',
        'sprint start': 'videos/squat_start_technique.mp4',
        'crouch start': 'videos/squat_start_technique.mp4'
    };

    /**
     * 检查文本中是否包含视频关键词
     */
    private findMatchingVideo(text: string): string | null {
        const lowerText = text.toLowerCase();
        for (const [keyword, videoPath] of Object.entries(this.videoKeywordMap)) {
            if (lowerText.includes(keyword.toLowerCase()) ||
                lowerText.includes(keyword) ||
                text.includes(keyword)) {
                console.log(`匹配到关键词: ${keyword}, 对应视频: ${videoPath}`);
                return videoPath;
            }
        }
        return null;
    }

    /**
     * 直接播放视频（跳过AI对话）
     */
    private async playVideoDirectly(videoPath: string, voiceId: string, voiceSpeed: string) {
        console.log('直接播放视频模式:', videoPath);

        // 获取视频名称（去掉路径和扩展名）
        const videoName = videoPath.split('/').pop()?.replace('.mp4', '') || '教学视频';
        const videoDisplayName = this.getVideoDisplayName(videoName);

        // 语音播报
        const announcement = `我将为您播放${videoDisplayName}`;
        console.log('语音播报:', announcement);

        // 在聊天界面显示播报信息
        let chatOverlay = document.getElementById('chatOverlay');
        if (chatOverlay) {
            chatOverlay.innerHTML += `<span class="chatSpan"><img src="/images/answer.png" style="height:1em;" />&nbsp;${announcement}</span>`;
            chatOverlay.scrollTop = chatOverlay.scrollHeight;
        }

        // 立即开始语音播报
        this.emitUIInteraction({
            TextToSpeech: {
                voiceConfig: { voiceId: voiceId, voiceSpeed: voiceSpeed },
                text: announcement
            }
        });

        // 检查是否是"起跑"相关视频，如果是则启用音频同步播放
        const isStartingVideo = this.isStartingRelatedVideo(videoPath);

        // 2秒后播放视频
        setTimeout(() => {
            console.log('2秒延迟后开始播放视频');
            if (isStartingVideo) {
                this.showInstructionalVideoWithAudio(videoPath);
            } else {
                this.showInstructionalVideo(videoPath);
            }
        }, 2000);

        // 清空输入框
        let textArea = document.getElementById('tts-chat-area') as HTMLTextAreaElement;
        if (textArea) {
            textArea.value = '';
        }
    }

    /**
     * 获取视频显示名称
     */
    private getVideoDisplayName(videoName: string): string {
        const videoNameMap: { [key: string]: string } = {
            'squat_start_technique': '蹲距式起跑技术教学视频',
            // 可以在这里添加更多视频名称映射
        };

        return videoNameMap[videoName] || videoName;
    }

    /**
     * 检查是否是起跑相关视频
     */
    private isStartingRelatedVideo(videoPath: string): boolean {
        // 检查视频路径是否包含起跑相关的视频
        return videoPath.includes('squat_start_technique.mp4');
    }

    /**
     * 显示教学视频并同时播放音频
     */
    private showInstructionalVideoWithAudio(videoPath: string) {
        console.log('显示教学视频并同时播放音频:', videoPath);

        // 保存当前音量设置
        this.saveCurrentAudioVolume();

        // 设置音量为0
        this.setAudioVolume('0');

        // 播放output.mp3音频文件
        this.playOutputAudio();

        // 设置状态标志
        this.isPlayingVideoWithAudio = true;

        // 优先尝试使用Electron播放器
        this.tryElectronVideoPlayerWithAudio(videoPath).then((success) => {
            if (!success) {
                console.log('Electron播放器不可用，使用浏览器方案');
                this.showAuthorizationDialogWithAudio(videoPath);
            }
        });
    }

    /**
     * 保存当前音量设置
     */
    private saveCurrentAudioVolume() {
        const audioVolumeElement = document.getElementById('audio-volume') as HTMLInputElement;
        if (audioVolumeElement) {
            this.savedAudioVolume = audioVolumeElement.value;
            console.log('已保存当前音量:', this.savedAudioVolume);
        }
    }

    /**
     * 设置音频音量
     */
    private setAudioVolume(volume: string) {
        const audioVolumeElement = document.getElementById('audio-volume') as HTMLInputElement;
        if (audioVolumeElement) {
            audioVolumeElement.value = volume;
            // 触发音量变更事件
            this.emitUIInteraction({ Audio: { volume: volume }});
            console.log('已设置音量为:', volume);
        }
    }

    /**
     * 恢复之前保存的音量设置
     */
    private restoreAudioVolume() {
        this.setAudioVolume(this.savedAudioVolume);
        console.log('已恢复音量为:', this.savedAudioVolume);
    }

    /**
     * 播放output.mp3音频文件
     */
    private playOutputAudio() {
        console.log('开始播放output.mp3');
        this.emitUIInteraction({ Audio: { playFile: 'output.mp3' }});
    }

    /**
     * 停止音频播放
     */
    private stopOutputAudio() {
        console.log('停止播放output.mp3');
        this.emitUIInteraction({ Audio: { reset: '' }});
    }

    /**
     * 尝试使用Electron视频播放器（带音频同步）
     */
    private tryElectronVideoPlayerWithAudio(videoPath: string): Promise<boolean> {
        return new Promise((resolve) => {
            console.log('尝试连接Electron视频播放器（音频同步模式）');

            try {
                // 创建WebSocket连接到Electron应用
                const ws = new WebSocket('ws://localhost:8765');

                ws.onopen = () => {
                    console.log('✅ 已连接到Electron视频播放器（音频同步模式）');

                    // 发送播放视频指令
                    const message = {
                        type: 'PLAY_VIDEO',
                        videoPath: videoPath
                    };

                    ws.send(JSON.stringify(message));
                    console.log('已发送视频播放指令:', message);

                    // 在聊天界面显示提示
                    let chatOverlay = document.getElementById('chatOverlay');
                    if (chatOverlay) {
                        chatOverlay.innerHTML += '<span class="chatSpan" style="color: #0066cc; font-style: italic;">🎬 教学视频正在第二屏幕自动播放，同时播放配套音频（Electron）</span>';
                        chatOverlay.scrollTop = chatOverlay.scrollHeight;
                    }

                    // 监听视频播放结束的消息
                    ws.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            if (data.type === 'VIDEO_ENDED') {
                                console.log('收到视频播放结束通知');
                                this.onVideoWithAudioEnded();
                                ws.close(); // 收到结束消息后关闭连接
                            }
                        } catch (e) {
                            console.log('解析WebSocket消息失败:', e);
                        }
                    };

                    // 不立即关闭连接，等待视频结束消息
                    resolve(true);
                };

                ws.onerror = (error) => {
                    console.log('❌ Electron视频播放器连接失败:', error);
                    resolve(false);
                };

                ws.onclose = () => {
                    console.log('与Electron视频播放器的连接已关闭');
                };

                // 5秒超时
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        console.log('❌ Electron视频播放器连接超时');
                        ws.close();
                        resolve(false);
                    }
                }, 5000);

            } catch (error) {
                console.log('❌ 创建WebSocket连接失败:', error);
                resolve(false);
            }
        });
    }

    /**
     * 视频播放结束时的处理（音频同步模式）
     */
    private onVideoWithAudioEnded() {
        console.log('视频播放结束，执行音频同步清理');

        // 停止播放output.mp3
        this.stopOutputAudio();

        // 恢复音量设置
        this.restoreAudioVolume();

        // 重置状态标志
        this.isPlayingVideoWithAudio = false;
        this.currentVideoWindow = null;

        console.log('音频同步清理完成');
    }

    /**
     * 显示授权对话框（带音频同步）
     */
    private showAuthorizationDialogWithAudio(videoPath: string) {
        console.log('显示授权对话框（音频同步模式）:', videoPath);

        // 在聊天界面显示提示
        let chatOverlay = document.getElementById('chatOverlay');
        if (chatOverlay) {
            chatOverlay.innerHTML += '<span class="chatSpan" style="color: #ff6600; font-style: italic;">🎬 教学视频需要您的授权才能播放，同时播放配套音频</span>';
            chatOverlay.scrollTop = chatOverlay.scrollHeight;
        }

        // 创建授权对话框
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content bg-dark text-white">
                    <div class="modal-header">
                        <h5 class="modal-title">🎬 视频播放授权</h5>
                    </div>
                    <div class="modal-body">
                        <p>系统将在新窗口播放教学视频，同时播放配套音频。</p>
                        <p>请点击"允许播放"来启动视频播放。</p>
                        <p><small class="text-muted">注意：音量将临时设置为0，视频结束后自动恢复</small></p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="allow-video-play-with-audio">允许播放</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        const bootstrapModal = new Modal(modal);
        bootstrapModal.show();

        // 处理允许播放按钮点击
        const allowButton = modal.querySelector('#allow-video-play-with-audio');
        if (allowButton) {
            allowButton.addEventListener('click', async () => {
                bootstrapModal.hide();
                await this.openVideoWindowWithAudio(videoPath);
            });
        }

        // 清理模态框
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    /**
     * 在新窗口打开视频（带音频同步）
     */
    private async openVideoWindowWithAudio(videoPath: string) {
        console.log('在新窗口打开视频（音频同步模式）:', videoPath);

        try {
            // 获取配置
            const configManager = VideoPlayerConfigManager.getInstance();
            const videoWindow = await configManager.getVideoWindowConfig();

            // 计算窗口位置
            const screenWidth = window.screen.availWidth;
            const screenHeight = window.screen.availHeight;
            const x = screenWidth - videoWindow.size.width - videoWindow.defaultPosition.offsetFromRight;
            const y = videoWindow.defaultPosition.offsetFromTop;

            // 构建视频URL
            const videoUrl = `/videos/${videoPath.split('/').pop()}`;
            const windowUrl = `/viewer.html?video=${encodeURIComponent(videoUrl)}&autoplay=true&enableSound=true`;

            // 打开新窗口
            this.currentVideoWindow = window.open(
                windowUrl,
                'videoPlayer',
                `width=${videoWindow.size.width},height=${videoWindow.size.height},left=${x},top=${y},${videoWindow.features}`
            );

            if (this.currentVideoWindow) {
                console.log('视频窗口已打开（音频同步模式）');

                // 在聊天界面显示提示
                let chatOverlay = document.getElementById('chatOverlay');
                if (chatOverlay) {
                    chatOverlay.innerHTML += '<span class="chatSpan" style="color: #0066cc; font-style: italic;">🎬 教学视频正在新窗口播放，同时播放配套音频</span>';
                    chatOverlay.scrollTop = chatOverlay.scrollHeight;
                }

                // 监听窗口关闭事件
                const checkClosed = setInterval(() => {
                    if (this.currentVideoWindow && this.currentVideoWindow.closed) {
                        console.log('视频窗口已关闭');
                        clearInterval(checkClosed);
                        this.onVideoWithAudioEnded();
                    }
                }, 1000);

                // 设置最大监听时间（10分钟）
                setTimeout(() => {
                    clearInterval(checkClosed);
                    if (this.isPlayingVideoWithAudio) {
                        console.log('视频播放超时，执行清理');
                        this.onVideoWithAudioEnded();
                    }
                }, 600000); // 10分钟

            } else {
                console.error('无法打开视频窗口');
                this.onVideoWithAudioEnded(); // 清理状态
            }

        } catch (error) {
            console.error('打开视频窗口失败:', error);
            this.onVideoWithAudioEnded(); // 清理状态
        }
    }



    /**
     * 带权限检查的语音识别启动
     */
    private async startSpeechRecognitionWithPermissionCheck() {
        try {
            // 检查是否已经在识别中
            if (this.isRecognizing) {
                console.log('⚠️ 语音识别已在运行中，跳过启动');
                return;
            }

            // 首先检查麦克风权限
            console.log('检查麦克风权限...');
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            console.log('✅ 麦克风权限正常');

            // 立即停止流，我们只是检查权限
            stream.getTracks().forEach(track => track.stop());

            // 再次检查状态，防止在权限检查期间状态发生变化
            if (this.isRecognizing) {
                console.log('⚠️ 在权限检查期间语音识别已启动，跳过');
                return;
            }

            // 清空文本框
            let textControl = document.getElementById('tts-chat-area') as HTMLTextAreaElement;
            if (textControl) {
                textControl.value = '';
            }

            // 设置语言
            let langControl = document.getElementById('tts-chat-mic-lang-control') as HTMLSelectElement;
            if (langControl) {
                this.speechRecognition.lang = langControl.value;
                console.log('设置语音识别语言:', langControl.value);
            }

            // 启动语音识别
            this.speechRecognition.start();
            console.log('语音识别已启动');

        } catch (error) {
            console.error('❌ 麦克风权限被拒绝或其他错误:', error);
            let errorMessage = '无法访问麦克风。请检查：\n';
            errorMessage += '1. 浏览器麦克风权限是否允许\n';
            errorMessage += '2. 系统麦克风权限是否开启\n';
            errorMessage += '3. 是否有其他应用占用麦克风';
            this.showSpeechRecognitionError(errorMessage);
        }
    }

    /**
     * 显示教学视频
     */
    private showInstructionalVideo(videoPath: string) {
        console.log('显示教学视频:', videoPath);

        // 优先尝试使用Electron播放器
        this.tryElectronVideoPlayer(videoPath).then((success) => {
            if (!success) {
                console.log('Electron播放器不可用，使用浏览器方案');
                this.showAuthorizationDialog(videoPath);
            }
        });
    }

    /**
     * 尝试使用Electron视频播放器
     */
    private tryElectronVideoPlayer(videoPath: string): Promise<boolean> {
        return new Promise((resolve) => {
            console.log('尝试连接Electron视频播放器');

            try {
                // 创建WebSocket连接到Electron应用
                const ws = new WebSocket('ws://localhost:8765');

                ws.onopen = () => {
                    console.log('✅ 已连接到Electron视频播放器');

                    // 发送播放视频指令
                    const message = {
                        type: 'PLAY_VIDEO',
                        videoPath: videoPath
                    };

                    ws.send(JSON.stringify(message));
                    console.log('已发送视频播放指令:', message);

                    // 在聊天界面显示提示
                    let chatOverlay = document.getElementById('chatOverlay');
                    if (chatOverlay) {
                        chatOverlay.innerHTML += '<span class="chatSpan" style="color: #0066cc; font-style: italic;">🎬 教学视频正在第二屏幕自动播放（Electron）</span>';
                        chatOverlay.scrollTop = chatOverlay.scrollHeight;
                    }

                    ws.close();
                    resolve(true);
                };

                ws.onerror = (error) => {
                    console.log('❌ Electron视频播放器连接失败:', error);
                    resolve(false);
                };

                ws.onclose = () => {
                    console.log('与Electron视频播放器的连接已关闭');
                };

                // 5秒超时
                setTimeout(() => {
                    if (ws.readyState === WebSocket.CONNECTING) {
                        console.log('⏰ 连接Electron播放器超时');
                        ws.close();
                        resolve(false);
                    }
                }, 5000);

            } catch (error) {
                console.log('❌ 创建WebSocket连接失败:', error);
                resolve(false);
            }
        });
    }

    /**
     * 在主屏幕显示授权对话框
     */
    private showAuthorizationDialog(videoPath: string) {
        console.log('在主屏幕显示授权对话框');

        // 移除已存在的对话框
        const existingDialog = document.getElementById('video-auth-dialog');
        if (existingDialog) {
            existingDialog.remove();
        }

        // 创建授权对话框
        const dialog = document.createElement('div');
        dialog.id = 'video-auth-dialog';
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            z-index: 10000;
            text-align: center;
            color: white;
            font-family: Arial, sans-serif;
            width: 300px;
        `;

        // 添加内容
        dialog.innerHTML = `
            <h3 style="margin-top: 0; color: white;">播放教学视频</h3>
            <p>系统将在第二屏幕播放教学视频</p>
            <div style="margin: 20px 0;">
                <img src="/images/mic-anim.gif" style="width: 50px; height: 50px;" />
            </div>
            <button id="auth-play-btn" style="
                background: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                margin-right: 10px;
            ">播放视频</button>
            <button id="auth-cancel-btn" style="
                background: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
            ">取消</button>
        `;

        // 添加到页面
        document.body.appendChild(dialog);

        // 绑定事件
        const playBtn = document.getElementById('auth-play-btn');
        const cancelBtn = document.getElementById('auth-cancel-btn');

        if (playBtn) {
            playBtn.onclick = () => {
                console.log('用户授权播放视频');
                dialog.remove();

                // 直接在第二屏幕打开视频，显示播放按钮
                console.log('在第二屏幕打开视频，等待用户点击播放');
                this.openVideoOnSecondScreen(videoPath, true);

                // 在聊天界面显示提示
                let chatOverlay = document.getElementById('chatOverlay');
                if (chatOverlay) {
                    chatOverlay.innerHTML += '<span class="chatSpan" style="color: #0066cc; font-style: italic;">📹 教学视频窗口已打开，请在第二屏幕点击播放按钮</span>';
                    chatOverlay.scrollTop = chatOverlay.scrollHeight;
                }
            };
        }

        if (cancelBtn) {
            cancelBtn.onclick = () => {
                console.log('用户取消播放视频');
                dialog.remove();

                // 在聊天界面显示提示
                let chatOverlay = document.getElementById('chatOverlay');
                if (chatOverlay) {
                    chatOverlay.innerHTML += '<span class="chatSpan" style="color: #f44336; font-style: italic;">❌ 已取消播放教学视频</span>';
                    chatOverlay.scrollTop = chatOverlay.scrollHeight;
                }
            };
        }
    }

    /**
     * 为新窗口激活音频权限
     */
    private activateAudioPermissionForNewWindow(): Promise<void> {
        return new Promise((resolve, reject) => {
            // 创建一个极短的静音音频来激活权限
            const audio = document.createElement('audio');
            audio.muted = true;
            audio.volume = 0;
            audio.preload = 'auto';

            // 创建一个极短的音频数据URL（0.1秒的静音）
            const audioData = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmHgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
            audio.src = audioData;

            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('主屏幕静音音频播放成功，权限已激活');
                    audio.remove();
                    resolve();
                }).catch(() => {
                    console.log('主屏幕静音音频播放失败');
                    audio.remove();
                    reject();
                });
            } else {
                audio.remove();
                resolve();
            }
        });
    }

    /**
     * 在第二屏幕打开已授权的视频
     */
    private openVideoOnSecondScreenWithAuth(videoPath: string) {
        console.log('在第二屏幕打开已授权的视频:', videoPath);

        // 生成一个授权令牌
        const authToken = Date.now().toString();

        // 将授权信息存储到sessionStorage，供新窗口读取
        sessionStorage.setItem('videoAuthToken', authToken);
        sessionStorage.setItem('videoAuthTime', Date.now().toString());

        // 在URL中传递授权信息
        this.openVideoOnSecondScreen(videoPath + '?auth=' + authToken, true);
    }

    /**
     * 通过播放静音音频来激活音频权限
     */
    private activateAudioPermission(): Promise<void> {
        return new Promise((resolve, reject) => {
            // 创建一个极短的静音音频
            const audio = document.createElement('audio');
            audio.muted = true;
            audio.volume = 0;

            // 创建一个极短的音频数据URL（0.1秒的静音）
            const audioData = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmHgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
            audio.src = audioData;

            const playPromise = audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('静音音频播放成功，权限已激活');
                    audio.remove();
                    resolve();
                }).catch(() => {
                    audio.remove();
                    reject();
                });
            } else {
                audio.remove();
                resolve();
            }
        });
    }

    /**
     * 在当前页面创建浮动视频播放器
     */
    private async createFloatingVideoPlayer(videoPath: string) {
        console.log('创建浮动视频播放器:', videoPath);

        // 移除已存在的视频播放器
        const existingPlayer = document.getElementById('floating-video-player');
        if (existingPlayer) {
            existingPlayer.remove();
        }

        // 从配置文件获取样式
        const configManager = VideoPlayerConfigManager.getInstance();
        const containerStyle = await configManager.getFloatingContainerStyle();

        // 创建浮动视频容器
        const videoContainer = document.createElement('div');
        videoContainer.id = 'floating-video-player';
        videoContainer.style.cssText = containerStyle;

        // 从配置文件获取视频和按钮样式
        const videoStyle = await configManager.getVideoStyle();
        const closeButtonStyle = await configManager.getCloseButtonStyle();

        // 创建视频元素
        const video = document.createElement('video');
        video.id = 'floating-video';
        video.src = videoPath;
        video.autoplay = true;
        video.muted = false; // 直接设置为有声音
        video.controls = false; // 隐藏控件
        video.style.cssText = videoStyle;

        // 创建关闭按钮
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '×';
        closeButton.style.cssText = closeButtonStyle;

        // 组装元素
        videoContainer.appendChild(video);
        videoContainer.appendChild(closeButton);
        document.body.appendChild(videoContainer);

        // 绑定事件
        closeButton.onclick = () => {
            video.pause();
            videoContainer.remove();
            console.log('浮动视频播放器已关闭');
        };

        // 视频播放完毕自动关闭
        video.addEventListener('ended', () => {
            console.log('视频播放完毕，3秒后自动关闭');
            setTimeout(() => {
                videoContainer.remove();
            }, 3000);
        });

        // 尝试播放
        const playPromise = video.play();
        if (playPromise !== undefined) {
            playPromise.then(() => {
                console.log('🎉 浮动视频有声音播放成功！');

                // 在聊天界面显示提示
                let chatOverlay = document.getElementById('chatOverlay');
                if (chatOverlay) {
                    chatOverlay.innerHTML += '<span class="chatSpan" style="color: #0066cc; font-style: italic;">📹 教学视频正在右上角播放</span>';
                    chatOverlay.scrollTop = chatOverlay.scrollHeight;
                }
            }).catch(error => {
                console.log('浮动视频播放失败:', error);
                // 如果还是失败，尝试静音播放
                video.muted = true;
                video.play().then(() => {
                    console.log('浮动视频静音播放成功');
                }).catch(err => {
                    console.error('浮动视频连静音都失败:', err);
                });
            });
        }

        console.log('浮动视频播放器创建完成');
    }

    /**
     * 通过在主页面播放静音音频来获取自动播放权限
     */
    private enableAutoplayPermission(): Promise<void> {
        return new Promise((resolve, reject) => {
            // 创建一个极短的静音音频来获取播放权限
            const audio = document.createElement('audio');
            audio.muted = true;
            audio.autoplay = true;
            audio.loop = false;

            // 创建一个极短的静音音频数据
            const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
            const buffer = audioContext.createBuffer(1, 1, 22050);
            const source = audioContext.createBufferSource();
            source.buffer = buffer;
            source.connect(audioContext.destination);

            // 尝试播放来获取权限
            const playPromise = audioContext.resume();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('音频上下文已激活，获得播放权限');
                    resolve();
                }).catch(() => {
                    reject();
                });
            } else {
                resolve();
            }
        });
    }

    /**
     * 智能多屏幕视频播放
     */
    private async openVideoOnSecondScreen(videoPath: string, enableSound: boolean = false) {
        console.log('尝试在第二个屏幕打开视频:', videoPath, enableSound ? '(有声音)' : '(静音)');

        // 获取配置文件中的窗口配置
        const configManager = VideoPlayerConfigManager.getInstance();
        const windowConfig = await configManager.getVideoWindowConfig();

        try {
            // 尝试使用现代浏览器的Screen API检测多屏幕
            if ('getScreenDetails' in window) {
                const screenDetails = await (window as any).getScreenDetails();
                console.log('检测到的屏幕信息:', screenDetails);

                if (screenDetails.screens.length > 1) {
                    // 找到第二个屏幕
                    const secondScreen = screenDetails.screens[1];
                    console.log('第二个屏幕信息:', secondScreen);

                    // 使用配置文件中的偏移量计算第二个屏幕位置
                    const x = secondScreen.left + secondScreen.width - windowConfig.defaultPosition.offsetFromRight;
                    const y = secondScreen.top + windowConfig.defaultPosition.offsetFromTop;

                    await this.openVideoWindowAtPosition(videoPath, x, y, '第二个屏幕', enableSound);
                    return;
                }
            }

            // 如果没有Screen API或只有一个屏幕，使用传统方法估算
            await this.openVideoWithScreenDetection(videoPath, enableSound);

        } catch (error) {
            console.log('Screen API不可用，使用传统方法:', error);
            await this.openVideoWithScreenDetection(videoPath);
        }
    }

    /**
     * 传统屏幕检测方法
     */
    private async openVideoWithScreenDetection(videoPath: string, enableSound: boolean = false) {
        console.log('使用传统方法检测屏幕配置');

        // 获取配置文件中的窗口配置
        const configManager = VideoPlayerConfigManager.getInstance();
        const windowConfig = await configManager.getVideoWindowConfig();

        // 获取屏幕信息
        const screenWidth = screen.width;
        const screenHeight = screen.height;
        const availWidth = screen.availWidth;
        const availHeight = screen.availHeight;

        console.log(`屏幕信息: ${screenWidth}x${screenHeight}, 可用: ${availWidth}x${availHeight}`);

        // 检测是否可能有多屏幕（这是一个估算方法）
        const windowOuterWidth = window.outerWidth;
        const windowScreenX = window.screenX || window.screenLeft || 0;

        console.log(`窗口位置: X=${windowScreenX}, 窗口宽度: ${windowOuterWidth}`);

        // 尝试不同的屏幕位置
        let targetX, targetY, screenInfo;

        // 方法1: 尝试在当前屏幕右侧（假设有第二个屏幕）
        if (screenWidth > 1920) { // 可能是超宽屏或多屏
            targetX = screenWidth - windowConfig.defaultPosition.offsetFromRight;
            targetY = windowConfig.defaultPosition.offsetFromTop;
            screenInfo = '检测到宽屏，尝试右侧显示';
        }
        // 方法2: 尝试在估算的第二个屏幕位置
        else {
            targetX = screenWidth + 100; // 尝试在第一个屏幕右侧
            targetY = windowConfig.defaultPosition.offsetFromTop;
            screenInfo = '尝试第二个屏幕位置';
        }

        await this.openVideoWindowAtPosition(videoPath, targetX, targetY, screenInfo, enableSound);
    }

    /**
     * 在指定位置打开视频窗口
     */
    private async openVideoWindowAtPosition(videoPath: string, x: number, y: number, screenInfo: string, enableSound: boolean = false) {
        console.log(`在位置 (${x}, ${y}) 打开视频窗口 - ${screenInfo} - ${enableSound ? '有声音' : '静音'}`);

        await this.openVideoInNewWindow(videoPath, x, y, screenInfo, enableSound);
    }

    /**
     * 在新窗口中打开视频
     */
    private async openVideoInNewWindow(videoPath: string, x?: number, y?: number, screenInfo?: string, enableSound: boolean = false) {
        console.log('在新窗口中打开视频:', videoPath);

        // 从配置文件获取窗口配置
        const configManager = VideoPlayerConfigManager.getInstance();
        const windowConfig = await configManager.getVideoWindowConfig();

        // 使用配置文件中的默认位置（如果没有指定）
        const posX = x !== undefined ? x : screen.width - windowConfig.defaultPosition.offsetFromRight;
        const posY = y !== undefined ? y : windowConfig.defaultPosition.offsetFromTop;

        console.log(`视频窗口将在位置 (${posX}, ${posY}) 打开`);
        console.log(`使用配置的窗口尺寸: ${windowConfig.size.width}x${windowConfig.size.height}`);
        if (screenInfo) {
            console.log('屏幕信息:', screenInfo);
        }

        // 创建纯视频窗口的HTML内容（无边框、无控件）
        const videoWindowContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>教学视频</title>
                <style>
                    * {
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }
                    html, body {
                        width: 100%;
                        height: 100%;
                        overflow: hidden;
                        background: #000;
                    }
                    video {
                        width: 100%;
                        height: 100%;
                        object-fit: contain;
                        display: block;
                    }
                    /* 隐藏视频控件，但保留功能 */
                    video::-webkit-media-controls {
                        display: none !important;
                    }
                    video::-webkit-media-controls-enclosure {
                        display: none !important;
                    }
                    /* 右键菜单样式 */
                    .context-menu {
                        position: fixed;
                        background: rgba(0, 0, 0, 0.9);
                        border: 1px solid #333;
                        border-radius: 4px;
                        padding: 5px 0;
                        z-index: 1000;
                        display: none;
                        min-width: 120px;
                    }
                    .context-menu-item {
                        padding: 8px 15px;
                        color: white;
                        cursor: pointer;
                        font-size: 14px;
                        font-family: Arial, sans-serif;
                    }
                    .context-menu-item:hover {
                        background: rgba(255, 255, 255, 0.1);
                    }
                    /* 点击播放覆盖层 */
                    .play-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.8);
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        z-index: 2000;
                        cursor: pointer;
                    }
                    .play-button {
                        width: 80px;
                        height: 80px;
                        border-radius: 50%;
                        background: rgba(255, 255, 255, 0.9);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-size: 30px;
                        color: #333;
                        margin-bottom: 20px;
                        transition: all 0.3s ease;
                    }
                    .play-button:hover {
                        background: white;
                        transform: scale(1.1);
                    }
                    .play-text {
                        color: white;
                        font-size: 16px;
                        text-align: center;
                        font-family: Arial, sans-serif;
                    }
                </style>
            </head>
            <body>
                <video id="instructional-video"
                       autoplay
                       playsinline
                       webkit-playsinline
                       ${enableSound ? '' : 'muted'}
                       preload="auto"
                       style="width: 100%; height: 100%; object-fit: contain;">
                    <source src="${videoPath}" type="video/mp4">
                    您的浏览器不支持视频播放。
                </video>



                <!-- 强力自动播放 -->
                <script>
                    // 全局变量
                    let videoStarted = false;
                    let attemptCount = 0;
                    const maxAttempts = 50;
                    const video = document.getElementById('instructional-video');

                    console.log('强力自动播放初始化');

                    // 方法1: 立即尝试播放
                    function forcePlay() {
                        if (videoStarted || attemptCount >= maxAttempts) return;

                        attemptCount++;
                        console.log('尝试播放 #' + attemptCount);

                        // 设置为有声音播放
                        video.muted = false;
                        video.volume = 1.0;

                        const playPromise = video.play();
                        if (playPromise !== undefined) {
                            playPromise.then(function() {
                                console.log('🎉 强力播放成功！');
                                videoStarted = true;
                            }).catch(function(error) {
                                console.log('播放失败 #' + attemptCount + ':', error.name);

                                // 如果是权限问题，尝试静音播放然后取消静音
                                if (error.name === 'NotAllowedError') {
                                    video.muted = true;
                                    video.play().then(function() {
                                        console.log('静音播放成功，尝试取消静音');
                                        setTimeout(function() {
                                            video.muted = false;
                                            console.log('已取消静音');
                                        }, 100);
                                    }).catch(function() {
                                        // 继续尝试
                                        setTimeout(forcePlay, 100);
                                    });
                                } else {
                                    // 其他错误，继续尝试
                                    setTimeout(forcePlay, 100);
                                }
                            });
                        }
                    }

                    // 方法2: 监听各种事件来触发播放
                    const events = [
                        'DOMContentLoaded', 'load', 'loadstart', 'loadeddata',
                        'loadedmetadata', 'canplay', 'canplaythrough',
                        'click', 'touchstart', 'touchend', 'mousedown', 'mouseup',
                        'keydown', 'keyup', 'focus', 'blur', 'resize', 'scroll'
                    ];

                    events.forEach(function(eventName) {
                        document.addEventListener(eventName, function() {
                            if (!videoStarted) {
                                forcePlay();
                            }
                        }, { once: true, passive: true });

                        window.addEventListener(eventName, function() {
                            if (!videoStarted) {
                                forcePlay();
                            }
                        }, { once: true, passive: true });
                    });

                    // 方法3: 定时器持续尝试
                    const playInterval = setInterval(function() {
                        if (!videoStarted && attemptCount < maxAttempts) {
                            forcePlay();
                        } else {
                            clearInterval(playInterval);
                        }
                    }, 200);

                    // 方法4: 页面可见性变化时尝试
                    document.addEventListener('visibilitychange', function() {
                        if (!document.hidden && !videoStarted) {
                            forcePlay();
                        }
                    });

                    // 方法5: 窗口焦点变化时尝试
                    window.addEventListener('focus', function() {
                        if (!videoStarted) {
                            forcePlay();
                        }
                    });

                    // 方法6: 立即开始尝试
                    setTimeout(forcePlay, 10);
                    setTimeout(forcePlay, 100);
                    setTimeout(forcePlay, 500);
                    setTimeout(forcePlay, 1000);



                    // 视频播放完毕自动关闭窗口
                    video.addEventListener('ended', function() {
                        console.log('视频播放完毕，3秒后自动关闭窗口');
                        setTimeout(function() {
                            window.close();
                        }, 3000);
                    });

                    // 视频加载错误处理
                    video.addEventListener('error', function(e) {
                        console.error('视频加载失败:', e);
                        setTimeout(function() {
                            window.close();
                        }, 2000);
                    });

                    // 视频开始播放时的处理
                    video.addEventListener('play', function() {
                        console.log('视频开始播放');
                        videoStarted = true;
                    });

                    console.log('强力自动播放器初始化完成');
                </script>

                <!-- 点击播放覆盖层 -->
                <div id="play-overlay" class="play-overlay" style="display: none;">
                    <div class="play-button">🔊</div>
                    <div class="play-text" id="play-text">点击启用声音<br>（视频正在静音播放）</div>
                </div>

                <!-- 右键菜单 -->
                <div id="context-menu" class="context-menu">
                    <div class="context-menu-item" onclick="togglePlayPause()">⏯️ 播放/暂停</div>
                    <div class="context-menu-item" onclick="restartVideo()">🔄 重播</div>
                    <div class="context-menu-item" onclick="toggleMute()">🔊 静音/取消静音</div>
                    <div class="context-menu-item" onclick="closeWindow()">❌ 关闭窗口</div>
                </div>

                <script>
                    const video = document.getElementById('instructional-video');
                    const contextMenu = document.getElementById('context-menu');
                    const playOverlay = document.getElementById('play-overlay');
                    let isVideoEnded = false;
                    let userHasInteracted = false; // 初始为false
                    let audioPermissionGranted = false;
                    const enableSoundMode = ${enableSound ? 'true' : 'false'};

                    // 监听模拟的用户交互
                    window.addEventListener('message', function(event) {
                        if (event.data && event.data.type === 'USER_INTERACTION_SIMULATED') {
                            console.log('收到模拟用户交互信号');
                            audioPermissionGranted = true;
                            userHasInteracted = true;

                            // 立即尝试播放有声音视频
                            video.muted = false;
                            video.volume = 1.0;

                            const playPromise = video.play();
                            if (playPromise !== undefined) {
                                playPromise.then(() => {
                                    console.log('🎉 模拟交互后视频有声音播放成功！');
                                }).catch(e => {
                                    console.log('模拟交互后播放仍然失败:', e);
                                    // 如果还是失败，尝试静音播放
                                    video.muted = true;
                                    video.play().then(() => {
                                        console.log('回退到静音播放');
                                    });
                                });
                            }
                        }
                    });

                    // 视频加载完成后的处理
                    video.addEventListener('loadedmetadata', function() {
                        console.log('视频元数据加载完成');

                        const videoWidth = video.videoWidth;
                        const videoHeight = video.videoHeight;
                        console.log('视频原始尺寸:', videoWidth + 'x' + videoHeight);

                        // 注释掉自动调整窗口大小的代码，使用配置文件中的尺寸
                        // 如果需要根据视频尺寸调整，可以取消注释下面的代码
                        /*
                        // 计算合适的显示尺寸（保持宽高比）
                        const maxWidth = ${windowConfig.size.width};
                        const maxHeight = ${windowConfig.size.height};

                        let displayWidth = videoWidth;
                        let displayHeight = videoHeight;

                        // 如果视频太大，按比例缩放
                        if (displayWidth > maxWidth) {
                            displayHeight = (displayHeight * maxWidth) / displayWidth;
                            displayWidth = maxWidth;
                        }

                        if (displayHeight > maxHeight) {
                            displayWidth = (displayWidth * maxHeight) / displayHeight;
                            displayHeight = maxHeight;
                        }

                        // 调整窗口大小为视频尺寸
                        window.resizeTo(displayWidth, displayHeight);
                        console.log('窗口调整为:', displayWidth + 'x' + displayHeight);
                        */

                        console.log('使用配置文件中的窗口尺寸: ${windowConfig.size.width}x${windowConfig.size.height}');
                    });

                    // 视频开始播放
                    video.addEventListener('play', function() {
                        console.log('视频开始播放');
                        // 如果用户已经交互过，才尝试取消静音
                        if (userHasInteracted) {
                            setTimeout(() => {
                                video.muted = false;
                                console.log('用户交互后已取消静音');
                            }, 100);
                        }
                    });

                    // 监听暂停事件（可能是因为取消静音失败导致的）
                    video.addEventListener('pause', function() {
                        if (!userHasInteracted && !isVideoEnded) {
                            console.log('视频被暂停（可能是取消静音失败），显示点击提示');
                            showClickToPlayOverlay();
                        }
                    });

                    // 视频加载完成后的处理
                    video.addEventListener('loadeddata', function() {
                        console.log('视频数据加载完成');
                        if (enableSoundMode) {
                            console.log('等待父窗口音频权限...');
                            // 等待一段时间让父窗口处理权限请求
                            setTimeout(() => {
                                if (!audioPermissionGranted) {
                                    console.log('未收到音频权限，尝试静音播放');
                                    attemptAutoPlay(true); // 静音播放
                                }
                            }, 1000);
                        } else {
                            attemptAutoPlay(true); // 静音模式
                        }
                    });

                    // 视频可以播放时的处理
                    video.addEventListener('canplay', function() {
                        if (video.paused && !enableSoundMode) {
                            console.log('视频可以播放，静音模式');
                            attemptAutoPlay(true);
                        }
                    });

                    // 尝试自动播放的函数
                    function attemptAutoPlay(muted = false) {
                        if (muted || !enableSoundMode) {
                            video.muted = true;
                            console.log('尝试静音播放');
                        } else {
                            video.muted = false;
                            video.volume = 1.0;
                            console.log('尝试有声音播放');
                        }

                        const playPromise = video.play();

                        if (playPromise !== undefined) {
                            playPromise.then(() => {
                                if (muted) {
                                    console.log('静音播放成功');
                                } else {
                                    console.log('🎉 有声音播放成功！');
                                }
                            }).catch(error => {
                                console.log('播放失败:', error.name, error.message);

                                // 如果有声音播放失败，回退到静音
                                if (!muted && error.name === 'NotAllowedError') {
                                    console.log('回退到静音播放');
                                    attemptAutoPlay(true);
                                }
                            });
                        }
                    }

                    // 视频播放结束自动关闭窗口
                    video.addEventListener('ended', function() {
                        console.log('视频播放完毕，3秒后自动关闭窗口');
                        isVideoEnded = true;

                        // 3秒后自动关闭
                        setTimeout(function() {
                            window.close();
                        }, 3000);
                    });

                    // 视频加载错误处理
                    video.addEventListener('error', function(e) {
                        console.error('视频加载失败:', e);
                        alert('视频加载失败，窗口将关闭');
                        setTimeout(() => window.close(), 2000);
                    });

                    // 右键菜单功能
                    video.addEventListener('contextmenu', function(e) {
                        e.preventDefault();
                        contextMenu.style.display = 'block';
                        contextMenu.style.left = e.clientX + 'px';
                        contextMenu.style.top = e.clientY + 'px';
                    });

                    // 点击其他地方隐藏菜单
                    document.addEventListener('click', function() {
                        contextMenu.style.display = 'none';
                    });

                    // 双击全屏/退出全屏
                    video.addEventListener('dblclick', function() {
                        if (document.fullscreenElement) {
                            document.exitFullscreen();
                        } else {
                            video.requestFullscreen();
                        }
                    });

                    // 键盘快捷键
                    document.addEventListener('keydown', function(e) {
                        switch(e.code) {
                            case 'Space':
                                e.preventDefault();
                                togglePlayPause();
                                break;
                            case 'KeyR':
                                restartVideo();
                                break;
                            case 'KeyM':
                                toggleMute();
                                break;
                            case 'Escape':
                                closeWindow();
                                break;
                        }
                    });

                    // 功能函数
                    function togglePlayPause() {
                        if (video.paused) {
                            video.play();
                        } else {
                            video.pause();
                        }
                    }

                    function restartVideo() {
                        video.currentTime = 0;
                        video.play();
                        isVideoEnded = false;
                    }

                    function toggleMute() {
                        video.muted = !video.muted;
                    }

                    function closeWindow() {
                        window.close();
                    }

                    // 窗口获得焦点时尝试播放
                    window.addEventListener('focus', function() {
                        console.log('窗口获得焦点，尝试有声音播放');
                        if (enableSoundMode && video.paused) {
                            userHasInteracted = true;
                            video.muted = false;
                            video.play().then(() => {
                                console.log('🎉 窗口焦点后有声音播放成功！');
                            }).catch(e => {
                                console.log('窗口焦点后播放失败:', e);
                            });
                        }
                    });

                    // 页面点击时尝试播放
                    document.addEventListener('click', function() {
                        console.log('检测到页面点击，尝试有声音播放');
                        if (enableSoundMode && (video.paused || video.muted)) {
                            userHasInteracted = true;
                            video.muted = false;
                            if (video.paused) {
                                video.play().then(() => {
                                    console.log('🎉 点击后有声音播放成功！');
                                }).catch(e => {
                                    console.log('点击后播放失败:', e);
                                });
                            } else {
                                console.log('视频正在播放，已取消静音');
                            }
                        }
                    });

                    // 显示点击播放覆盖层
                    function showClickToPlayOverlay() {
                        console.log('显示点击启用声音覆盖层');
                        const playText = document.getElementById('play-text');
                        const playButton = playOverlay.querySelector('.play-button');

                        if (video.paused) {
                            // 视频暂停了，需要重新播放
                            playButton.textContent = '▶';
                            playText.innerHTML = '点击播放教学视频<br>（需要用户交互）';
                        } else {
                            // 视频在播放但静音
                            playButton.textContent = '🔊';
                            playText.innerHTML = '点击启用声音<br>（视频正在静音播放）';
                        }

                        playOverlay.style.display = 'flex';
                    }

                    // 隐藏覆盖层并启用声音/播放视频
                    function hideOverlayAndPlay() {
                        playOverlay.style.display = 'none';
                        userHasInteracted = true;

                        if (video.paused) {
                            // 如果视频暂停了，重新播放
                            const playPromise = video.play();
                            if (playPromise !== undefined) {
                                playPromise.then(() => {
                                    console.log('用户交互后重新播放成功');
                                    setTimeout(() => {
                                        video.muted = false;
                                        console.log('已启用声音');
                                    }, 200);
                                }).catch(error => {
                                    console.error('重新播放失败:', error);
                                });
                            }
                        } else {
                            // 视频正在播放，只需要启用声音
                            video.muted = false;
                            console.log('已启用声音');
                        }
                    }

                    // 覆盖层点击事件
                    playOverlay.addEventListener('click', hideOverlayAndPlay);

                    // 页面加载完成后立即尝试播放
                    window.addEventListener('load', function() {
                        console.log('页面加载完成，立即尝试播放');

                        // 多次尝试播放，增加成功率
                        setTimeout(() => attemptAutoPlay(), 100);
                        setTimeout(() => attemptAutoPlay(), 500);
                        setTimeout(() => attemptAutoPlay(), 1000);

                        // 如果1.5秒后还是暂停，再试一次
                        setTimeout(() => {
                            if (video.paused) {
                                console.log('视频仍然暂停，最后一次尝试');
                                attemptAutoPlay();
                            }
                        }, 1500);
                    });

                    console.log('纯视频播放器初始化完成');
                </script>
            </body>
            </html>
        `;

        // 使用配置文件中的窗口特性字符串，但替换位置参数
        const windowFeatures = `width=${windowConfig.size.width},height=${windowConfig.size.height},left=${posX},top=${posY},scrollbars=no,resizable=yes,toolbar=no,menubar=no,location=no,status=no,titlebar=no,directories=no,fullscreen=no,channelmode=no`;
        const videoWindow = window.open('', 'instructionalVideo', windowFeatures);

        if (videoWindow) {
            videoWindow.document.write(videoWindowContent);
            videoWindow.document.close();

            // 确保窗口在正确位置（某些浏览器可能忽略初始位置参数）
            setTimeout(() => {
                try {
                    videoWindow.moveTo(posX, posY);
                    console.log(`视频窗口已移动到位置 (${posX}, ${posY})`);
                } catch (e) {
                    console.log('无法移动窗口位置:', e);
                }
            }, 100);

            console.log('教学视频新窗口已打开');

            // 在聊天界面显示提示
            let chatOverlay = document.getElementById('chatOverlay');
            if (chatOverlay) {
                const message = screenInfo ?
                    `📹 教学视频已在新窗口中打开 (${screenInfo})` :
                    '📹 教学视频已在新窗口中打开';
                chatOverlay.innerHTML += `<span class="chatSpan" style="color: #0066cc; font-style: italic;">${message}</span>`;
                chatOverlay.scrollTop = chatOverlay.scrollHeight;
            }
        } else {
            console.warn('无法打开新窗口，可能被浏览器阻止');
            // 回退到当前页面播放
            await this.showVideoInCurrentPage(videoPath);
        }
    }

    /**
     * 在当前页面显示视频
     */
    private async showVideoInCurrentPage(videoPath: string) {
        console.log('在当前页面显示视频:', videoPath);

        // 检查页面中的视频元素
        const embeddedVideo = document.getElementById('embedded-video') as HTMLVideoElement;
        const embeddedContainer = document.getElementById('embedded-video-container');

        console.log('embedded-video元素:', embeddedVideo);
        console.log('embedded-video-container元素:', embeddedContainer);

        if (embeddedVideo && embeddedContainer) {
            // 显示视频容器
            embeddedContainer.classList.remove('hidden');
            embeddedContainer.style.display = 'block';

            // 设置新的视频源
            embeddedVideo.src = videoPath;
            embeddedVideo.load(); // 重新加载视频
            embeddedVideo.play().catch(e => console.log('视频自动播放失败:', e));

            console.log('教学视频已设置:', videoPath);

            // 在聊天界面显示视频提示
            let chatOverlay = document.getElementById('chatOverlay');
            if (chatOverlay) {
                chatOverlay.innerHTML += '<span class="chatSpan" style="color: #0066cc; font-style: italic;">📹 已为您显示相关教学视频（右上角）</span>';
                chatOverlay.scrollTop = chatOverlay.scrollHeight;
            }
        } else {
            console.warn('嵌入视频元素未找到，尝试动态创建');
            await this.createVideoPlayerDynamically(videoPath);
        }
    }

    /**
     * 动态创建视频播放器
     */
    private async createVideoPlayerDynamically(videoPath: string) {
        console.log('动态创建视频播放器');

        // 从配置文件获取样式
        const configManager = VideoPlayerConfigManager.getInstance();
        const containerStyle = await configManager.getEmbeddedContainerStyle();

        // 创建视频容器
        const videoContainer = document.createElement('div');
        videoContainer.id = 'embedded-video-container';
        videoContainer.style.cssText = containerStyle;

        // 从配置文件获取标题栏和标题样式
        const titleBarStyle = await configManager.getTitleBarStyle();
        const titleStyle = await configManager.getTitleStyle();

        // 创建标题栏
        const titleBar = document.createElement('div');
        titleBar.style.cssText = titleBarStyle;

        const title = document.createElement('span');
        title.textContent = '📹 教学视频';
        title.style.cssText = titleStyle;

        const closeBtn = document.createElement('button');
        closeBtn.id = 'close-video-btn';
        closeBtn.innerHTML = '&times;';
        closeBtn.style.cssText = `
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
        `;

        titleBar.appendChild(title);
        titleBar.appendChild(closeBtn);

        // 创建视频元素
        const video = document.createElement('video');
        video.id = 'embedded-video';
        video.controls = true;
        video.style.cssText = 'width: 100%; height: 200px; border-radius: 4px;';
        video.src = videoPath;

        // 组装元素
        videoContainer.appendChild(titleBar);
        videoContainer.appendChild(video);

        // 添加到页面
        document.body.appendChild(videoContainer);

        // 绑定关闭事件
        closeBtn.onclick = () => {
            video.pause();
            video.currentTime = 0;
            videoContainer.remove();
            console.log('动态创建的视频播放器已关闭');
        };

        // 播放视频
        video.load();
        video.play().catch(e => console.log('视频自动播放失败:', e));

        console.log('动态视频播放器创建成功');

        // 在聊天界面显示视频提示
        let chatOverlay = document.getElementById('chatOverlay');
        if (chatOverlay) {
            chatOverlay.innerHTML += '<span class="chatSpan" style="color: #0066cc; font-style: italic;">📹 已为您显示相关教学视频（右上角）</span>';
            chatOverlay.scrollTop = chatOverlay.scrollHeight;
        }
    }

    /**
     * 直接调用通义千问API
     */
    async callTongyiQianwenDirectly(prompt: string, voiceId: string, voiceSpeed: string) {
        console.log('直接调用通义千问API:', prompt);

        // 检查是否需要显示相关教学视频
        const matchingVideo = this.findMatchingVideo(prompt);
        if (matchingVideo) {
            // 如果匹配到视频，跳过AI对话，直接播放视频
            console.log('检测到视频关键词，跳过AI对话，直接播放视频');
            this.playVideoDirectly(matchingVideo, voiceId, voiceSpeed);
            return; // 直接返回，不进行AI对话
        }

        const apiKey = "sk-e01487fd05124b01b5d0fe1be1da9671";
        const endpoint = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";

        try {
            // 显示加载状态
            let chatOverlay = document.getElementById('chatOverlay');
            if (chatOverlay) {
                chatOverlay.innerHTML += '<span class="chatSpan" id="loading-message"><img src="/images/mic-anim.gif" style="height:1em;" />&nbsp;通义千问正在思考...</span>';
                chatOverlay.scrollTop = chatOverlay.scrollHeight;
            }

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                    model: "qwen-max",
                    messages: [
                        {
                            role: "user",
                            content: prompt
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 1000
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            const aiResponse = data.choices[0].message.content;

            console.log('通义千问回复:', aiResponse);

            // 检查AI回复中是否包含相关关键词，如果包含也显示教学视频
            const matchingVideoInResponse = this.findMatchingVideo(aiResponse);
            if (matchingVideoInResponse && !matchingVideo) { // 如果用户问题中没有匹配但回复中有匹配
                this.showInstructionalVideo(matchingVideoInResponse);
            }

            // 显示AI回复
            if (chatOverlay) {
                // 移除加载状态
                const loadingMessage = document.getElementById('loading-message');
                if (loadingMessage) {
                    loadingMessage.remove();
                }

                // 添加AI回复
                chatOverlay.innerHTML += '<span class="chatSpan"><img src="/images/answer.png" style="height:1em;" />&nbsp;' + aiResponse + '</span>';
                chatOverlay.scrollTop = chatOverlay.scrollHeight;
            }

            // 🆕 将AI回复显示到 Caption（现有逻辑不动，TTS会自动处理）
            // 这里不需要额外调用，因为TTS synthesis会自动显示到Caption

            // 调用TTS播放回复 - 修复通义千问语音播放问题
            // 使用与其他模型相同的分步发送方式
            this.emitUIInteraction({
                TextToSpeech: {
                    voiceConfig: { voiceId: voiceId, voiceSpeed: voiceSpeed }
                }
            });

            // 延迟发送synthesis请求，确保voiceConfig先被处理
            setTimeout(() => {
                this.emitUIInteraction({
                    TextToSpeech: {
                        synthesis: { text: aiResponse }
                    }
                });
            }, 100);

            // 清空输入框
            let textArea = document.getElementById('tts-chat-area') as HTMLTextAreaElement;
            if (textArea) {
                textArea.value = '';
            }

        } catch (error) {
            console.error('通义千问API调用失败:', error);

            // 显示错误信息
            let errorChatOverlay = document.getElementById('chatOverlay');
            if (errorChatOverlay) {
                // 移除加载状态
                const loadingMessage = document.getElementById('loading-message');
                if (loadingMessage) {
                    loadingMessage.remove();
                }

                errorChatOverlay.innerHTML += '<span class="chatSpan" style="color: red;">❌&nbsp;通义千问调用失败: ' + error.message + '</span>';
                errorChatOverlay.scrollTop = errorChatOverlay.scrollHeight;
            }
        }
    }

    /**
     * 发送文本到 Caption 显示（如果 Caption 开启的话）
     * @param text 要显示的文本
     * @param time 显示时间（秒），可选，默认为 3 秒
     */
    private sendTextToCaption(text: string, time: number = 3) {
        // 检查 Caption 是否开启
        let captionSwitch = document.getElementById('caption-switch') as HTMLInputElement;
        if (captionSwitch && captionSwitch.checked && text.trim().length > 0) {
            console.log('📺 发送文本到 Caption:', text);
            this.emitUIInteraction({
                Caption: {
                    custom: {
                        text: text.trim(),
                        time: time.toString()
                    }
                }
            });
        }
    }
}