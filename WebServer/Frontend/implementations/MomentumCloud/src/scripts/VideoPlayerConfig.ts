/**
 * 视频播放器配置管理类
 * 负责从配置文件加载视频播放器的样式和行为配置
 */

export interface VideoPlayerStyleConfig {
    position: string;
    top: string;
    right: string;
    zIndex: number;
    width: string;
    height: string;
    background: string;
    borderRadius: string;
    padding?: string;  // 改为可选属性
    boxShadow: string;
    overflow?: string;
    border?: string;
}

export interface VideoStyleConfig {
    width: string;
    height: string;
    objectFit: string;
}

export interface ButtonStyleConfig {
    position: string;
    top: string;
    right: string;
    width: string;
    height: string;
    background: string;
    color: string;
    border: string;
    borderRadius: string;
    cursor: string;
    fontSize: string;
    zIndex: number;
}

export interface TitleBarStyleConfig {
    display: string;
    justifyContent: string;
    alignItems: string;
    marginBottom: string;
}

export interface TitleStyleConfig {
    color: string;
    fontSize: string;
    fontWeight: string;
    margin: string;
}

export interface VideoWindowConfig {
    defaultPosition: {
        offsetFromRight: number;
        offsetFromTop: number;
    };
    size: {
        width: number;
        height: number;
    };
    features: string;
}

export interface HoloDisplayConfig {
    backgroundDistance: number;
    cameraLocation: {
        x: number;
        y: number;
        z: number;
    };
    cameraRotation: {
        x: number;
        y: number;
        z: number;
    };
    cameraSize: number;
    fov: number;
}

export interface CharacterConfig {
    defaultSettings: {
        location: {
            x: number;
            y: number;
            z: number;
        };
        rotation: {
            x: number;
            y: number;
            z: number;
        };
        scale: {
            x: number;
            y: number;
            z: number;
        };
    };
}

export interface VideoPlayerConfig {
    videoPlayer: {
        embeddedContainer: VideoPlayerStyleConfig;
        floatingContainer: VideoPlayerStyleConfig;
        video: VideoStyleConfig;
        closeButton: ButtonStyleConfig;
        titleBar: TitleBarStyleConfig;
        title: TitleStyleConfig;
    };
    videoWindow: VideoWindowConfig;
    holoDisplay: HoloDisplayConfig;
    character: CharacterConfig;
    paths: {
        configRelativePath: string;
    };
}

export class VideoPlayerConfigManager {
    private static instance: VideoPlayerConfigManager;
    private config: VideoPlayerConfig | null = null;
    private configPath: string = '../../Binaries/Win64/video-player-config.json';

    private constructor() {}

    public static getInstance(): VideoPlayerConfigManager {
        if (!VideoPlayerConfigManager.instance) {
            VideoPlayerConfigManager.instance = new VideoPlayerConfigManager();
        }
        return VideoPlayerConfigManager.instance;
    }

    /**
     * 加载配置文件
     */
    public async loadConfig(): Promise<VideoPlayerConfig> {
        if (this.config) {
            return this.config;
        }

        try {
            console.log('正在加载视频播放器配置文件:', this.configPath);
            
            const response = await fetch(this.configPath);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            this.config = await response.json();
            console.log('视频播放器配置加载成功:', this.config);
            
            return this.config;
        } catch (error) {
            console.warn('加载视频播放器配置失败，使用默认配置:', error);
            return this.getDefaultConfig();
        }
    }

    /**
     * 获取默认配置
     */
    private getDefaultConfig(): VideoPlayerConfig {
        return {
            videoPlayer: {
                embeddedContainer: {
                    position: 'fixed',
                    top: '20px',
                    right: '20px',
                    zIndex: 1000,
                    width: '320px',
                    height: '240px',
                    background: 'rgba(0,0,0,0.8)',
                    borderRadius: '8px',
                    padding: '10px',
                    boxShadow: '0 4px 8px rgba(0,0,0,0.3)'
                },
                floatingContainer: {
                    position: 'fixed',
                    top: '20px',
                    right: '20px',
                    width: '400px',
                    height: '300px',
                    zIndex: 10000,
                    background: '#000',
                    borderRadius: '8px',
                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5)',
                    overflow: 'hidden',
                    border: '2px solid #333'
                },
                video: {
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain'
                },
                closeButton: {
                    position: 'absolute',
                    top: '5px',
                    right: '5px',
                    width: '30px',
                    height: '30px',
                    background: 'rgba(0, 0, 0, 0.7)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    cursor: 'pointer',
                    fontSize: '18px',
                    zIndex: 10001
                },
                titleBar: {
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '8px'
                },
                title: {
                    color: 'white',
                    fontSize: '12px',
                    fontWeight: 'bold',
                    margin: '0'
                }
            },
            videoWindow: {
                defaultPosition: {
                    offsetFromRight: 820,
                    offsetFromTop: 50
                },
                size: {
                    width: 800,
                    height: 600
                },
                features: 'width=800,height=600,scrollbars=no,resizable=yes,status=no,location=no,toolbar=no,menubar=no'
            },
            holoDisplay: {
                backgroundDistance: 4.5,
                cameraLocation: {
                    x: 35,
                    y: 0,
                    z: 155
                },
                cameraRotation: {
                    x: 1,
                    y: -90,
                    z: 0
                },
                cameraSize: 60.0,
                fov: 14.0
            },
            character: {
                defaultSettings: {
                    location: {
                        x: 0,
                        y: 0,
                        z: 0
                    },
                    rotation: {
                        x: 0,
                        y: 0,
                        z: 0
                    },
                    scale: {
                        x: 1.0,
                        y: 1.0,
                        z: 1.0
                    }
                }
            },
            paths: {
                configRelativePath: '../../Binaries/Win64/video-player-config.json'
            }
        };
    }

    /**
     * 将样式配置对象转换为CSS字符串
     */
    public styleObjectToCssText(styleObj: any): string {
        return Object.entries(styleObj)
            .map(([key, value]) => {
                // 将驼峰命名转换为CSS属性名
                const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
                return `${cssKey}: ${value}`;
            })
            .join('; ') + ';';
    }

    /**
     * 获取嵌入式容器样式
     */
    public async getEmbeddedContainerStyle(): Promise<string> {
        const config = await this.loadConfig();
        return this.styleObjectToCssText(config.videoPlayer.embeddedContainer);
    }

    /**
     * 获取浮动容器样式
     */
    public async getFloatingContainerStyle(): Promise<string> {
        const config = await this.loadConfig();
        return this.styleObjectToCssText(config.videoPlayer.floatingContainer);
    }

    /**
     * 获取视频样式
     */
    public async getVideoStyle(): Promise<string> {
        const config = await this.loadConfig();
        return this.styleObjectToCssText(config.videoPlayer.video);
    }

    /**
     * 获取关闭按钮样式
     */
    public async getCloseButtonStyle(): Promise<string> {
        const config = await this.loadConfig();
        return this.styleObjectToCssText(config.videoPlayer.closeButton);
    }

    /**
     * 获取标题栏样式
     */
    public async getTitleBarStyle(): Promise<string> {
        const config = await this.loadConfig();
        return this.styleObjectToCssText(config.videoPlayer.titleBar);
    }

    /**
     * 获取标题样式
     */
    public async getTitleStyle(): Promise<string> {
        const config = await this.loadConfig();
        return this.styleObjectToCssText(config.videoPlayer.title);
    }

    /**
     * 获取视频窗口配置
     */
    public async getVideoWindowConfig(): Promise<VideoWindowConfig> {
        const config = await this.loadConfig();
        return config.videoWindow;
    }

    /**
     * 获取HoloDisplay配置
     */
    public async getHoloDisplayConfig(): Promise<HoloDisplayConfig> {
        const config = await this.loadConfig();
        return config.holoDisplay;
    }

    /**
     * 获取Character配置
     */
    public async getCharacterConfig(): Promise<CharacterConfig> {
        const config = await this.loadConfig();
        return config.character;
    }

    /**
     * 重新加载配置
     */
    public async reloadConfig(): Promise<VideoPlayerConfig> {
        this.config = null;
        return await this.loadConfig();
    }
}
