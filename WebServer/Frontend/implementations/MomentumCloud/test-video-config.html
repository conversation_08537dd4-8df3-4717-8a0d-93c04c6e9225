<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频播放器配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .config-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 视频播放器配置测试</h1>
        <p>这个页面用于测试视频播放器配置文件的加载和应用。</p>
        
        <div>
            <button class="button" onclick="loadConfig()">📁 加载配置文件</button>
            <button class="button" onclick="testEmbeddedPlayer()">🎥 测试嵌入式播放器</button>
            <button class="button" onclick="testFloatingPlayer()">🎭 测试浮动播放器</button>
            <button class="button" onclick="reloadConfig()">🔄 重新加载配置</button>
        </div>

        <div id="status"></div>
        <div id="configDisplay" class="config-display"></div>
    </div>

    <script type="module">
        import { VideoPlayerConfigManager } from './src/scripts/VideoPlayerConfig.js';

        let configManager;
        
        window.loadConfig = async function() {
            try {
                showStatus('正在加载配置文件...', 'info');
                configManager = VideoPlayerConfigManager.getInstance();
                const config = await configManager.loadConfig();
                
                document.getElementById('configDisplay').textContent = JSON.stringify(config, null, 2);
                showStatus('✅ 配置文件加载成功！', 'success');
            } catch (error) {
                showStatus(`❌ 配置文件加载失败: ${error.message}`, 'error');
                console.error('配置加载错误:', error);
            }
        };

        window.testEmbeddedPlayer = async function() {
            if (!configManager) {
                showStatus('⚠️ 请先加载配置文件', 'error');
                return;
            }

            try {
                showStatus('正在创建嵌入式视频播放器...', 'info');
                
                // 移除已存在的播放器
                const existing = document.getElementById('test-embedded-player');
                if (existing) existing.remove();

                // 获取样式配置
                const containerStyle = await configManager.getEmbeddedContainerStyle();
                const videoStyle = await configManager.getVideoStyle();
                const titleBarStyle = await configManager.getTitleBarStyle();
                const titleStyle = await configManager.getTitleStyle();

                // 创建容器
                const container = document.createElement('div');
                container.id = 'test-embedded-player';
                container.style.cssText = containerStyle;

                // 创建标题栏
                const titleBar = document.createElement('div');
                titleBar.style.cssText = titleBarStyle;

                const title = document.createElement('span');
                title.textContent = '🧪 测试视频播放器';
                title.style.cssText = titleStyle;

                const closeBtn = document.createElement('button');
                closeBtn.innerHTML = '×';
                closeBtn.style.cssText = `
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 0;
                    width: 24px;
                    height: 24px;
                `;
                closeBtn.onclick = () => container.remove();

                titleBar.appendChild(title);
                titleBar.appendChild(closeBtn);

                // 创建视频占位符
                const videoPlaceholder = document.createElement('div');
                videoPlaceholder.style.cssText = videoStyle + ' background: #333; display: flex; align-items: center; justify-content: center; color: white; font-size: 14px;';
                videoPlaceholder.textContent = '📹 视频播放区域 (测试模式)';

                container.appendChild(titleBar);
                container.appendChild(videoPlaceholder);
                document.body.appendChild(container);

                showStatus('✅ 嵌入式播放器创建成功！', 'success');
            } catch (error) {
                showStatus(`❌ 创建嵌入式播放器失败: ${error.message}`, 'error');
                console.error('创建播放器错误:', error);
            }
        };

        window.testFloatingPlayer = async function() {
            if (!configManager) {
                showStatus('⚠️ 请先加载配置文件', 'error');
                return;
            }

            try {
                showStatus('正在创建浮动视频播放器...', 'info');
                
                // 移除已存在的播放器
                const existing = document.getElementById('test-floating-player');
                if (existing) existing.remove();

                // 获取样式配置
                const containerStyle = await configManager.getFloatingContainerStyle();
                const videoStyle = await configManager.getVideoStyle();
                const closeButtonStyle = await configManager.getCloseButtonStyle();

                // 创建容器
                const container = document.createElement('div');
                container.id = 'test-floating-player';
                container.style.cssText = containerStyle;

                // 创建视频占位符
                const videoPlaceholder = document.createElement('div');
                videoPlaceholder.style.cssText = videoStyle + ' background: #333; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;';
                videoPlaceholder.textContent = '🎬 浮动视频播放器 (测试模式)';

                // 创建关闭按钮
                const closeBtn = document.createElement('button');
                closeBtn.innerHTML = '×';
                closeBtn.style.cssText = closeButtonStyle;
                closeBtn.onclick = () => container.remove();

                container.appendChild(videoPlaceholder);
                container.appendChild(closeBtn);
                document.body.appendChild(container);

                showStatus('✅ 浮动播放器创建成功！', 'success');
            } catch (error) {
                showStatus(`❌ 创建浮动播放器失败: ${error.message}`, 'error');
                console.error('创建播放器错误:', error);
            }
        };

        window.reloadConfig = async function() {
            if (!configManager) {
                showStatus('⚠️ 请先加载配置文件', 'error');
                return;
            }

            try {
                showStatus('正在重新加载配置文件...', 'info');
                const config = await configManager.reloadConfig();
                document.getElementById('configDisplay').textContent = JSON.stringify(config, null, 2);
                showStatus('✅ 配置文件重新加载成功！', 'success');
            } catch (error) {
                showStatus(`❌ 重新加载配置失败: ${error.message}`, 'error');
                console.error('重新加载错误:', error);
            }
        };

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            
            // 3秒后清除状态
            setTimeout(() => {
                statusDiv.textContent = '';
                statusDiv.className = 'status';
            }, 3000);
        }

        // 页面加载时自动加载配置
        window.addEventListener('load', () => {
            loadConfig();
        });
    </script>
</body>
</html>
