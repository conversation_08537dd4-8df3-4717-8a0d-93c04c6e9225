<!-- Copyright Digital Domain, Inc. All Rights Reserved. -->
<!DOCTYPE HTML>
<html>
<head>
	<title>Momentum Cloud</title>
	<link rel="shortcut icon" href="./images/MomentumCloud-32x32.png" type="image/x-icon">
	<link rel="icon" type="image/png" sizes="96x96" href="./images/MomentumCloud-96x96.png">
	<link rel="icon" type="image/png" sizes="32x32" href="./images/MomentumCloud-32x32.png">
	<link rel="icon" type="image/png" sizes="16x16" href="./images/MomentumCloud-16x16.png">
	<link type="text/css" rel="stylesheet" href="./css/controller.css">
	<script type="text/javascript" src="https://webrtc.github.io/adapter/adapter-latest.js"></script>
	<script type="text/javascript" src="./scripts/webRtcPlayer.js"></script>
	<script type="text/javascript" src="./scripts/data.js"></script>
	<script type="text/javascript" src="./scripts/app.js"></script>
	<link rel="stylesheet" href="./bootstrap/css/bootstrap.min.css" >
	<link rel="stylesheet" href="./bootstrap/icons/font/bootstrap-icons.css">
	<script src="./bootstrap/js/bootstrap.bundle.min.js"></script>
</head>

<body onload="load()">
	<div class="container-fluid">
		<div class="row" style="background-color:black;">
			<div class="col-md-6">
				<div id="playercontainer"><div id="player"></div></div>
				<div id="settings" style="margin-top:5px;">
					<div class="form-group" style="grid-template-columns:70% 30%;">
						<h5 class="text-white">Kick All Other Players</h5>
						<div>
							<input type="button" id="kick-other-players-button" class="btn btn-primary btn-sm float-end" style="width:100px;height:90%;" value="Kick">
						</div>
						<h5 class="text-white">Show FPS</h5>
						<label class="tgl-switch">
							<input type="checkbox" id="show-fps-tgl" class="tgl tgl-flat">
							<div class="tgl-slider"></div>
						</label>
						<h5 class="text-white">Show Stats</h5>
						<label class="tgl-switch">
							<input type="checkbox" id="show-stats-tgl" class="tgl tgl-flat" checked>
							<div class="tgl-slider"></div>
						</label>
					</div>
					<div id="statsContainer">
						<div id="statsResult" class="stats">Not Connected</div>
					</div>

					<h5 class="collapsible text-white">Networking</h5>
					<div class="content">
						<div id="networkingSettings" class="form-group" style="grid-template-columns:70% 30%;">
							<h5 class="text-white">Encoder Min QP</h5>
							<input type="number" class="form-control form-control-sm text-center" id="encoder-min-qp-text" value="0" min="0" max="51"/>
							<h5 class="text-white">Encoder Max QP</h5>
							<input type="number" class="form-control form-control-sm text-center" id="encoder-max-qp-text" value="51" min="0" max="51"/>
							<h5 class="text-white">WebRTC FPS</h5>
							<input type="number" class="form-control form-control-sm text-center" id="webrtc-fps-text" value="60" min="1" max="999"/>
							<h5 class="text-white">WebRTC Min Bitrate (kbps)</h5>
							<input type="number" class="form-control form-control-sm text-center" id="webrtc-min-bitrate-text" value="0" min="0" max="100000"/>
							<h5 class="text-white">WebRTC Max Bitrate (kbps)</h5>
							<input type="number" class="form-control form-control-sm text-center" id="webrtc-max-bitrate-text" value="0" min="0" max="100000"/>
						</div>
						<hr style="border-top:1px solid grey; margin:0.5em 0; opacity:1;"/>
						<div id="latencyTest">
							<input type="button" id="test-latency-button" class="btn btn-primary btn-sm" value="Latency Test"/>
							<div id=LatencyStats class="stats">No stats yet...</div>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-6">
				<div class="row mt-1 mb-1">
					<div class="col-md-6">
						<h3 class="collapsible text-white">Script</h3>
						<div class="content">
							<h5 class="text-white">Demo Script</h5>
							<div class="mb-1">
								<input type="button" id="start-demo-button" class="btn btn-primary btn-sm" value="Start">
								<input type="button" id="stop-demo-button" class="btn btn-primary btn-sm" value="Stop">
							</div>
							<h5 class="text-white">Timecode Player<span style="color: turquoise; font-style: italic; font-size: 0.6em;"> (Experimental)</span></h5>
							<div class="form-group mb-1" style="grid-template-columns:auto min-content min-content; column-gap:5px;">
								<div>
									<input type="file" id="timecode-file" style="display:none" accept=".csv">
									<input type="button" id="timecode-file-button" class="btn btn-primary btn-sm" value="Choose File">
									<label id="timecode-file-text" class="text-white align-bottom h6">No file chosen</label>
								</div>
								<input type="button" id="timecode-play-button" class="btn btn-primary btn-sm" value="Play">
								<input type="button" id="timecode-stop-button" class="btn btn-primary btn-sm" value="Stop">
							</div>
							<h5 class="text-white">Timecode Recorder<span style="color: turquoise; font-style: italic; font-size: 0.6em;"> (Experimental)</span></h5>
							<div class="mb-1">
								<input type="button" id="timecode-toggle-recording-button" class="btn btn-primary btn-sm" value="Start Recording">
							</div>
						</div>

						<h3 class="collapsible collapsible-active text-white">Character</h3>
						<div class="content content-active">
							<h5 class="text-white">Character List
								<input type="button" id="switch-character-button-resetcloth" class="btn btn-primary btn-sm" value="Reset Simulation"/>
							</h5>
							<div class="mb-1">
								<select id="character-select" class="form-select form-select-sm text-start">
									<option value="none" selected>None</option>
								</select>
							</div>

							<h5 class="collapsible text-white">Custom Character<span style="color: turquoise; font-style: italic; font-size: 0.6em;"> (Experimental)</span></h5>
							<div class="content">
								<div class="mb-1">
									<h5 class="text-white">Add Character</h5>
									<div class="mb-1">
										<input type="file" id="vrm-file" style="display:none" accept=".vrm">
										<input type="button" id="vrm-button" class="btn btn-primary btn-sm" value="Choose File">
										<label id="vrm-text" class="text-white h6">No file chosen</label>
									</div>
									<div class="form-group mb-1" style="grid-template-columns:auto min-content; column-gap:5px;">
										<select id="vrm-mat-select" class="form-select form-select-sm text-start">
											<option value="1" selected>MToon</option>
											<option value="0" selected>MToon UnLit</option>
										</select>
										<input type="button" id="vrm-file-upload" class="btn btn-primary btn-sm float-end" value="Upload">
									</div>
									<h5 class="text-white">Delete Character</h5>
									<div class="form-group" style="grid-template-columns:auto min-content; column-gap:5px;">
										<select id="vrm-char-select" class="form-select form-select-sm text-start">
											<option value="none" selected>None</option>
										</select>
										<input type="button" id="vrm-file-delete" class="btn btn-primary btn-sm float-end" value="Delete">
									</div>
								</div>
							</div>

							<h5 class="collapsible text-white">Shadow</h5>
							<div class="content">
								<h5 class="text-white">Enable
									<label class="tgl-switch">
										<input type="checkbox" id="shadow-switch" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h5>
								<div class="form-group" style="grid-template-columns:auto 30%;">
									<h5 class="text-white">Angle (-70~70)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="shadow-angle-text" min="-70" max="70" step="1" value="0">
									<h5 class="text-white">Length (>=0)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="shadow-length-text" min="0" max="9999" step="1" value="0">
									<h5 class="text-white">Opacity (0~1)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="shadow-opacity-text" min="0" max="1" step="0.01" value="0">
									<h5 class="text-white">Offset (0.05~1)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="shadow-offset-text" min="0.05" max="1" step="0.01" value="0">
									<h5 class="text-white">Blur (0~1)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="shadow-blur-text" min="0" max="1" step="0.01" value="0">
								</div>
							</div>
						</div>

						<h3 class="collapsible collapsible-active text-white">Outfit</h3>
						<div class="content content-active">
							<h5 class="text-white">Outfit List</h5>
							<div class="mb-1">
								<div class="form-group mb-1" style="grid-template-columns:auto min-content; column-gap:5px;">
									<select id="outfit-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<input type="button" id="outfit-dock" class="btn btn-primary btn-sm" value="Dock">
								</div>
								<div id="outfit-dock-container"></div>
							</div>

							<h5 class="collapsible text-white">Customized Outfit<span style="color: turquoise; font-style: italic; font-size: 0.6em;"> (Experimental)</span></h5>
							<div class="content">
								<div id="compatibilityCheck-container">
									<h5 class="text-white">Compatibility Check
										<label class="tgl-switch">
											<input type="checkbox" id="compatibilityCheck-switch" class="tgl tgl-flat">
											<div class="tgl-slider"></div>
										</label>
									</h5>
								</div>
								<div class="form-group mb-1" style="grid-template-columns:60% 40%;">
									<h5 class="text-white">Head</h5>
									<select id="custom-outfit-head-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<h5 class="text-white">Hair</h5>
									<select id="custom-outfit-hair-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<h5 class="text-white">Upper</h5>
									<select id="custom-outfit-upper-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<h5 class="text-white">Lower</h5>
									<select id="custom-outfit-lower-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<h5 class="text-white">Feet</h5>
									<select id="custom-outfit-feet-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<h5 class="text-white">Accessories</h5>
									<select id="custom-outfit-acc-select" class="form-select form-select-sm text-start" style="resize:vertical;" multiple>
										<option value="none" selected>None</option>
									</select>
								</div>

								<div class="form-group mb-1" style="grid-template-columns:auto min-content min-content; column-gap:5px;">
									<input type="text" id="custom-outfit-preset-filename-text" class="form-control form-control-sm text-start" placeholder="Enter Preset Name" value="">
									<input type="button" id="custom-outfit-preset-save" class="btn btn-primary btn-sm" value="Save as Preset">
									<input type="button" id="modular-info-copy" class="btn btn-primary btn-sm" value ="Copy">
								</div>

								<h5 class="text-white">Preset List</h5>
								<div class="form-group" style="grid-template-columns:auto min-content; column-gap:5px;">
									<select id="custom-outfit-preset-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<input type="button" id="custom-outfit-preset-apply" class="btn btn-primary btn-sm" value="Apply">
								</div>
							</div>

							<h5 class="text-white">Color Adjustment
								<input type="button" id="avatarColor-reset" class="btn btn-primary btn-sm" value="Reset All">
							</h5>
							<div class="mb-1">
								<div class="form-group" style="grid-template-columns:auto min-content; column-gap:5px;">
									<select id="avatarColor-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<input type="color" id="avatarColor-color" value="#ff0000" value="Dock">
								</div>
							</div>

							<h5 class="collapsible text-white">Custom Logo<span style="color: turquoise; font-style: italic; font-size: 0.6em;"> (Experimental)</span></h5>
							<div class="content">
								<h5 class="text-white">Logo Placement</h5>
								<div class="mb-1">
									<select id="logo-place-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
								</div>
								<h5 class="text-white">Logo List</h5>
								<div class="mb-1">
									<select id="logo-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<div class="mb-1"></div>
									<div class="form-group" style="grid-template-columns:auto min-content; column-gap:5px;">
										<div>
											<input type="file" id="custom-logo-file" style="display:none" accept=".png">
											<input type="button" id="custom-logo-file-button" class="btn btn-primary btn-sm" value="Choose File">
											<label id="custom-logo-file-text" class="text-white align-bottom h6">No file chosen</label>
										</div>
										<input type="button" id="custom-logo-file-upload" class="btn btn-primary btn-sm" value="Upload">
									</div>
								</div>
								<div class="form-group mb-1" style="grid-template-columns:auto 30% 5% 30%;">
									<h5 class="text-white">Offset</h5>
									<input type="number" id="logo-offsetX" class="form-control form-control-sm text-center" value="0" step="0.1">
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="logo-offsetY" class="form-control form-control-sm text-center" value="0" step="0.1">
								</div>
								<div class="form-group" style="grid-template-columns:auto 30%;">
									<h5 class="text-white">Scale</h5>
									<input type="number" id="logo-scale" class="form-control form-control-sm text-center" value="1" step="0.5">
									<h5 class="text-white">Rotation Angle</h5>
									<input type="number" id="logo-rotate" class="form-control form-control-sm text-center" value="0" step="1">
								</div>
							</div>
						</div>

						<h3 class="collapsible collapsible-active text-white">Camera Framing</h3>
						<div class="content content-active">
							<h5 class="text-white">Camera Framing List
								<input type="button" id="camera-info-copy" class="btn btn-primary btn-sm" value ="Copy">
								<span id="copy-feedback" class="h6" style="display:none; color:green;">Copied</span>
							</h5>
							<select id="camera-select" class="form-select form-select-sm text-start">
								<option value="none" selected>None</option>
							</select>

							<div id="joystickDiv" style="display:none; justify-content:center; flex-wrap:wrap;">
								<div class="zoom-button-container">
									<div id="zoomOutBtn" class="button-minus"></div>
								</div>
								<div id="joystickContainer" class="joystick-container">
									<div id="joystickFrame" class="joystick-frame"></div>
									<div id="joystickHandle" class="joystick-handle"></div>
								</div>
								<div class="zoom-button-container">
									<div id="zoomInBtn" class="button-plus"></div>
								</div>
							</div>

							<div id="camera-transform" class="row" style="display:none">
								<div class="form-group mb-1" style="grid-template-columns:auto 15% 5% 15% 5% 15%;">
									<h5 class="text-white">Camera Location</h5>
									<input type="number" id="camera-location-x" class="form-control form-control-sm text-center" value="0.0" min="-99999" max="99999"/>
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="camera-location-y" class="form-control form-control-sm text-center" value="0.0" min="-99999" max="99999"/>
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="camera-location-z" class="form-control form-control-sm text-center" value="0.0" min="-99999" max="99999"/>
									<h5 class="text-white">Camera Rotation</h5>
									<input type="number" id="camera-rotation-p" class="form-control form-control-sm text-center" value="0.0" min="-360" max="360"/>
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="camera-rotation-y" class="form-control form-control-sm text-center" value="0.0" min="-360" max="360"/>
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="camera-rotation-r" class="form-control form-control-sm text-center" value="0.0" min="-360" max="360"/>
									<h5 class="text-white">Character Location</h5>
									<input type="number" id="character-location-x" class="form-control form-control-sm text-center" value="0.0" min="-99999" max="99999"/>
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="character-location-y" class="form-control form-control-sm text-center" value="0.0" min="-99999" max="99999"/>
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="character-location-z" class="form-control form-control-sm text-center" value="0.0" min="-99999" max="99999"/>
									<h5 class="text-white">Character Rotation</h5>
									<input type="number" id="character-rotation-p" class="form-control form-control-sm text-center" value="0.0" min="-360" max="360"/>
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="character-rotation-y" class="form-control form-control-sm text-center" value="0.0" min="-360" max="360"/>
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="character-rotation-r" class="form-control form-control-sm text-center" value="0.0" min="-360" max="360"/>
								</div>
								<div class="form-group" style="grid-template-columns:80% 20%;">
									<h5 class="text-white">Character Move BlendTime</h5>
									<input type="number" id="character-move-blendtime" class="form-control form-control-sm text-center" min="0" max="100" value="2">
									<h5 class="text-white">Character Rotate BlendTime</h5>
									<input type="number" id="character-rotate-blendtime" class="form-control form-control-sm text-center" min="0" max="100" value="2">
								</div>
							</div>
						</div>

						<h3 class="collapsible collapsible-active text-white">Facial/Body Animation</h3>
						<div class="content content-active">
							<h5 class="text-white">Looping Anim List</h5>
							<div class="mb-1">
								<div class="form-group" style="grid-template-columns:auto min-content; column-gap:5px;">
									<select id="loopAnimation-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<input type="button" id="loopAnimation-dock" class="btn btn-primary btn-sm" value="&#10010; Favorite">
								</div>
							</div>
							<h5 class="text-white">Triggered Anim List</h5>
							<div class="mb-1">
								<div class="form-group" style="grid-template-columns:auto min-content min-content; column-gap:5px;">
									<select id="triggerAnimation-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<input type="button" id="triggerAnimation-button" class="btn btn-primary btn-sm" value="Trigger">
									<input type="button" id="triggerAnimation-dock" class="btn btn-primary btn-sm" value="&#10010; Favorite">
								</div>
							</div>
							<h5 class="text-white">Facial Expression</h5>
							<div class="mb-1">
								<div class="form-group" style="grid-template-columns:auto min-content; column-gap:5px;">
									<select id="expression-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<input type="button" id="expression-dock" class="btn btn-primary btn-sm" value="&#10010; Favorite">
								</div>
							</div>
							<h5 class="text-white">Auto Blink
								<label class="tgl-switch">
									<input type="checkbox" id="autoblink-switch" class="tgl tgl-flat" checked>
									<div class="tgl-slider"></div>
								</label>
							</h5>
							<h5 class="collapsible text-white">Favorites</h5>
							<div id="animation-favorites-content" class="content">
								<div id="animation-favorites-tab-panel" class="tab"></div>
							</div>
						</div>

						<h3 class="collapsible text-white">Look-At</h3>
						<div class="content">
							<div class="form-group mb-1" style="grid-template-columns:auto 30%;">
								<h5 class="text-white">Loot At Target</h5>
								<select id="lookAt-select" class="form-select form-select-sm text-start">
									<option value="0" selected>None</option>
									<option value="1">Camera</option>
									<option value="2">Custom</option>
								</select>
								<h5 class="text-white">Lock Pitch</h5>
								<label class="tgl-switch">
									<input type="checkbox" id="lookAtCamLockPitch" class="tgl tgl-flat" checked>
									<div class="tgl-slider"></div>
								</label>
								<h5 class="text-white">Eye Only</h5>
								<label class="tgl-switch">
									<input type="checkbox" id="lookAtCamEyeOnly" class="tgl tgl-flat">
									<div class="tgl-slider"></div>
								</label>
							</div>
							<div id="lookAtOptions-container" style="display:none;">
								<h5 class="collapsible collapsible-active text-white">Eye Parameters</h5>
								<div class="content content-active">
									<div class="form-group" style="grid-template-columns:auto 10% 30%;">
										<h5 class="text-white">Look In/Out</h5>
										<output id="LookAtCamOffsetX_num" class="text-white text-center" for="LookAtCamOffsetX_Range">0</output>
										<input type="range" min="-1" max="1" value="0" step="0.01" class="slider" id="LookAtCamOffsetX_Range">
										<h5 class="text-white">Look Up/Down</h5>
										<output id="LookAtCamOffsetY_num" class="text-white text-center" for="LookAtCamOffsetY_Range">0</output>
										<input type="range" min="-1" max="1" value="-0.2" step="0.01" class="slider" id="LookAtCamOffsetY_Range">
									</div>
									<div class="form-group" style="grid-template-columns:auto 30%;">
										<h5 class="text-white">Horizontal Limit Angle</h5>
										<input type="number" id="lookAtCamLimitAngle-horizontal-num" class="form-control form-control-sm text-center" name="" min="0" max="90" value="30" step="1">
										<h5 class="text-white">Vertical Limit Angle</h5>
										<input type="number" id="lookAtCamLimitAngle-vertical-num" class="form-control form-control-sm text-center" name="" min="0" max="90" value="10" step="1">
										<h5 class="text-white">BlendTime (ms)</h5>
										<input type="number" id="lookAtCamBlendTime-num" class="form-control form-control-sm text-center" value="100" step="100">
									</div>
								</div>
							</div>
							<div id="lookAtOptions-custom-container" style="display:none;">
								<h5 class="collapsible collapsible-active text-white">Custom Rotate</h5>
								<div class="content content-active">
									<div class="form-group" style="grid-template-columns:auto 30%;">
										<h5 class="text-white">Yaw</h5>
										<input type="number" id="lookAtCam-custom-rotate-yaw" class="form-control form-control-sm text-center" name="" min="-180" max="180" value="90" step="0.1">
										<h5 class="text-white">Pitch</h5>
										<input type="number" id="lookAtCam-custom-rotate-pitch" class="form-control form-control-sm text-center" name="" min="-180" max="180" value="0" step="0.1">
										<h5 class="text-white">Eye Speed</h5>
										<input type="number" id="lookAtCam-custom-rotate-eye-speed" class="form-control form-control-sm text-center" name="" min="0" max="100" value="3" step="0.1">
										<h5 class="text-white">Head Speed</h5>
										<input type="number" id="lookAtCam-custom-rotate-head-speed" class="form-control form-control-sm text-center" name="" min="0" max="100" value="1" step="0.1">
										<h5 class="text-white">Body Speed</h5>
										<input type="number" id="lookAtCam-custom-rotate-body-speed" class="form-control form-control-sm text-center" name="" min="0" max="100" value="0.5" step="0.1">
									</div>
								</div>
							</div>
						</div>

						<h3 class="collapsible text-white">Background</h3>
						<div class="content">
							<h5 class="text-white">Resolution
								<div style="float:right;">
									<label class="text-white">
										<input type="number" id="resolution-width" class="form-control form-control-sm text-center" min="640" max="7680" value="1920" step="1">
									</label>
									<span>x</span>
									<label class="text-white">
										<input type="number" id="resolution-height" class="form-control form-control-sm text-center" min="360" max="4320" value="1080" step="1">
									</label>
									<button id="resolution-button" class="btn btn-primary btn-sm">Apply</button>
								</div>
							</h5>

							<h5 class="text-white">Background List</h5>
							<div class="mb-1">
								<div class="form-group mb-1" style="grid-template-columns:auto min-content; column-gap:5px;">
									<select id="background-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<input type="button" id="bg-file-dock" class="btn btn-primary btn-sm" value="Dock">
								</div>
								<div id="bg-file-dock-container"></div>
								<div class="form-group" style="grid-template-columns:auto min-content; column-gap:5px;">
									<div>
										<input type="file" id="bg-file" style="display:none" accept=".jpg,.jpeg,.png,.mp4">
										<input type="button" id="bg-file-button" class="btn btn-primary btn-sm" value="Choose File">
										<label id="bg-file-text" class="text-white align-bottom h6">No file chosen</label>
									</div>
									<input type="button" id="bg-file-upload" class="btn btn-primary btn-sm" value="Upload">
								</div>
							</div>
							<div class="form-group" style="grid-template-columns:auto 30%;">
								<h5 class="text-white">Background Style</h5>
								<select id="background-style-select" class="form-select form-select-sm text-start">
									<option value="Stretch" selected>Stretch</option>
									<option value="Tiling">Tiling</option>
									<option value="Fill">Fill</option>
								</select>
								<h5 class="text-white">Background Mode</h5>
								<select id="background-mode-select" class="form-select form-select-sm text-start">
									<option value="Composure" selected>Composure</option>
									<option value="Legacy">Legacy</option>
								</select>
								<h5 class="text-white">BlendTime</h5>
								<input type="number" id="background-blendtime" class="form-control form-control-sm text-center" min="0" value="1" step="0.1">
								<h5 class="text-white">Color</h5>
								<input type="color" id="background-color" value="#ffffff" style="width:100%;">
							</div>
							<div id="bgAi" class="row" style="display:none">
								<h5 class="text-white">Generative AI Prompt</h5>
								<form style="margin-top:2px;" onsubmit="return false;">
									<textarea type="textarea" id="bgAiPrompt-text" class="form-control" required></textarea>
									<input type="button" id="bgAiPrompt-button" class="btn btn-primary btn-sm" style="margin-top:2px;" value="Submit">
								</form>
							</div>
						</div>

						<h3 class="collapsible text-white">Lighting</h3>
						<div class="content">
							<h5 class="text-white">Lightset List</h5>
							<div class="mb-1">
								<select id="lightset-select" class="form-select form-select-sm text-start">
									<option value="none" selected>None</option>
								</select>
							</div>
							<h5 class="collapsible text-white">Advanced</h5>
							<div class="content">
								<h5 class="text-white">Light List</h5>
								<div class="mb-1">
									<select id="light-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
								</div>
								<h5 class="text-white">Enable
								<label class="tgl-switch">
									<input type="checkbox" id="light-switch" class="tgl tgl-flat">
									<div class="tgl-slider"></div>
								</label></h5>
								<div class="form-group" style="grid-template-columns:auto 45%;">
									<h5 class="text-white">Intensity</h5>
									<input type="number" class="form-control form-control-sm text-center" id="light-intensity" min="0" step="1" value="0">
									<h5 class="text-white">Color</h5>
									<input type="color" id="light-color" value="#ffffff" style="width:100%;">
								</div>
								<div class="form-group mb-1" style="grid-template-columns:auto 20% 5% 20% 5% 20%;">
									<h5 class="text-white">Location</h5>
									<input type="number" id="light-loc-X" class="form-control form-control-sm text-center" value="0" step="1">
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="light-loc-Y" class="form-control form-control-sm text-center" value="0" step="1">
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="light-loc-Z" class="form-control form-control-sm text-center" value="0" step="1">
								</div>
								<div class="form-group mb-1" style="grid-template-columns:auto 20% 5% 20% 5% 20%;">
									<h5 class="text-white">Rotation</h5>
									<input type="number" id="light-rot-X" class="form-control form-control-sm text-center" value="0" step="1">
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="light-rot-Y" class="form-control form-control-sm text-center" value="0" step="1">
									<h5 class="text-white text-center">x</h5>
									<input type="number" id="light-rot-Z" class="form-control form-control-sm text-center" value="0" step="1">
								</div>
								<div class="form-group" style="grid-template-columns:auto 30%;">
									<h5 class="text-white">Show Gizmo</h5>
									<label class="tgl-switch">
										<input type="checkbox" id="light-gizmo-switch" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</div>
							</div>

							<h5 class="text-white">Lighting Follow
								<label class="tgl-switch">
									<input type="checkbox" id="lightingFollow-switch" class="tgl tgl-flat">
									<div class="tgl-slider"></div>
								</label>
							</h5>
						</div>

						<h3 class="collapsible text-white">Effect</h3>
						<div class="content">
							<h5 class="text-white">Environment Particle</h5>
							<div class="mb-1">
								<select id="env-particle-select" class="form-select form-select-sm text-start" style="resize:vertical;" multiple>
									<option value="none" selected>None</option>
								</select>
							</div>
							<h5 class="text-white">Triggered Particle</h5>
							<div class="form-group mb-1" style="grid-template-columns:auto min-content; column-gap:5px;">
								<select id="trigger-particle-select" class="form-select form-select-sm text-start">
									<option value="none" selected>None</option>
								</select>
								<input type="button" id="trigger-particle" class="btn btn-primary btn-sm" value="Trigger">
							</div>
							<div class="form-group" style="grid-template-columns:auto 20% 5% 20% 5% 20%;">
								<h5 class="text-white">Offset</h5>
								<input type="number" id="particle-offsetX" class="form-control form-control-sm text-center" value="0" step="1">
								<h5 class="text-white text-center">x</h5>
								<input type="number" id="particle-offsetY" class="form-control form-control-sm text-center" value="0" step="1">
								<h5 class="text-white text-center">x</h5>
								<input type="number" id="particle-offsetZ" class="form-control form-control-sm text-center" value="0" step="1">
							</div>
						</div>
					</div>

					<!-- Modal -->
					<div id="ssmlModel" class="modal fade" tabindex="-1">
						<div class="modal-dialog">
							<div class="modal-content">
								<div class="modal-header" style="height:1rem; background-color:gainsboro;">
									<h6 class="modal-title">Momentum Cloud</h6>
									<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
								</div>
								<div class="modal-body">
									<p>The Speed input is disabled while on SSML enabled. Please use SSML prosody element to control the speaking rate.</p>
									<input type="checkbox" id="ssmlModel-switch">
									<label for="ssmlModel-switch">Don't show this again</label>
									<input type="button" id="tts-stop-button" class="btn btn-primary float-end" data-bs-dismiss="modal" value="Close">
								</div>
							</div>
						</div>
					</div>
					<div class="col-md-6">
						<h3 class="collapsible collapsible-active text-white">Speech</h3>
						<div class="content content-active" style="padding-left:5px; padding-right:5px; border:none;">
							<div id="audiosource-tab-panel" class="tab">
								<!-- Nav tabs -->
								<ul class="nav nav-tabs text-white h5">
									<li class="nav-item">
										<button class="nav-link active" data-bs-toggle="tab" data-bs-target="#audiosource-tts-container">TextToSpeech</button>
									</li>
									<li class="nav-item">
										<button class="nav-link" data-bs-toggle="tab" data-bs-target="#audiosource-files-container">AudioFile</button>
									</li>
									<li class="nav-item">
										<button class="nav-link" data-bs-toggle="tab" data-bs-target="#audiosource-stream-container">Streaming</button>
									</li>
								</ul>
								<!-- Tab panes -->
								<div class="tab-content">
									<div id="audiosource-tts-container" class="tab-pane fade show active">
										<h5 class="text-white fw-bold">Converts text into spoken audio</h5>
										<div class="row mb-1">
											<div class="col-md-6">
												<h5 class="text-white">Engine</h5>
												<select id="tts-engine-control" class="form-select form-select-sm text-start">
													<option value="none" selected>None</option>
												</select>
											</div>
											<div class="col-md-6">
												<h5 class="text-white">Language</h5>
												<select id="tts-language-control" class="form-select form-select-sm text-start">
													<option value="none" selected>None</option>
												</select>
											</div>
										</div>
										<div class="row mb-1">
											<div class="col-md-6">
												<h5 class="text-white">Voice</h5>
												<select id="tts-voice-control" class="form-select form-select-sm text-start">
													<option value="none" selected>None</option>
												</select>
											</div>
											<div class="col-md-6">
												<h5 class="text-white">Speed</h5>
												<input type="number" id="tts-speed-num" class="form-control form-select-sm text-center" name="" min="0.5" max="4.0" value="1.0" step="0.01">
											</div>
										</div>
										<div id="tts-tab-panel" class="tab">
											<!-- Nav tabs -->
											<ul class="nav nav-tabs text-white h5" style="border-bottom:1px solid gray;">
												<li class="nav-item">
													<button class="nav-link active" data-bs-toggle="tab" data-bs-target="#tts-text-container">Text</button>
												</li>
												<li class="nav-item">
													<button class="nav-link" data-bs-toggle="tab" data-bs-target="#tts-chat-container">Chat</button>
												</li>
											</ul>
											<!-- Tab panes -->
											<div class="tab-content text-white h6" style="box-shadow:none;">
												<div id="tts-text-container" class="tab-pane fade show active">
													<div class="mb-1">
														<input type="radio" id="tts-speak-radio" name="tts-synthesis-mode" onclick="document.getElementById('tts-saveas-filename-text').disabled = true;" checked>
														<label for="tts-speak-radio">Speak</label>
														<input type="radio" id="tts-saveas-radio" name="tts-synthesis-mode" onclick="document.getElementById('tts-saveas-filename-text').disabled = false;">
														<label for="tts-saveas-radio">SaveAs:</label>
														<input type="text" id="tts-saveas-filename-text" placeholder="Enter File Name" value="Example" style="display:inline; width:20%;" disabled>
														<label style="text-indent:5px;">Stream
															<label class="tgl-switch" style="float:left">
																<input type="checkbox" id="stream-switch" class="tgl tgl-flat">
																<div class="tgl-slider"></div>
															</label>
														</label>
														<label style="text-indent:5px;">SSML
															<label class="tgl-switch" style="float:left">
																<input type="checkbox" id="ssml-switch" class="tgl tgl-flat">
																<div class="tgl-slider"></div>
															</label>
														</label>
													</div>
													<form id="tts-text-form" class="mb-1" onsubmit="return false;">
														<textarea type="textarea" id="tts-text-area" class="form-control" required></textarea>
														<input type="submit" id="tts-text-submit-button" class="btn btn-primary btn-sm" style="margin-top:2px;" value="Submit">
														<input type="button" id="tts-text-stop-button" class="btn btn-primary btn-sm" style="margin-top:2px;" value="Stop">
													</form>

													<h6 class="text-white">Second Caption<span id="tts-second-text-warning" style="color: rgb(255, 255, 255); font-style: italic; font-size: 0.6em;"> (Please turn on Caption before use)</span></h6>
													<textarea type="textarea" id="tts-second-text-area" class="form-control" disabled></textarea>
												</div>
												<div id="tts-chat-container" class="tab-pane fade">
													<div class="form-group mb-1" style="grid-template-columns:min-content 25% min-content 30% auto; align-items:center; column-gap:3px;">
														<label class="text-white">Model</label>
														<select id="tts-chat-model-control" class="form-select-sm text-start">
															<option value="none" selected>None</option>
														</select>
														<input type="button" id="tts-chat-mic-button" style="width:24px; height:24px;">
														<select id="tts-chat-mic-lang-control" class="form-select-sm text-start">
															<option value="cmn-Hans-CN">普通话 (中国大陆)</option>
															<option value="cmn-Hant-TW">中文 (台灣)</option>
															<option value="ja-JP">日本語</option>
															<option value="en-US" selected>English</option>
														</select>
														<label style="text-indent:5px;">History
															<label class="tgl-switch" style="float:left">
																<input type="checkbox" id="tts-chat-history-switch" class="tgl tgl-flat">
																<div class="tgl-slider"></div>
															</label>
														</label>
														<label id="tts-chat-openairealtime-voice-label" class="text-white" style="display:none;">Voice</label>
														<select id="tts-chat-openairealtime-voice-control" class="form-select-sm text-start" style="display:none;">
															<option value="alloy" selected>Alloy</option>
															<option value="ash">Ash</option>
															<option value="ballad">Ballad</option>
															<option value="coral">Coral</option>
															<option value="echo">Echo</option>
															<option value="sage">Sage</option>
															<option value="shimmer">Shimmer</option>
															<option value="verse">Verse</option>
														</select>
													</div>
													<form id="tts-chat-form" onsubmit="return false;">
														<textarea type="textarea" id="tts-chat-area" class="form-control" required></textarea>
														<input type="submit" id="tts-chat-submit-button" class="btn btn-primary btn-sm" style="margin-top:2px;" value="Submit">
														<input type="button" id="tts-chat-stop-button" class="btn btn-primary btn-sm" style="margin-top:2px;" value="Stop">
													</form>
												</div>
											</div>
										</div>
									</div>

									<div id="audiosource-files-container" class="tab-pane fade">
										<h5 class="text-white fw-bold">Playing an audio file</h5>
										<div class="row">
											<div class="form-group mb-1" style="grid-template-columns:auto min-content min-content; column-gap:5px;">
												<select id="audio-select" class="form-select form-select-sm text-start">
													<option value="none" selected>None</option>
												</select>
												<input type="button" id="play-audio-button" class="btn btn-primary btn-sm" value="Play">
												<input type="button" id="stop-audio-button" class="btn btn-primary btn-sm" value="Stop">
											</div>
											<h5 class="text-white">Audio File Upload</h5>
											<div class="form-group mb-1" style="grid-template-columns:auto min-content; column-gap:5px;">
												<div>
													<input type="file" id="audio-file" style="display:none" accept=".wav,.mp3">
													<input type="button" id="audio-file-button" class="btn btn-primary btn-sm" value="Choose File">
													<label id="audio-file-text" class="text-white align-bottom h6">No file chosen</label>
												</div>
												<input type="button" id="audio-file-upload" class="btn btn-primary btn-sm" value="Upload">
											</div>
											<h5 class="text-white">Caption File Upload<span style="color: rgb(255, 255, 255); font-style: italic; font-size: 0.6em;"> (Same as audio file name)</span></h5>
											<div class="form-group mb-1" style="grid-template-columns:auto min-content; column-gap:5px;">
												<div>
													<input type="file" id="audio-caption-file" style="display:none" accept=".txt">
													<input type="button" id="audio-caption-file-button" class="btn btn-primary btn-sm" value="Choose File">
													<label id="audio-caption-file-text" class="text-white align-bottom h6">No file chosen</label>
												</div>
												<input type="button" id="audio-caption-file-upload" class="btn btn-primary btn-sm" value="Upload">
											</div>
										</div>
									</div>

									<div id="audiosource-stream-container" class="tab-pane fade">
										<h5 class="text-white fw-bold">Real-Time audio streaming</h5>
										<h5 class="text-white">
											<div>
												<input type="radio" id="audio-stream-mic-radio" name="audio_stream" checked>
												<label for="audio-stream-mic-radio" class="text-white">Microphone</label>
												<div id="audio-stream-mic-container" style="position:relative;">
													<h6 class="text-white">
														<label style="position:absolute; bottom:2px; right:0; text-indent:5px;">Enable
															<label class="tgl-switch" style="float:left">
																<input type="checkbox" id="usemic-tgl" class="tgl tgl-flat">
																<div class="tgl-slider"></div>
															</label>
														</label>
													</h6>
												</div>
											</div>
											<div>
												<input type="radio" id="audio-stream-file-radio" name="audio_stream">
												<label for="audio-stream-file-radio" class="text-white">File</label>
												<div id="audio-stream-file-container" class="div-disabled" style="margin-left:1em">
													<div class="form-group mb-1" style="grid-template-columns:auto min-content min-content; column-gap:5px;">
														<div>
															<input type="file" id="audio-stream-file" style="display:none" accept=".wav,.mp3">
															<input type="button" id="audio-stream-file-button" class="btn btn-primary btn-sm" value="Choose File">
															<label id="audio-stream-file-text" class="text-white align-bottom h6">No file chosen</label>
														</div>
														<input type="button" id="audio-stream-file-play-button" class="btn btn-primary btn-sm" value="Play" onclick="audioStream_play()">
														<input type="button" id="audio-stream-file-stop-button" class="btn btn-primary btn-sm" value="Stop" onclick="audioStream_stop()">
													</div>
												</div>
											</div>
											<div>
												<input type="radio" id="audio-stream-url-radio" name="audio_stream">
												<label for="audio-stream-url-radio" class="text-white">URL</label>
												<div id="audio-stream-url-container" class="div-disabled" style="margin-left:1em">
													<h6 class="text-white">
														<div class="form-group" style="grid-template-columns:auto min-content min-content; column-gap:5px;">
															<input type="text" id="audio-stream-url-text" class="form-control form-control-sm text-start" value="" placeholder="http://...">
															<input type="button" id="audio-stream-url-play-button" class="btn btn-primary btn-sm" value="Play" onclick="audioStream_play()">
															<input type="button" id="audio-stream-url-stop-button" class="btn btn-primary btn-sm" value="Stop" onclick="audioStream_stop()">
														</div>
													</h6>
												</div>
											</div>
										</h5>
									</div>

									<hr style="border-top:1px solid grey; margin:0.5em 0; opacity:1;"/>
									<div id="audio-settings-container" style="padding-left:5px; padding-right:5px">
										<div class="form-group mb-1" style="grid-template-columns:70% 30%;">
											<h5 class="text-white">Audio Volume</h5>
											<input type="number" id="audio-volume" class="form-control form-control-sm text-center" name="" min="0" max="1.0" value="1.0" step="0.1">
										</diV>
									</div>
									<div id="caption-container" style="padding-left:5px; padding-right:5px">
										<h5 class="collapsible text-white">Caption</h5>
										<div class="content">
											<h5 class="text-white">Enable
												<label class="tgl-switch">
													<input type="checkbox" id="caption-switch" class="tgl tgl-flat">
													<div class="tgl-slider"></div>
												</label>
											</h5>
											<div class="form-group mb-1" style="grid-template-columns:70% 30%;">
												<h5 class="text-white">Clear Caption</h5>
												<div>
													<input type="button" id="caption-clear-button" class="btn btn-primary btn-sm float-end" value="Clear">
												</div>
											</div>
											<h5 class="text-white">Auto Clear Caption
												<label class="tgl-switch">
													<input type="checkbox" id="caption-auto-clear-switch" class="tgl tgl-flat">
													<div class="tgl-slider"></div>
												</label>
											</h5>
											<h5 class="text-white">Show BG
												<label class="tgl-switch">
													<input type="checkbox" id="caption-bg-switch" class="tgl tgl-flat">
													<div class="tgl-slider"></div>
												</label>
											</h5>

											<div class="form-group mb-1" style="grid-template-columns:auto 30% 5% 30%;">
												<h5 class="text-white">Shift</h5>
												<input type="number" id="caption-shiftX" class="form-control form-control-sm text-center" min="-9999" max="9999" value="0">
												<h5 class="text-white text-center">x</h5>
												<input type="number" id="caption-shiftY" class="form-control form-control-sm text-center" min="-9999" max="9999" value="0">
												<h5 class="text-white">Size</h5>
												<input type="number" id="caption-sizeX" class="form-control form-control-sm text-center" min="1" max="9999" value="960">
												<h5 class="text-white text-center">x</h5>
												<input type="number" id="caption-sizeY" class="form-control form-control-sm text-center" min="1" max="9999" value="270">
												<h5 class="text-white">Padding</h5>
												<div></div><div></div>
												<input type="number" id="caption-padding" class="form-control form-control-sm text-center" min="0" max="999" value="10">
											</div>

											<div class="form-group mb-1" style="grid-template-columns:auto 45% 30%; column-gap:2px;">
												<h5 class="text-white">Font</h5>
												<select id="caption-font-select" class="form-select form-select-sm text-start">
													<option value="none" selected>None</option>
												</select>
												<div class="form-group" style="grid-template-columns:auto 0% min-content;">
													<input type="number" id="caption-font-size" class="form-control form-control-sm text-center" value="24" min="1" max="100">
													<input type="color" id="caption-font-color-pick" value="#ffffff" style="visibility:hidden;width:0px;">
													<button id="caption-font-color-button"></button>
												</div>
											</div>

											<h5 class="text-white">Background List</h5>
											<div class="mb-1">
												<select id="caption-background-select" class="form-select form-select-sm text-start">
													<option value="none" selected>None</option>
												</select>
												<div class="mb-1"></div>
												<input type="file" id="caption-bg-file" style="display:none" accept=".jpg,.jpeg,.png">
												<input type="button" id="caption-bg-file-button" class="btn btn-primary btn-sm" value="Choose File">
												<label id="caption-bg-file-text" class="text-white h6">No file chosen</label>
												<input type="button" id="caption-bg-file-upload" class="btn btn-primary btn-sm float-end" value="Upload">
											</div>

											<form id="caption-custom-form" class="mb-1" onsubmit="return false;">
												<div class="mb-1">
													<h5 class="text-white">Custom Caption</h5>
													<textarea type="textarea" id="caption-custom-text-area" class="form-control" disabled></textarea>
												</div>
												<div class="form-group mb-1" style="grid-template-columns:70% 30%; column-gap:2px;">
													<h5 class="text-white">Custom Time</h5>
													<input type="number" id="caption-custom-text-time" class="form-control form-control-sm text-center" name="" min="0" max="9999" value="1" step="1">
												</diV>
												<input type="submit" id="caption-custom-text-submit-button" class="btn btn-primary btn-sm" value="Submit">
											</form>
										</div>
									</div>

									<div id="lipsync-container" style="padding-left:5px; padding-right:5px">
										<h5 class="collapsible text-white">LipSync</h5>
										<div class="content">
											<div class="form-group" style="grid-template-columns:70% 30%;">
												<h5 class="text-white">Enable</h5>
												<label class="tgl-switch">
													<input type="checkbox" id="lipsync-tgl" class="tgl tgl-flat" checked>
													<div class="tgl-slider"></div>
												</label>
												<h5 class="text-white">Audio Offset (ms)</h5>
												<label class="tgl-switch">
													<input type="number" id="lipsync-audio-offset-num" class="form-control form-control-sm text-center" name="" min="0" max="10000" value="0" step="10">
												</label>
											</div>
											<h5 class="text-white" >Smoothing</h5>
											<div style="padding-left:1em;">
												<div class="form-group" style="grid-template-columns:70% 30%;">
													<h6 class="text-white">Mode</h6>
													<select id="bs-smooth-mode-select" class="form-select form-select-sm text-start">
														<option value="0" selected>None</option>
													</select>
													<h6 class="text-white">Frame Size(Moving Average)</h6>
													<input type="number" id="bs-smooth-framesize" class="form-control form-control-sm text-center" name="" min="1" max="100" value="2" step="1">
													<h6 class="text-white">Alpha(Linear Interpolation)</h6>
													<input type="number" id="bs-smooth-alpha" class="form-control form-control-sm text-center" name="" min="0" max="1" value="1" step="0.1">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

						<h3 class="collapsible text-white">Record</h3>
						<div class="content">
							<div class="form-group" style="grid-template-columns:70% 30%;">
								<h5 class="text-white">Alpha Channel</h5>
								<label class="tgl-switch">
									<input type="checkbox" id="alpha-record-checkbox" class="tgl tgl-flat">
									<div class="tgl-slider"></div>
								</label>
							</div>
							<div id="watermark-container" class="div-disabled">
								<h5 class="text-white">Watermark
									<label class="tgl-switch">
										<input type="checkbox" id="watermark-record-checkbox" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h5>
							</div>
							<div class="form-group" style="grid-template-columns:70% 30%;">
								<h5 class="text-white">FrameRate</h5>
								<input type="number" id="record-framerate" class="form-control form-control-sm text-center" min="1" max="60" value="30" step="1">
								<h5 class="text-white">Video Record</h5>
								<div>
									<div class="float-end">
										<button id="start-record-button" class="btn btn-primary btn-sm">Start</button>
										<button id="stop-record-button" class="btn btn-primary btn-sm" disabled>Stop</button>
									</div>
								</div>
								<h5 class="text-white">PNG Record</h5>
								<div>
									<div class="float-end">
										<button id="start-png-record-button" class="btn btn-primary btn-sm">Start</button>
										<button id="stop-png-record-button" class="btn btn-primary btn-sm" disabled>Stop</button>
									</div>
								</div>
							</div>
							<!-- MRQ Start -->
							<br>
							<div class="form-group" style="grid-template-columns:70% 30%;">
								<!-- Title -->
								<!-- <div class="mb-3">
									<h5 class="text-white">Movie Render Queue</h5>
								</div> -->
								<!-- MRQ Mode -->
								<h5 class="text-white">Movie Render Queue Mode</h5>
								<div>
									<div class="float-end">
										<select id="MRQMode-select" class="form-select form-select-sm text-start">
											<option value="0" selected>Single PNG</option>
											<option value="1">Timecoded PNG</option>
										</select>
									</div>
								</div>

								<h5 class="text-white">Timecode Select</h5>
								<div>
									<div class="float-end">
										<input type="file" id="MRQtimecode-file" style="display:none" accept=".csv">
										<input type="button" id="MRQtimecode-file-button" class="btn btn-primary btn-sm" value="Choose File">
										<label id="MRQtimecode-file-text" class="text-white align-bottom h6">No file chosen</label>
									</div>
								</div>

								<!-- Performance Capture Title & Button -->
								<h5 class="text-white">Performance Capture</h5>
								<div class="float-end">
									<button id="performance-capture-button" class="btn btn-primary btn-sm">Start Capture</button>
								</div>
								<h5 class="text-white">Performance recording</h5>
								<select id="performance-select" class="form-select form-select-sm text-start">
									<option value="0" selected>None</option>
								</select>

								<!-- Title -->
								<h5 class="text-white">Start MRQ</h5>
								<!-- Render Button -->
								<div>
									<div class="float-end">
										<button id="start-render-button" class="btn btn-primary btn-sm">Start Rendering</button>
									</div>
								</div>
								<!-- MRQ State and Frame Status -->
								<h6 class="text-white">MRQ State </h6>
								<span class="text-white" id="mrq-state">Idle</span>
								<h6 class="text-white">MRQ Frame </h6>
								<span class="text-white" id="mrq-frame">0</span>

								<!-- Render Time Input -->
								<h5 class="text-white">Render Duration (seconds)</h5>
									<input type="number" id="render-time" class="form-control form-control-sm text-center" min="1" max="600" value="5" step="1">
								<!-- Render Parameters Dropdown -->
								<div class="form-group mb-1" style="grid-template-columns:100% 43%;">
									<h5 class="text-white">Render Settings</h5>
									<select id="MRQConfig-select" class="form-select form-select-sm text-start">
										<option value="0" selected>Full HD 25FPS</option>
										<option value="1">Full HD 30FPS</option>
										<option value="2">4K 25FPS</option>
										<option value="3">4K 30FPS</option>
										<option value="4">Full HD with Alpha 25FPS</option>
										<option value="5">Full HD with Alpha 30FPS</option>
										<option value="6">4K with Alpha 25FPS</option>
										<option value="7">4K with Alpha 30FPS</option>
									</select>
								</div>
							</div>
							<!-- MRQ End -->
						</div>

						<h3 class="collapsible text-white">Mocap</h3>
						<div class="content">
							<div class="form-group mb-1" style="grid-template-columns:70% 30%;">
								<h5 class="text-white">Motion Source</h5>
								<select id="operation-select" class="form-select form-select-sm text-start">
									<option value="0" selected>Preset</option>
									<option value="1">MVN+ARKit</option>
									<option value="2">Preset+ARKit</option>
									<option value="3">VMC</option>
									<option value="4">VMC+ARKit</option>
								</select>
							</div>
							<h5 class="text-white">MVN Forward Lock Enable
								<label class="tgl-switch">
									<input type="checkbox" id="mvn-forwardLock-switch" class="tgl tgl-flat">
									<div class="tgl-slider"></div>
								</label>
							</h5>
							<div class="form-group mb-1" style="grid-template-columns:70% 30%;">
								<h5 class="text-white">Forward Axis Limit</h5>
								<input type="number" class="form-control form-control-sm text-center" id="forward-axis-limit" min="0" step="1" value="10">
							</div>
							<h5 class="text-white">ARKit Correction Enable
								<label class="tgl-switch">
									<input type="checkbox" id="arkit-correction-switch" class="tgl tgl-flat" checked>
									<div class="tgl-slider"></div>
								</label>
							</h5>
							<h5 class="text-white">ARKit Head Rotation Enable
								<label class="tgl-switch">
									<input type="checkbox" id="arkit-head-rotation-switch" class="tgl tgl-flat">
									<div class="tgl-slider"></div>
								</label>
							</h5>
							<div class="form-group" style="grid-template-columns:auto 30%;">
								<h5 class="text-white">BoundScale</h5>
								<input type="number" class="form-control form-control-sm text-center" id="bound-scale" min="1" max="3000" step="1" value="1">
							</div>
						</div>

						<h3 class="collapsible text-white">Streaming Output</h3>
						<div class="content">
							<h5 class="text-white" >NDI Output</h5>
							<div style="padding-left:1em;">
								<h6 class="text-white">Standard NDI Channel
									<label class="tgl-switch">
										<input type="checkbox" id="ndi-output-switch" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h6>
								<h6 class="text-white">Standard NDI Channel with Alpha Background
									<label class="tgl-switch">
										<input type="checkbox" id="ndi-output-switch-alpha-bg" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h6>
								<h6 class="text-white">OWL NDI Channel
									<label class="tgl-switch">
										<input type="checkbox" id="ndi-output-owl-switch" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h6>
								<h6 class="text-white">OWL NDI Channel with Alpha Background
									<label class="tgl-switch">
										<input type="checkbox" id="ndi-output-owl-switch-alpha-bg" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h6>
							</div>
							<h5 class="text-white" >Spout Output</h5>
							<div style="padding-left:1em;">
								<h6 class="text-white">OWL Spout Channel
									<label class="tgl-switch">
										<input type="checkbox" id="spout-output-switch" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h6>
								<h6 class="text-white">OWL Spout Channel with Alpha Background
									<label class="tgl-switch">
										<input type="checkbox" id="spout-output-switch-alpha-bg" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h6>
							</div>
						</div>

						<h3 class="collapsible text-white">Advanced</h3>
						<div class="content">
							<h5 class="text-white">Keyboard Input
								<label class="tgl-switch">
									<input type="checkbox" id="keyboard-input-tgl" class="tgl tgl-flat">
									<div class="tgl-slider"></div>
								</label>
							</h5>

							<h5 class="text-white">Mouse Input
								<label class="tgl-switch">
									<input type="checkbox" id="mouse-input-tgl" class="tgl tgl-flat">
									<div class="tgl-slider"></div>
								</label>
							</h5>

							<div class="form-group mb-1" style="grid-template-columns:70% 30%;">
								<h5 class="text-white">Maximum Framerate</h5>
								<input type="number" id="max-fps" class="form-control form-control-sm text-center" min="1" max="240" value="30">
							</div>

							<h5 class="collapsible text-white">Side By Side (Color & Depth)</h5>
							<div id="sideBySideDepth-container" class="content">
								<h5 class="text-white">Enable
									<label class="tgl-switch">
										<input type="checkbox" id="sideBySideDepth-switch" class="tgl tgl-flat">
										<div class="tgl-slider"></div>
									</label>
								</h5>
								<div class="form-group" style="grid-template-columns:75% 25%;">
									<h5 class="text-white">Depth Start (cm)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="sideBySideDepth-depthSpaceStart-text" value="0">
									<h5 class="text-white">Depth Scale (cm)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="sideBySideDepth-depthSpaceScale-text" value="0">
									<h5 class="text-white">Depth ColorOffset (cm)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="sideBySideDepth-depthSpaceColorOffset-text" value="0">
									<h5 class="text-white">Clamp AvatarMin (0~255)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="sideBySideDepth-grayscaleClampAvatarMin-text" min="0" max="255" value="0">
									<h5 class="text-white">Clamp AvatarMax (0~255)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="sideBySideDepth-grayscaleClampAvatarMax-text" min="0" max="255" value="0">
									<h5 class="text-white">Clamp Background (0~255)</h5>
									<input type="number" class="form-control form-control-sm text-center" id="sideBySideDepth-grayscaleClampBackground-text" min="0" max="255" value="0">
								</div>
							</div>

							<h5 class="collapsible text-white">Scalability</h5>
							<div class="content">
								<div class="form-group" style="grid-template-columns:70% 30%;">
									<h5 class="text-white">AntiAliasing</h5>
									<select id="scalability-AntiAliasing" class="form-select form-select-sm text-start">
										<option value="0">Low</option>
										<option value="1">Medium</option>
										<option value="2">High</option>
										<option value="3">Superior</option>
										<option value="4" selected>Ultimate</option>
									</select>
									<h5 class="text-white">PostProcess</h5>
									<select id="scalability-PostProcess" class="form-select form-select-sm text-start">
										<option value="0">Low</option>
										<option value="1">Medium</option>
										<option value="2">High</option>
										<option value="3">Superior</option>
										<option value="4" selected>Ultimate</option>
									</select>
									<h5 class="text-white">Shadow</h5>
									<select id="scalability-Shadow" class="form-select form-select-sm text-start">
										<option value="0">Low</option>
										<option value="1">Medium</option>
										<option value="2">High</option>
										<option value="3">Superior</option>
										<option value="4" selected>Ultimate</option>
									</select>
									<h5 class="text-white">Texture</h5>
									<select id="scalability-Texture" class="form-select form-select-sm text-start">
										<option value="0">Low</option>
										<option value="1">Medium</option>
										<option value="2">High</option>
										<option value="3">Superior</option>
										<option value="4" selected>Ultimate</option>
									</select>
									<h5 class="text-white">Effects</h5>
									<select id="scalability-Effects" class="form-select form-select-sm text-start">
										<option value="0">Low</option>
										<option value="1">Medium</option>
										<option value="2">High</option>
										<option value="3">Superior</option>
										<option value="4" selected>Ultimate</option>
									</select>
								</div>
								<div>
									<input type="button" id="scalability-reset-button" class="btn btn-primary btn-sm" value="Reset">
									<input type="button" id="scalability-auto-button" class="btn btn-primary btn-sm" value="Auto">
								</div>
							</div>
						</div>

						<h3 class="collapsible text-white">TTS TimeCode Edit<span style="color: turquoise; font-style: italic; font-size: 0.6em;"> (Experimental)</h3>
						<div class="content">
							<h5 class="text-white">Triggered Anim List</h5>
							<div class="mb-1">
								<div class="form-group mb-1" style="grid-template-columns:auto min-content min-content; column-gap:5px;">
									<select id="tc-triggerAnimation-select" class="form-select form-select-sm text-start">
										<option value="none" selected>None</option>
									</select>
									<input type="button" id="tc-triggerAnimation-button" class="btn btn-primary btn-sm" value="Trigger">
									<input type="button" id="tc-triggerAnimation-dock" class="btn btn-primary btn-sm" value="Dock">
								</div>
								<div id="tc-triggerAnimation-dock-container"></div>
							</div>
							<h5 class="text-white">Text</h5>
							<form id="tc-tts-text-form" class="mb-1" onsubmit="return false;">
								<textarea type="textarea" id="tc-tts-text-area" class="form-control" required></textarea>
								<input type="submit" id="tc-process-button" class="btn btn-primary btn-sm" style="margin-top:2px;" value="Process">
							</form>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

</body>
</html>