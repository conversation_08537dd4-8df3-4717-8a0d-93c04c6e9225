/*! ../AFK/AFKController */

/*! ../Config/Config */

/*! ../Config/ConfigUI */

/*! ../DataChannel/DataChannelController */

/*! ../DataChannel/DataChannelLatencyTestController */

/*! ../DataChannel/DataChannelSender */

/*! ../DataChannel/InitialSettings */

/*! ../DataChannel/LatencyTestResults */

/*! ../FreezeFrame/FreezeFrameController */

/*! ../Inputs/InputClassesFactory */

/*! ../Inputs/XRGamepadController */

/*! ../Logger/Logger */

/*! ../Overlay/AFKOverlay */

/*! ../Overlay/ConnectOverlay */

/*! ../Overlay/DisconnectOverlay */

/*! ../Overlay/ErrorOverlay */

/*! ../Overlay/InfoOverlay */

/*! ../Overlay/PlayOverlay */

/*! ../PeerConnectionController/PeerConnectionController */

/*! ../UI/Controls */

/*! ../UI/FullscreenIcon */

/*! ../UI/LabelledButton */

/*! ../UI/OnScreenKeyboard */

/*! ../UI/SettingsPanel */

/*! ../UI/StatsPanel */

/*! ../UI/UIConfigurationTypes */

/*! ../UI/VideoQpIndicator */

/*! ../UeInstanceMessage/ResponseController */

/*! ../UeInstanceMessage/SendMessageController */

/*! ../UeInstanceMessage/StreamMessageController */

/*! ../UeInstanceMessage/ToStreamerMessagesController */

/*! ../Util/CoordinateConverter */

/*! ../Util/EventEmitter */

/*! ../Util/EventListenerTracker */

/*! ../Util/FileUtil */

/*! ../Util/MathUtils */

/*! ../Util/RTCUtils */

/*! ../VideoPlayer/StreamController */

/*! ../VideoPlayer/VideoPlayer */

/*! ../WebRtcPlayer/WebRtcPlayerController */

/*! ../WebSockets/WebSocketController */

/*! ../WebXR/WebXRController */

/*! ../pixelstreamingfrontend */

/*! ./AFK/AFKController */

/*! ./ActionOverlay */

/*! ./AggregatedStats */

/*! ./Application/Application */

/*! ./BaseOverlay */

/*! ./CandidateStat */

/*! ./Config/Config */

/*! ./Config/ConfigUI */

/*! ./Config/SettingBase */

/*! ./Config/SettingFlag */

/*! ./Config/SettingNumber */

/*! ./Config/SettingOption */

/*! ./Config/SettingText */

/*! ./Config/SettingUIBase */

/*! ./Config/SettingUIFlag */

/*! ./Config/SettingUINumber */

/*! ./Config/SettingUIOption */

/*! ./Config/SettingUIText */

/*! ./DataChannel/InitialSettings */

/*! ./DataChannel/LatencyTestResults */

/*! ./DataChannelLatencyTest */

/*! ./DataChannelLatencyTestResults */

/*! ./DataChannelStats */

/*! ./FakeTouchController */

/*! ./FreezeFrame */

/*! ./FullscreenIcon */

/*! ./GamepadController */

/*! ./HoveringMouseEvents */

/*! ./InboundRTPStats */

/*! ./KeyboardController */

/*! ./LatencyTest */

/*! ./LockedMouseEvents */

/*! ./Logger/Logger */

/*! ./MessageReceive */

/*! ./MessageSend */

/*! ./MouseButtons */

/*! ./MouseController */

/*! ./OutBoundRTPStats */

/*! ./Overlay/AFKOverlay */

/*! ./Overlay/ActionOverlay */

/*! ./Overlay/BaseOverlay */

/*! ./Overlay/ConnectOverlay */

/*! ./Overlay/DisconnectOverlay */

/*! ./Overlay/ErrorOverlay */

/*! ./Overlay/InfoOverlay */

/*! ./Overlay/PlayOverlay */

/*! ./Overlay/TextOverlay */

/*! ./PeerConnectionController/AggregatedStats */

/*! ./PeerConnectionController/CandidatePairStats */

/*! ./PeerConnectionController/CandidateStat */

/*! ./PeerConnectionController/DataChannelStats */

/*! ./PeerConnectionController/InboundRTPStats */

/*! ./PeerConnectionController/OutBoundRTPStats */

/*! ./PixelStreaming/PixelStreaming */

/*! ./SessionStats */

/*! ./SettingBase */

/*! ./SettingFlag */

/*! ./SettingNumber */

/*! ./SettingOption */

/*! ./SettingText */

/*! ./SettingUIBase */

/*! ./SettingUIFlag */

/*! ./SettingUINumber */

/*! ./SettingUIOption */

/*! ./SettingUIText */

/*! ./SettingsIcon */

/*! ./SignallingProtocol */

/*! ./SpecialKeyCodes */

/*! ./StatsIcon */

/*! ./StreamStats */

/*! ./Styles/PixelStreamingApplicationStyles */

/*! ./TextOverlay */

/*! ./TouchController */

/*! ./UI/UIConfigurationTypes */

/*! ./UeInstanceMessage/StreamMessageController */

/*! ./Util/CoordinateConverter */

/*! ./Util/EventEmitter */

/*! ./WebRtcPlayer/WebRtcPlayerController */

/*! ./WebSockets/MessageReceive */

/*! ./WebSockets/MessageSend */

/*! ./WebSockets/SignallingProtocol */

/*! ./WebSockets/WebSocketController */

/*! ./WebXR/WebXRController */

/*! ./XRIcon */

/*! @epicgames-ps/lib-pixelstreamingfrontend-ue5.4 */

/*! jss */

/*! jss-plugin-camel-case */

/*! jss-plugin-global */

/*! sdp */

/*!**********************!*\
  !*** external "jss" ***!
  \**********************/

/*!**********************!*\
  !*** external "sdp" ***!
  \**********************/

/*!**************************!*\
  !*** ./src/UI/XRIcon.ts ***!
  \**************************/

/*!****************************!*\
  !*** ./src/UI/Controls.ts ***!
  \****************************/

/*!*****************************!*\
  !*** ./src/UI/StatsIcon.ts ***!
  \*****************************/

/*!******************************!*\
  !*** ./src/Config/Config.ts ***!
  \******************************/

/*!******************************!*\
  !*** ./src/Logger/Logger.ts ***!
  \******************************/

/*!******************************!*\
  !*** ./src/UI/StatsPanel.ts ***!
  \******************************/

/*!******************************!*\
  !*** ./src/Util/FileUtil.ts ***!
  \******************************/

/*!******************************!*\
  !*** ./src/Util/RTCUtils.ts ***!
  \******************************/

/*!*******************************!*\
  !*** ./src/UI/LatencyTest.ts ***!
  \*******************************/

/*!*******************************!*\
  !*** ./src/Util/MathUtils.ts ***!
  \*******************************/

/*!********************************!*\
  !*** ./src/Config/ConfigUI.ts ***!
  \********************************/

/*!********************************!*\
  !*** ./src/UI/SettingsIcon.ts ***!
  \********************************/

/*!*********************************!*\
  !*** ./src/UI/SettingsPanel.ts ***!
  \*********************************/

/*!**********************************!*\
  !*** ./src/AFK/AFKController.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/UI/FullscreenIcon.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/UI/LabelledButton.ts ***!
  \**********************************/

/*!**********************************!*\
  !*** ./src/Util/EventEmitter.ts ***!
  \**********************************/

/*!***********************************!*\
  !*** ./src/Config/SettingBase.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/Config/SettingFlag.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/Config/SettingText.ts ***!
  \***********************************/

/*!***********************************!*\
  !*** ./src/Overlay/AFKOverlay.ts ***!
  \***********************************/

/*!************************************!*\
  !*** ./src/Inputs/MouseButtons.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/Overlay/BaseOverlay.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/Overlay/InfoOverlay.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/Overlay/PlayOverlay.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/Overlay/TextOverlay.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/UI/OnScreenKeyboard.ts ***!
  \************************************/

/*!************************************!*\
  !*** ./src/UI/VideoQpIndicator.ts ***!
  \************************************/

/*!************************************!*\
  !*** external "jss-plugin-global" ***!
  \************************************/

/*!*************************************!*\
  !*** ./src/Config/SettingNumber.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/Config/SettingOption.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/Config/SettingUIBase.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/Config/SettingUIFlag.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/Config/SettingUIText.ts ***!
  \*************************************/

/*!*************************************!*\
  !*** ./src/Overlay/ErrorOverlay.ts ***!
  \*************************************/

/*!**************************************!*\
  !*** ./src/Overlay/ActionOverlay.ts ***!
  \**************************************/

/*!**************************************!*\
  !*** ./src/WebXR/WebXRController.ts ***!
  \**************************************/

/*!***************************************!*\
  !*** ./src/Config/SettingUINumber.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/Config/SettingUIOption.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/Inputs/MouseController.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/Inputs/SpecialKeyCodes.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/Inputs/TouchController.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/Overlay/ConnectOverlay.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/WebSockets/MessageSend.ts ***!
  \***************************************/

/*!***************************************!*\
  !*** ./src/pixelstreamingfrontend.ts ***!
  \***************************************/

/*!****************************************!*\
  !*** ./src/Application/Application.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/FreezeFrame/FreezeFrame.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/UI/UIConfigurationTypes.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** ./src/VideoPlayer/VideoPlayer.ts ***!
  \****************************************/

/*!****************************************!*\
  !*** external "jss-plugin-camel-case" ***!
  \****************************************/

/*!*****************************************!*\
  !*** ./src/Inputs/GamepadController.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/Inputs/LockedMouseEvents.ts ***!
  \*****************************************/

/*!*****************************************!*\
  !*** ./src/Util/CoordinateConverter.ts ***!
  \*****************************************/

/*!******************************************!*\
  !*** ./src/Inputs/KeyboardController.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/Overlay/DisconnectOverlay.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/UI/DataChannelLatencyTest.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/Util/EventListenerTracker.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/WebSockets/MessageReceive.ts ***!
  \******************************************/

/*!******************************************!*\
  !*** ./src/pixelstreamingfrontend-ui.ts ***!
  \******************************************/

/*!*******************************************!*\
  !*** ./src/Inputs/FakeTouchController.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/Inputs/HoveringMouseEvents.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/Inputs/InputClassesFactory.ts ***!
  \*******************************************/

/*!*******************************************!*\
  !*** ./src/Inputs/XRGamepadController.ts ***!
  \*******************************************/

/*!********************************************!*\
  !*** ./src/DataChannel/InitialSettings.ts ***!
  \********************************************/

/*!*********************************************!*\
  !*** ./src/VideoPlayer/StreamController.ts ***!
  \*********************************************/

/*!**********************************************!*\
  !*** ./src/DataChannel/DataChannelSender.ts ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/PixelStreaming/PixelStreaming.ts ***!
  \**********************************************/

/*!**********************************************!*\
  !*** ./src/WebSockets/SignallingProtocol.ts ***!
  \**********************************************/

/*!***********************************************!*\
  !*** ./src/DataChannel/LatencyTestResults.ts ***!
  \***********************************************/

/*!***********************************************!*\
  !*** ./src/WebSockets/WebSocketController.ts ***!
  \***********************************************/

/*!**************************************************!*\
  !*** ./src/DataChannel/DataChannelController.ts ***!
  \**************************************************/

/*!**************************************************!*\
  !*** ./src/FreezeFrame/FreezeFrameController.ts ***!
  \**************************************************/

/*!****************************************************!*\
  !*** ./src/WebRtcPlayer/WebRtcPlayerController.ts ***!
  \****************************************************/

/*!*****************************************************!*\
  !*** ./src/PeerConnectionController/StreamStats.ts ***!
  \*****************************************************/

/*!*****************************************************!*\
  !*** ./src/UeInstanceMessage/ResponseController.ts ***!
  \*****************************************************/

/*!******************************************************!*\
  !*** ./src/PeerConnectionController/SessionStats.ts ***!
  \******************************************************/

/*!*******************************************************!*\
  !*** ./src/PeerConnectionController/CandidateStat.ts ***!
  \*******************************************************/

/*!*******************************************************!*\
  !*** ./src/Styles/PixelStreamingApplicationStyles.ts ***!
  \*******************************************************/

/*!********************************************************!*\
  !*** ./src/UeInstanceMessage/SendMessageController.ts ***!
  \********************************************************/

/*!*********************************************************!*\
  !*** ./src/PeerConnectionController/AggregatedStats.ts ***!
  \*********************************************************/

/*!*********************************************************!*\
  !*** ./src/PeerConnectionController/InboundRTPStats.ts ***!
  \*********************************************************/

/*!**********************************************************!*\
  !*** ./src/DataChannel/DataChannelLatencyTestResults.ts ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./src/PeerConnectionController/DataChannelStats.ts ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./src/PeerConnectionController/OutBoundRTPStats.ts ***!
  \**********************************************************/

/*!**********************************************************!*\
  !*** ./src/UeInstanceMessage/StreamMessageController.ts ***!
  \**********************************************************/

/*!************************************************************!*\
  !*** ./src/PeerConnectionController/CandidatePairStats.ts ***!
  \************************************************************/

/*!*************************************************************!*\
  !*** ./src/DataChannel/DataChannelLatencyTestController.ts ***!
  \*************************************************************/

/*!***************************************************************!*\
  !*** ./src/UeInstanceMessage/ToStreamerMessagesController.ts ***!
  \***************************************************************/

/*!*****************************************************************!*\
  !*** external "@epicgames-ps/lib-pixelstreamingfrontend-ue5.4" ***!
  \*****************************************************************/

/*!******************************************************************!*\
  !*** ./src/PeerConnectionController/PeerConnectionController.ts ***!
  \******************************************************************/

/**
 * A better abstraction over CSS.
 *
 * @copyright Oleg Isonen (Slobodskoi) / Isonen 2014-present
 * @website https://github.com/cssinjs/jss
 * @license MIT
 */
