/**
 * 关键词唤醒功能 - 通过"小景小景"唤醒对话
 * Keyword Wake-up Feature for Momentum Cloud
 */

(function() {
    // 日志函数，方便调试
    function log(message) {
        console.log("[关键词唤醒] " + message);
    }

    // 配置参数
    const config = {
        keyword: "你好",       // 唤醒关键词
        confidence: 0.6,      // 识别置信度阈值
        cooldownTime: 5000,   // 唤醒后冷却时间(毫秒)，防止重复触发
        autoRestart: true,    // 对话结束后是否自动重启关键词监听
        enabled: true,        // 功能开关
        silenceTimeout: 10000, // 静音超时时间(毫秒)，超过此时间自动关闭语音识别
        speechEndTimeout: 1500 // 语音结束后等待时间(毫秒)，超过此时间自动提交结果
    };

    // 状态变量
    let state = {
        listening: false,        // 是否正在监听关键词
        recognition: null,       // 语音识别对象
        lastWakeup: 0,           // 上次唤醒时间戳
        lastWakeupProcessing: 0, // 上次唤醒处理开始的时间戳
        inConversation: false,   // 是否在对话中
        resourceReleasing: false // 是否正在释放资源
    };

    // 等待DOM加载完成
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", initKeywordWakeup);
    } else {
        // 如果DOM已加载完成，直接初始化
        initKeywordWakeup();
    }

    /**
     * 初始化关键词唤醒功能
     */
    function initKeywordWakeup() {
        log("初始化关键词唤醒功能");
        
        // 创建开关控件
        createWakeupSwitch();
        
        // 从localStorage中读取之前的设置
        const savedState = localStorage.getItem('keywordWakeupEnabled');
        if (savedState !== null) {
            config.enabled = savedState === 'true';
            log("已从本地存储恢复设置: " + (config.enabled ? "开启" : "关闭"));
            
            // 更新开关状态
            const wakeupSwitch = document.getElementById('keyword-wakeup-switch');
            if (wakeupSwitch) {
                wakeupSwitch.checked = config.enabled;
            }
        }
        
        // 如果功能开启，启动关键词监听
        if (config.enabled) {
            startKeywordListening();
        }
        
        // 监听对话状态变化
        monitorConversationState();
    }
    
    /**
     * 创建关键词唤醒开关
     */
    function createWakeupSwitch() {
        try {
            // 查找Speech模块下的表单容器
            const speechSection = Array.from(document.querySelectorAll('h3.collapsible.text-white')).find(
                el => el.textContent.includes('Speech')
            );
            let container = null;
            
            if (speechSection) {
                // 找到Speech部分的内容区域
                const speechContent = speechSection.nextElementSibling;
                if (speechContent && speechContent.classList.contains('content')) {
                    // 尝试找到TTS Chat容器
                    const ttsTabContent = speechContent.querySelector('#tts-chat-container');
                    if (ttsTabContent) {
                        // 找到表单容器
                        container = ttsTabContent.querySelector('.form-group.mb-1');
                    }
                }
            }
            
            log('找到特定的容器form-group.mb-1：'+container)
            // 如果找不到特定容器，尝试查找任何表单容器
            if (!container) {
                container = document.querySelector('.form-group.mb-1');
            }
            
            // 如果仍找不到容器，创建浮动开关
            if (!container) {
                log("未找到开关容器，将创建浮动开关");
                createFloatingSwitch();
                return;
            }
            
            // 创建一个新的行来放置唤醒词开关
            const switchRow = document.createElement('div');
            switchRow.className = 'form-group mb-1';
            switchRow.style.cssText = 'display: flex; align-items: center; margin-top: 10px;';
            
            // 创建开关标签和控件
            const switchLabel = document.createElement('h5');
            switchLabel.className = 'text-white';
            switchLabel.innerHTML = '唤醒词<span style="font-size:0.8em;color:#aaa;margin-left:3px;">(你好)</span>';
            switchLabel.style.cssText = 'margin: 0; flex-grow: 1;';
            
            const switchWrapper = document.createElement('label');
            switchWrapper.className = 'tgl-switch';
            
            const switchInput = document.createElement('input');
            switchInput.type = 'checkbox';
            switchInput.id = 'keyword-wakeup-switch';
            switchInput.className = 'tgl tgl-flat';
            switchInput.checked = config.enabled;
            
            const switchSlider = document.createElement('div');
            switchSlider.className = 'tgl-slider';
            
            // 组装开关
            switchWrapper.appendChild(switchInput);
            switchWrapper.appendChild(switchSlider);
            
            // 组装行
            switchRow.appendChild(switchLabel);
            switchRow.appendChild(switchWrapper);
            
            // 添加到容器
            // 尝试添加到自动对话开关旁边
            const autoConversationLabel = Array.from(document.querySelectorAll('label')).find(
                el => el.textContent.includes('自动对话')
            );
            if (autoConversationLabel && autoConversationLabel.parentNode) {
                autoConversationLabel.parentNode.insertBefore(switchRow, autoConversationLabel.nextSibling);
            } else {
                // 如果找不到自动对话开关，直接添加到容器末尾
                container.appendChild(switchRow);
            }
            
            // 添加事件监听
            switchInput.addEventListener('change', function() {
                config.enabled = switchInput.checked;
                localStorage.setItem('keywordWakeupEnabled', config.enabled);
                log("唤醒词功能: " + (config.enabled ? "开启" : "关闭"));
                
                if (config.enabled) {
                    startKeywordListening();
                } else {
                    stopKeywordListening();
                }
            });
            
            log("已创建唤醒词开关");
        } catch (e) {
            log("创建唤醒词开关失败: " + e.toString());
            // 失败时创建浮动开关作为备选
            createFloatingSwitch();
        }
    }
    
    /**
     * 创建浮动开关（当无法找到合适的容器时）
     */
    function createFloatingSwitch() {
        try {
            // 创建浮动开关容器
            const floatingSwitch = document.createElement('div');
            floatingSwitch.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                background-color: rgba(0,0,0,0.7);
                color: white;
                padding: 10px;
                border-radius: 5px;
                display: flex;
                align-items: center;
            `;
            
            // 创建开关标签
            const switchLabel = document.createElement('label');
            switchLabel.innerHTML = '唤醒词"你好"';
            switchLabel.style.marginRight = '10px';
            
            // 创建开关
            const switchInput = document.createElement('input');
            switchInput.type = 'checkbox';
            switchInput.id = 'keyword-wakeup-switch';
            switchInput.checked = config.enabled;
            
            // 组装
            floatingSwitch.appendChild(switchLabel);
            floatingSwitch.appendChild(switchInput);
            
            // 添加到页面
            document.body.appendChild(floatingSwitch);
            
            // 添加事件监听
            switchInput.addEventListener('change', function() {
                config.enabled = switchInput.checked;
                localStorage.setItem('keywordWakeupEnabled', config.enabled);
                log("唤醒词功能: " + (config.enabled ? "开启" : "关闭"));
                
                if (config.enabled) {
                    startKeywordListening();
                } else {
                    stopKeywordListening();
                }
            });
            
            log("已创建浮动唤醒词开关");
        } catch (e) {
            log("创建浮动唤醒词开关失败: " + e.toString());
        }
    }
    
    /**
     * 启动关键词监听
     */
    function startKeywordListening() {
        if (state.listening) {
            log("已经在监听关键词，不重复启动");
            return;
        }
        
        // 检查是否正在进行语音识别或对话
        if (window.isRecognizing) {
            log("检测到语音识别正在进行，暂不启动关键词监听");
            return;
        }
        
        // 检查是否正在释放资源
        if (state.resourceReleasing) {
            log("资源正在释放中，稍后再试");
            setTimeout(startKeywordListening, 1000);
            return;
        }
        
        if (!('webkitSpeechRecognition' in window)) {
            log("浏览器不支持语音识别功能，请使用Chrome浏览器");
            alert("您的浏览器不支持语音唤醒功能，请使用Chrome浏览器");
            return;
        }
        
        try {
            // 创建语音识别对象
            state.recognition = new webkitSpeechRecognition();
            state.recognition.continuous = true;  // 连续识别
            state.recognition.interimResults = true;  // 中间结果
            state.recognition.lang = 'cmn-Hans-CN';  // 设置为中文
            
            // 开始事件
            state.recognition.onstart = function() {
                state.listening = true;
                log('关键词监听已启动，请说"你好"唤醒对话');
            };
            
            // 结束事件
            state.recognition.onend = function() {
                state.listening = false;
                log("关键词监听已结束");
                
                // 如果功能仍然开启，且不在对话中，则自动重启
                if (config.enabled && config.autoRestart && !state.inConversation) {
                    log("自动重启关键词监听");
                    setTimeout(startKeywordListening, 500);
                }
            };
            
            // 错误事件
            state.recognition.onerror = function(event) {
                log("关键词监听出错: " + event.error);

                // 如果是不允许访问麦克风的错误，提示用户
                if (event.error === 'not-allowed') {
                    showMicrophonePermissionDialog();
                }
                // 针对aborted错误的特殊处理
                else if (event.error === 'aborted') {
                    log("语音识别被中止，可能是由于资源冲突或浏览器问题");
                    
                    // 清理可能的资源残留
                    if (state.recognition) {
                        try {
                            state.recognition.abort();
                            state.recognition = null;
                            log("已在aborted错误后清理识别资源");
                        } catch (e) {
                            // 忽略可能的错误
                        }
                    }
                    
                    // 检查是否处于唤醒处理阶段或对话中
                    const now = Date.now();
                    const inWakeupProcess = state.lastWakeupProcessing && (now - state.lastWakeupProcessing < 10000);
                    
                    if (inWakeupProcess || state.inConversation || window.isRecognizing) {
                        log("检测到处于唤醒处理或对话中，不立即重启关键词监听");
                        // 延迟检查是否可以重启，给系统恢复时间
                        setTimeout(function() {
                            if (config.enabled && config.autoRestart && !state.inConversation && !window.isRecognizing) {
                                log("唤醒处理结束，现在重启关键词监听");
                                startKeywordListening();
                            }
                        }, 8000); // 更长的延迟，确保系统完全恢复
                    } else {
                        // 如果不在唤醒处理中，使用更长的延迟时间
                        if (config.enabled && config.autoRestart) {
                        log("尝试在较长延迟后恢复关键词监听");
                        setTimeout(startKeywordListening, 5000);
                        }
                    }
                    return;
                }
                
                // 其他错误的自动重启
                if (config.enabled && config.autoRestart && !state.inConversation && !window.isRecognizing) {
                    log("尝试恢复关键词监听");
                    setTimeout(startKeywordListening, 3000);
                }
            };
            
            // 结果事件 - 检测关键词
            state.recognition.onresult = function(event) {
                let transcript = '';
                
                // 获取识别结果
                for (let i = event.resultIndex; i < event.results.length; ++i) {
                    transcript += event.results[i][0].transcript;
                }
                
                // 转换为小写并去除空格进行匹配
                const normalizedTranscript = transcript.trim().toLowerCase();
                
                // 检查是否包含唤醒词
                if (normalizedTranscript.includes(config.keyword.toLowerCase())) {
                    const now = Date.now();
                    
                    // 检查是否在冷却时间内
                    if (now - state.lastWakeup > config.cooldownTime) {
                        log("检测到唤醒词: " + normalizedTranscript);
                        state.lastWakeup = now;
                        
                        // 停止关键词监听
                        stopKeywordListening();
                        
                        // 触发唤醒动作
                        triggerWakeupAction();
                    } else {
                        log("唤醒词冷却中，忽略本次唤醒");
                    }
                }
            };
            
            // 启动识别
            state.recognition.start();
            log("开始监听唤醒词");
        } catch (e) {
            log("启动关键词监听失败: " + e.toString());
        }
    }
    
    /**
     * 停止关键词监听
     */
    function stopKeywordListening() {
        if (!state.listening || !state.recognition) {
            return;
        }
        
        // 标记为正在释放资源
        state.resourceReleasing = true;
        
        try {
            // 停止识别
            state.recognition.stop();
            log("关键词监听已停止");
            
            // 确保资源完全释放
            setTimeout(function() {
                if (state.recognition) {
                    try {
                        // 额外尝试中止识别，确保资源释放
                        state.recognition.abort();
                        log("已强制释放语音识别资源");
                    } catch (err) {
                        // 忽略可能的错误
                    }
                    // 清除引用
                    state.recognition = null;
                }
                
                // 资源释放完成
                state.resourceReleasing = false;
            }, 500); // 增加延迟，确保资源完全释放
        } catch (e) {
            log("停止关键词监听失败: " + e.toString());
            
            // 出错时也尝试确保资源完全释放
            try {
                if (state.recognition) {
                    state.recognition.abort();
                    state.recognition = null;
                }
            } catch (err) {
                // 忽略可能的错误
            }
            
            // 即使出错也要标记资源释放完成
            setTimeout(function() {
                state.resourceReleasing = false;
            }, 500);
        }
        
        // 确保状态一致
        state.listening = false;
    }
    
    /**
     * 触发唤醒动作
     */
    function triggerWakeupAction() {
        log("执行唤醒动作");
        
        // 设置状态标记，防止重复触发
        state.inConversation = true;
        
        // 记录唤醒时间戳，用于防止重复触发
        state.lastWakeupProcessing = Date.now();
        
        // 确保释放所有现有语音识别资源
        cleanupAllSpeechRecognitionResources();
        
        try {
            // 播放唤醒提示音
            playWakeupSound().then(() => {
                // 语音合成完成后再触发麦克风
                log("语音回应完成，现在激活麦克风");
                activateMicrophone();
                
                // 在麦克风激活一段时间后执行最终清理
                schedulePostActivationCleanup();
            }).catch(e => {
                log("语音合成出错，直接激活麦克风: " + e);
                activateMicrophone();
                
                // 同样执行最终清理
                schedulePostActivationCleanup();
            });
        } catch (e) {
            log("执行唤醒动作失败: " + e.toString());
            
            // 等待足够长时间后再恢复关键词监听
            setTimeout(function() {
                // 只有在确认对话未开始的情况下才恢复监听
                if (!window.isRecognizing) {
                    log("对话未能成功开始，尝试恢复关键词监听");
            state.inConversation = false;
            startKeywordListening();
                }
            }, 3000); // 延迟3秒，给系统足够时间恢复
        }
    }
    
    /**
     * 清理所有语音识别资源
     */
    function cleanupAllSpeechRecognitionResources() {
        log("清理所有语音识别资源");
        
        // 优先设置全局标志，表示没有任何语音识别在运行
        window.isRecognizing = false;
        
        // 清理关键词监听的识别实例
        if (state.recognition) {
            try {
                state.recognition.abort();
                state.recognition = null;
                log("已清理关键词监听识别实例");
            } catch (err) {
                // 忽略可能的错误
            }
        }
        
        // 清理全局语音识别实例
        if (window.globalSpeechRecognition) {
            try {
                window.globalSpeechRecognition.abort();
                log("已清理全局语音识别实例");
            } catch (err) {
                // 忽略可能的错误
            }
        }
        
        // 清理独立语音识别实例
        if (window.standaloneRecognition) {
            try {
                window.standaloneRecognition.abort();
                window.standaloneRecognition = null;
                log("已清理独立语音识别实例");
            } catch (err) {
                // 忽略可能的错误
            }
        }
        
        // 尝试清理应用实例中的语音识别
        if (window.MomentumCloudApplication && window.app) {
            try {
                // 尝试停止可能正在运行的录音/识别
                if (typeof window.app.stopRecording === 'function') {
                    window.app.stopRecording();
                }
                
                if (window.app.speechRecognition) {
                    if (typeof window.app.speechRecognition.abort === 'function') {
                        window.app.speechRecognition.abort();
                        log("已清理应用实例中的语音识别");
                    }
                }
            } catch (err) {
                // 忽略可能的错误
            }
        }
        
        // 重置UI元素状态
        try {
            const micButton = document.getElementById('tts-chat-mic-button');
            if (micButton) {
                if (micButton.style) {
                    micButton.style.backgroundImage = 'url("/images/mic.gif")';
                }
                micButton.classList.remove('active');
            }
        } catch (err) {
            // 忽略可能的错误
        }
    }
    
    /**
     * 安排麦克风激活后的清理工作
     */
    function schedulePostActivationCleanup() {
        // 在麦克风激活一段时间后执行最终清理
        setTimeout(function() {
            log("执行麦克风激活后的最终清理");
            
            // 确保全局识别状态正确
            if (!window.isRecognizing) {
                log("检测到语音识别未成功启动，重置状态");
                state.inConversation = false;
                
                // 清除任何可能的残留资源
                cleanupAllSpeechRecognitionResources();
                
                // 如果自动对话功能关闭，检查是否需要恢复关键词监听
                const autoConvSwitch = document.getElementById('auto-conversation-switch');
                const isAutoConvEnabled = autoConvSwitch && autoConvSwitch.checked;
                
                if (!isAutoConvEnabled && config.enabled) {
                    log("自动对话已关闭，尝试恢复关键词监听");
                    startKeywordListening();
                }
            } else {
                log("语音识别已成功启动，保持对话状态");
            }
        }, 3000); // 延迟3秒，给麦克风激活足够时间
    }
    
    /**
     * 独立激活语音识别（专门用于自动对话功能关闭的情况）
     * 不依赖auto-conversation.js中的功能
     */
    function activateStandaloneSpeechRecognition() {
        log("尝试独立激活语音识别（不使用自动对话功能）");
        
        try {
            // 确保之前的识别已完全停止
            cleanupAllSpeechRecognitionResources();
            
            // 创建新的语音识别实例
            if ('webkitSpeechRecognition' in window) {
                // 创建新的语音识别实例
                const recognition = new webkitSpeechRecognition();
                
                // 配置语音识别参数
                recognition.continuous = false;     // 单次识别，减少冲突
                recognition.interimResults = true;
                recognition.maxAlternatives = 1;    // 只返回最可能的结果
                recognition.lang = 'cmn-Hans-CN';  // 中文
                
                // 静音检测相关变量
                const silenceTracker = {
                    lastSpeechTime: Date.now(),   // 上次检测到语音的时间
                    silenceTimer: null,           // 静音定时器
                    resultTimer: null,            // 结果提交定时器
                    hasContent: false,            // 是否有语音内容
                    lastText: ""                  // 上次识别的文本内容
                };
                
                // 查找文本输入区域
                const textArea = document.getElementById('tts-chat-area');
                
                // 开始事件
                recognition.onstart = function() {
                    log("独立语音识别已开始");
                    window.isRecognizing = true;
                    
                    // 更新麦克风按钮外观（如果有）
                    const micButton = document.getElementById('tts-chat-mic-button');
                    if (micButton) {
                        if (micButton.style) {
                            micButton.style.backgroundImage = 'url("/images/mic-anim.gif")';
                        }
                        // 添加活跃类名
                        micButton.classList.add('active');
                    }
                    
                    // 启动静音检测
                    startSilenceDetection();
                };
                
                // 结果事件
                recognition.onresult = function(event) {
                    let transcript = '';
                    let isFinal = false;
                    
                    // 获取识别结果
                    for (let i = event.resultIndex; i < event.results.length; ++i) {
                        const result = event.results[i];
                        transcript += result[0].transcript;
                        
                        // 检查是否有最终结果
                        if (result.isFinal) {
                            isFinal = true;
                        }
                    }
                    
                    // 更新文本区域（如果有）
                    if (textArea) {
                        textArea.value = transcript;
                    }
                    
                    // 记录识别到的文本
                    silenceTracker.lastText = transcript;
                    
                    // 如果有内容，记录时间并重置静音计时器
                    if (transcript.trim().length > 0) {
                        silenceTracker.hasContent = true;
                        silenceTracker.lastSpeechTime = Date.now();
                        
                        // 重置静音计时器
                        resetSilenceTimer();
                        
                        // 如果是最终结果，可以考虑提前提交
                        if (isFinal) {
                            // 清除之前的结果定时器
                            if (silenceTracker.resultTimer) {
                                clearTimeout(silenceTracker.resultTimer);
                            }
                            
                            // 设置新的结果定时器，在短时间后自动提交
                            silenceTracker.resultTimer = setTimeout(function() {
                                log("检测到最终结果，准备提交");
                                submitSpeechResult();
                            }, config.speechEndTimeout);
                        }
                    }
                    
                    log("语音识别结果: " + transcript + (isFinal ? " (最终结果)" : ""));
                };
                
                // 结束事件
                recognition.onend = function() {
                    log("独立语音识别已结束");
                    window.isRecognizing = false;
                    
                    // 清除所有定时器
                    if (silenceTracker.silenceTimer) {
                        clearTimeout(silenceTracker.silenceTimer);
                        silenceTracker.silenceTimer = null;
                    }
                    
                    if (silenceTracker.resultTimer) {
                        clearTimeout(silenceTracker.resultTimer);
                        silenceTracker.resultTimer = null;
                    }
                    
                    // 更新麦克风按钮外观
                    const micButton = document.getElementById('tts-chat-mic-button');
                    if (micButton) {
                        if (micButton.style) {
                            micButton.style.backgroundImage = 'url("/images/mic.gif")';
                        }
                        // 移除活跃类名
                        micButton.classList.remove('active');
                    }
                    
                    // 如果有输入内容，自动点击提交按钮
                    if (textArea && textArea.value.trim()) {
                        const submitButton = document.getElementById('tts-chat-submit-button');
                        if (submitButton) {
                            log("自动点击提交按钮");
                            submitButton.click();
                        }
                    }
                    
                    // 恢复关键词监听（如果启用）
                    if (config.enabled && config.autoRestart && !state.inConversation) {
                        log("语音识别结束，恢复关键词监听");
                        setTimeout(startKeywordListening, 1000);
                    }
                };
                
                // 错误事件
                recognition.onerror = function(event) {
                    log("独立语音识别出错: " + event.error);
                    window.isRecognizing = false;
                    
                    // 如果是不允许访问麦克风的错误，提示用户
                    if (event.error === 'not-allowed') {
                        alert("请允许访问麦克风，否则无法使用语音识别功能");
                    }
                    
                    // 恢复关键词监听（如果启用）
                    if (config.enabled && config.autoRestart && !state.inConversation) {
                        log("语音识别出错，延迟后恢复关键词监听");
                        setTimeout(startKeywordListening, 3000);
                    }
                };
                
                // 添加错误防范：防止语音识别在启动前被潜在的错误中断
                try {
                    // 确保同时只有一个识别实例在运行
                    if (window.standaloneRecognition) {
                        try {
                            window.standaloneRecognition.abort();
                        } catch (err) {
                            // 忽略可能的错误
                        }
                    }
                    
                    // 保存引用到全局变量，方便后续管理
                    window.standaloneRecognition = recognition;
                    
                    // 启动识别
                    recognition.start();
                    log("独立语音识别已启动");
                    
                    // 启动成功监控：如果启动后短时间内发生错误，自动恢复
                    setTimeout(function() {
                        if (!window.isRecognizing) {
                            log("独立语音识别没有成功启动，尝试恢复关键词监听");
                            state.inConversation = false;
                            startKeywordListening();
                        }
                    }, 2000);
                    
                    // 启动静音检测
                    startSilenceDetection();
                    
                    return true;
                } catch (err) {
                    log("启动独立语音识别失败: " + err.toString());
                    return false;
                }
            } else {
                log("浏览器不支持语音识别功能，无法独立激活");
                return false;
            }
        } catch (e) {
            log("独立激活语音识别失败: " + e.toString());
            return false;
        }
    }
    
    /**
     * 激活麦克风
     */
    function activateMicrophone() {
        // 检查全局状态，防止在不适当的时候激活
        if (window.isRecognizing) {
            log("检测到语音识别正在进行，不激活麦克风");
            return;
        }
        
        // 检查自动对话开关状态
        const autoConvSwitch = document.getElementById('auto-conversation-switch');
        const isAutoConvEnabled = autoConvSwitch && autoConvSwitch.checked;
        
        log("自动对话功能状态: " + (isAutoConvEnabled ? "开启" : "关闭"));
        
        setTimeout(function() {
            try {
                // 清理可能存在的语音识别资源
                if (window.globalSpeechRecognition) {
                    try {
                        window.globalSpeechRecognition.abort();
                    } catch (e) {
                        // 忽略可能的错误
                    }
                }
                
                // 查找麦克风按钮
            const micButton = document.getElementById('tts-chat-mic-button');
                
            if (micButton) {
                log("自动点击麦克风按钮");
                    
                    // 为避免点击冲突，我们使用不同的策略激活麦克风
                    if (isAutoConvEnabled) {
                        // 如果自动对话开启，使用自动对话脚本中的函数
                        if (window.startSpeechRecognitionDirectly) {
                            log("通过自动对话脚本启动语音识别");
                            window.startSpeechRecognitionDirectly();
                        } else {
                            // 自动对话脚本中的函数不可用，直接点击按钮
                            log("自动对话函数不可用，直接点击麦克风按钮");
                micButton.click();
                        }
                    } else {
                        // 如果自动对话关闭，尝试使用独立的语音识别
                        log("自动对话已关闭，尝试使用独立语音识别");
                        
                        // 先尝试独立激活语音识别
                        if (!activateStandaloneSpeechRecognition()) {
                            // 如果独立激活失败，再尝试直接点击按钮
                            log("独立语音识别失败，尝试直接点击麦克风按钮");
                            
                            // 确保按钮可点击
                            if (micButton.disabled) {
                                micButton.disabled = false;
                            }
                            
                            // 尝试不同的点击方法
                            try {
                                // 原生点击
                                micButton.click();
                                
                                // 如果原生点击可能不生效，尝试模拟事件
                                const clickEvent = new MouseEvent('click', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                });
                                setTimeout(() => micButton.dispatchEvent(clickEvent), 100);
                            } catch (err) {
                                log("点击按钮失败: " + err);
                            }
                        }
                    }
            } else {
                log("未找到麦克风按钮");
                
                    // 尝试其他方法启动语音识别
                    if (isAutoConvEnabled && window.startSpeechRecognitionDirectly) {
                    log("通过自动对话脚本启动语音识别");
                    window.startSpeechRecognitionDirectly();
                    } else if (!isAutoConvEnabled) {
                        // 自动对话关闭，尝试独立语音识别
                        activateStandaloneSpeechRecognition();
                    } else if (window.MomentumCloudApplication && window.app) {
                        // 最后尝试使用应用实例
                        log("尝试使用应用实例启动语音识别");
                        try {
                            if (typeof window.app.startRecording === 'function') {
                                window.app.startRecording();
                            } else if (typeof window.app.startSpeechRecognition === 'function') {
                                window.app.startSpeechRecognition();
                            }
                        } catch (err) {
                            log("使用应用实例启动识别失败: " + err);
                        }
                    }
                }
            } catch (e) {
                log("激活麦克风时出错: " + e.toString());
            }
        }, 1800); // 进一步增加延迟至1800毫秒，确保语音识别资源充分释放
    }
    
    /**
     * 播放唤醒提示音
     */
    function playWakeupSound() {
        return new Promise((resolve, reject) => {
            try {
                // 使用语音合成播放"我在"
                if ('speechSynthesis' in window) {
                    log("使用语音合成播放'我在'");
                    
                    // 创建语音合成对象
                    const utterance = new SpeechSynthesisUtterance('我在');
                    
                    // 设置语音参数
                    utterance.lang = 'zh-CN'; // 中文
                    utterance.volume = 1.0;   // 音量
                    utterance.rate = 1.0;     // 语速
                    utterance.pitch = 1.2;    // 音调略高，听起来更活泼
                    
                    // 添加结束事件，用于通知语音播放完成
                    utterance.onend = function() {
                        log("语音合成播放完成");
                        resolve();
                    };
                    
                    // 添加错误事件
                    utterance.onerror = function(event) {
                        log("语音合成错误: " + event.error);
                        reject(event.error);
                    };
                    
                    // 获取可用的声音列表并选择合适的声音
                    let voices = window.speechSynthesis.getVoices();
                    
                    // 在某些浏览器中，getVoices()可能是异步的，声音列表可能为空
                    if (voices.length === 0) {
                        // 如果列表为空，添加一个事件监听器来等待声音列表加载完成
                        window.speechSynthesis.addEventListener('voiceschanged', function() {
                            voices = window.speechSynthesis.getVoices();
                            setVoiceAndSpeak(utterance, voices);
                        });
                        
                        // 同时播放短提示音，确保有立即反馈
                        playShortTone();
                        
                        // 由于某些浏览器可能不会触发voiceschanged事件，设置一个超时
                        setTimeout(() => resolve(), 1000);
                    } else {
                        // 如果声音列表已加载，直接设置声音并播放
                        setVoiceAndSpeak(utterance, voices);
                        
                        // 同时播放短提示音，提供立即反馈
                        playShortTone();
                        
                        // 某些浏览器可能不会正确触发onend事件，设置一个超时作为备份
                        setTimeout(() => resolve(), 1500);
                    }
                    
                    return; // 如果语音合成可用，就不再执行后面的代码
                } else {
                    log("语音合成不可用，使用提示音代替");
                    // 如果语音合成不可用，降级为使用提示音
                    playShortTone();
                    // 短暂延迟后解析Promise
                    setTimeout(() => resolve(), 300);
                }
            } catch (e) {
                log("播放唤醒提示音失败: " + e.toString());
                // 出错时尝试播放基本提示音
                playShortTone();
                // 出错时立即解析Promise
                resolve();
            }
        });
    }
    
    /**
     * 设置语音合成的声音并播放
     */
    function setVoiceAndSpeak(utterance, voices) {
        try {
            // 尝试找到中文语音
            const chineseVoices = voices.filter(voice => voice.lang.includes('zh'));
            if (chineseVoices.length > 0) {
                // 优先选择女声，如果没有特定标识，就选第一个中文声音
                const femaleVoice = chineseVoices.find(voice => 
                    voice.name.toLowerCase().includes('female') || 
                    voice.name.toLowerCase().includes('女') ||
                    voice.name.toLowerCase().includes('xiaoyan')
                );
                utterance.voice = femaleVoice || chineseVoices[0];
            }
            
            // 播放语音
            window.speechSynthesis.speak(utterance);
            log("正在播放'我在'语音");
        } catch (e) {
            log("设置语音声音失败: " + e.toString());
        }
    }
    
    /**
     * 播放简短提示音（原来的提示音功能）
     */
    function playShortTone() {
        try {
            // 创建音频上下文
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // 创建音调
            const oscillator = audioContext.createOscillator();
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(880, audioContext.currentTime); // A5
            
            // 创建音量控制
            const gainNode = audioContext.createGain();
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + 0.05);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.3);
            
            // 连接节点
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            // 播放提示音
            oscillator.start();
            oscillator.stop(audioContext.currentTime + 0.3);
            
            log("播放简短提示音");
        } catch (e) {
            log("播放简短提示音失败: " + e.toString());
        }
    }
    
    /**
     * 监控对话状态变化
     */
    function monitorConversationState() {
        // 监听语音识别状态变化
        setInterval(function() {
            // 检查全局识别状态
            const isRecognizing = window.isRecognizing || false;
            
            // 如果从对话状态变为非对话状态，重启关键词监听
            if (state.inConversation && !isRecognizing) {
                log("检测到对话结束");
                
                // 确保不在资源释放过程中
                if (state.resourceReleasing) {
                    log("资源正在释放中，稍后再检查对话状态");
                    return;
                }
                
                state.inConversation = false;
                
                // 延迟重启关键词监听，等待可能的回复和资源完全释放
                if (config.enabled && config.autoRestart) {
                    setTimeout(function() {
                        // 再次检查状态，确保可以安全启动
                        if (!window.isRecognizing && !state.listening && !state.resourceReleasing) {
                            log("对话已结束，资源已释放，重启关键词监听");
                            startKeywordListening();
                        } else {
                            log("对话状态或资源状态不适合重启监听: 识别中=" + window.isRecognizing + 
                                ", 正在监听=" + state.listening + ", 资源释放中=" + state.resourceReleasing);
                        }
                    }, 3000); // 增加延迟，确保资源完全释放
                }
            }
        }, 1000);
        
        // 监听音频播放结束事件
        document.addEventListener('audio-play-end', function() {
            log("检测到音频播放结束");
            
            // 如果在对话中且功能开启，延迟一段时间后重启关键词监听
            if (state.inConversation && config.enabled && config.autoRestart) {
                setTimeout(function() {
                    // 检查状态，确保可以安全启动
                    if (!window.isRecognizing && !state.resourceReleasing) {
                        log("回复已结束，资源已释放，重启关键词监听");
                        state.inConversation = false;
                        startKeywordListening();
                    } else {
                        log("不适合重启监听: 识别中=" + window.isRecognizing + ", 资源释放中=" + state.resourceReleasing);
                        // 如果条件不满足，再次延迟检查
                        setTimeout(function() {
                            if (!window.isRecognizing && !state.resourceReleasing) {
                                log("延迟检查后重启关键词监听");
                        state.inConversation = false;
                        startKeywordListening();
                            }
                        }, 2000);
                    }
                }, 3000);
            }
        });
    }

    // 显示麦克风权限请求对话框
    function showMicrophonePermissionDialog() {
        // 创建模态对话框
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        const dialog = document.createElement('div');
        dialog.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 10px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        `;

        dialog.innerHTML = `
            <h3 style="color: #333; margin-bottom: 20px;">🎤 需要麦克风权限</h3>
            <p style="color: #666; margin-bottom: 20px; line-height: 1.5;">
                为了使用语音唤醒和对话功能，需要访问您的麦克风。<br>
                请点击浏览器地址栏左侧的 🔒 图标，然后允许麦克风权限。
            </p>
            <div style="display: flex; gap: 10px; justify-content: center;">
                <button id="mic-permission-retry" style="
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                ">重试</button>
                <button id="mic-permission-close" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                ">关闭</button>
            </div>
        `;

        modal.appendChild(dialog);
        document.body.appendChild(modal);

        // 绑定事件
        document.getElementById('mic-permission-retry').onclick = function() {
            document.body.removeChild(modal);
            // 重新尝试请求权限
            requestMicrophonePermission();
        };

        document.getElementById('mic-permission-close').onclick = function() {
            document.body.removeChild(modal);
        };

        // 点击背景关闭
        modal.onclick = function(e) {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        };
    }

    // 请求麦克风权限
    function requestMicrophonePermission() {
        if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({ audio: true })
                .then(function(stream) {
                    log("麦克风权限已获得");
                    // 停止流，我们只是为了获取权限
                    stream.getTracks().forEach(track => track.stop());
                    // 重新启动关键词监听
                    setTimeout(startKeywordListening, 500);
                })
                .catch(function(error) {
                    log("获取麦克风权限失败: " + error.message);
                    if (error.name === 'NotAllowedError') {
                        showMicrophonePermissionDialog();
                    }
                });
        } else {
            log("浏览器不支持 getUserMedia");
        }
    }

    // 暴露公共方法
    window.keywordWakeup = {
        start: startKeywordListening,
        stop: stopKeywordListening,
        setEnabled: function(enabled) {
            config.enabled = enabled;
            localStorage.setItem('keywordWakeupEnabled', enabled);

            const wakeupSwitch = document.getElementById('keyword-wakeup-switch');
            if (wakeupSwitch) {
                wakeupSwitch.checked = enabled;
            }

            if (enabled) {
                startKeywordListening();
            } else {
                stopKeywordListening();
            }
        },
        isEnabled: function() {
            return config.enabled;
        },
        setKeyword: function(keyword) {
            config.keyword = keyword;
            log("唤醒词已更改为: " + keyword);
        },
        requestPermission: requestMicrophonePermission,
        setSilenceDetectionEnabled: function(enabled) {
            config.silenceDetectionEnabled = enabled;
            log("沉默检测已" + (enabled ? "启用" : "禁用"));
        },
        setSilenceTimeout: function(timeout) {
            config.silenceTimeout = timeout;
            log("沉默超时设置为: " + timeout + "ms");
        },
        setSpeechEndTimeout: function(timeout) {
            config.speechEndTimeout = timeout;
            log("语音结束超时设置为: " + timeout + "ms");
        }
    };

    // 立即在控制台显示一条消息，表明脚本已加载
    log("唤醒词脚本已加载 v1.0");

                    /**
                 * 开始静音检测
                 */
                function startSilenceDetection() {
                    log("启动静音检测，超时时间: " + config.silenceTimeout + "ms");
                    
                    // 初始化状态
                    silenceTracker.lastSpeechTime = Date.now();
                    silenceTracker.hasContent = false;
                    
                    // 设置静音检测定时器
                    resetSilenceTimer();
                }
                
                /**
                 * 重置静音计时器
                 */
                function resetSilenceTimer() {
                    // 清除现有定时器
                    if (silenceTracker.silenceTimer) {
                        clearTimeout(silenceTracker.silenceTimer);
                    }
                    
                    // 设置新的定时器
                    silenceTracker.silenceTimer = setTimeout(function() {
                        // 检查上次检测到语音的时间是否超过阈值
                        const now = Date.now();
                        const elapsed = now - silenceTracker.lastSpeechTime;
                        
                        if (elapsed >= config.silenceTimeout) {
                            log("检测到静音超时 (" + elapsed + "ms)，准备停止语音识别");
                            
                            // 如果有内容，先提交，否则直接停止
                            if (silenceTracker.hasContent && silenceTracker.lastText.trim().length > 0) {
                                submitSpeechResult();
                            } else {
                                stopAndRestartListening();
                            }
                        } else {
                            // 如果还没超时，继续检测
                            resetSilenceTimer();
                        }
                    }, Math.min(1000, config.silenceTimeout / 2)); // 每1秒或一半超时时间检测一次
                }
                
                /**
                 * 提交语音识别结果
                 */
                function submitSpeechResult() {
                    log("提交语音识别结果");
                    
                    // 停止识别
                    try {
                        if (window.standaloneRecognition) {
                            window.standaloneRecognition.stop();
                        }
                    } catch (err) {
                        log("停止识别失败: " + err);
                    }
                    
                    // 点击提交按钮
                    setTimeout(function() {
                        const submitButton = document.getElementById('tts-chat-submit-button');
                        if (submitButton && silenceTracker.lastText.trim().length > 0) {
                            log("自动点击提交按钮");
                            submitButton.click();
                        } else {
                            log("无法找到提交按钮或没有内容可提交");
                            stopAndRestartListening();
                        }
                    }, 100);
                }
                
                /**
                 * 停止语音识别并重启关键词监听
                 */
                function stopAndRestartListening() {
                    log("停止语音识别并准备重启关键词监听");
                    
                    // 停止识别
                    try {
                        if (window.standaloneRecognition) {
                            window.standaloneRecognition.stop();
                        }
                    } catch (err) {
                        // 忽略可能的错误
                    }
        
        // 重置标志
        window.isRecognizing = false;
        
        // 更新麦克风按钮外观
        const micButton = document.getElementById('tts-chat-mic-button');
        if (micButton) {
            if (micButton.style) {
                micButton.style.backgroundImage = 'url("/images/mic.gif")';
            }
            micButton.classList.remove('active');
        }
        
        // 延迟后重启关键词监听
        setTimeout(function() {
            log("静音超时后，重启关键词监听");
            state.inConversation = false;
            startKeywordListening();
        }, 500);
    }
})(); 