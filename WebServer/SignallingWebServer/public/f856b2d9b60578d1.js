/**
 * Auto Conversation Feature for Momentum Cloud
 * Automatically activates microphone after chatbot finishes speaking
 * 自动对话功能 - 在机器人回复结束后自动开启麦克风
 */

(function() {
    // 日志函数，方便调试
    function log(message) {
        console.log("[自动对话] " + message);
    }

    // 向页面注入直接捕获脚本
    injectDirectCaptureScript();
    
    // 存储语音识别对象的引用
    let globalSpeechRecognition = null;
    
    // 添加跟踪语音识别的变量
    let lastSpeechDetectedTime = 0;
    let speechTimeoutId = null;
    const SPEECH_TIMEOUT_MS = 4000; // 4秒无新识别文字则视为说话完成
    
    // 添加长时间无语音检测变量
    let longSilenceTimeoutId = null;
    const LONG_SILENCE_TIMEOUT_MS = 10000; // 10秒无语音活动则进入沉默状态
    let lastSpeechActivityTime = 0; // 上次语音活动时间
    
    // 添加TTS播放状态标志
    let isTTSPlaying = false;
    
    // 全局暴露启动语音识别的函数
    window.startSpeechRecognitionDirectly = function() {
        log("直接启动语音识别");
        // 如果TTS正在播放，不启动语音识别
        if (isTTSPlaying) {
            log("TTS正在播放中，不启动语音识别");
            return;
        }
        
        // 如果处于沉默模式，不启动语音识别
        if (window.autoConversationState.inSilentMode) {
            log("处于沉默模式，不启动语音识别");
            return;
        }
        
        triggerMicrophoneAfterDelay(100); // 使用更短的延迟
    };
    
    // 添加全局状态跟踪
    window.autoConversationState = {
        lastAudioPlayEnd: 0,
        micActivationAttempts: 0,
        isProcessingAudio: false,
        speechRecognitionInstance: null,
        isTTSPlaying: false,
        inSilentMode: false, // 是否处于沉默模式（需要唤醒）
        lastRecognitionStopTime: 0, // 上次语音识别停止的时间戳（仅作参考）
        lastSpeechActivityTime: Date.now() // 上次语音活动时间（包括识别结果和TTS播放）
    };

    // 等待DOM加载完成
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", initAutoConversation);
    } else {
        // 如果DOM已加载完成，直接初始化
        initAutoConversation();
    }

    // 在initAutoConversation函数之前添加停止语音识别的函数
    /**
     * 停止当前正在进行的语音识别
     */
    function stopSpeechRecognition() {
        log("尝试停止所有语音识别");
        
        let recognitionStopped = false;
        
        // 尝试所有可能的方式停止语音识别，不依赖isRecognizing标志
        
        // 1. 尝试通过全局语音识别对象停止
        if (window.globalSpeechRecognition) {
            try {
                log("通过全局对象强制停止语音识别");
                window.globalSpeechRecognition.abort();
                window.isRecognizing = false;
                recognitionStopped = true;
                log("已发送停止命令到全局对象");
            } catch (e) {
                log("停止全局对象的语音识别失败: " + e.toString());
            }
        }
        
        // 2. 尝试通过应用实例停止
        if (window.app) {
            try {
                log("尝试通过应用实例停止语音识别");
                // 直接获取应用的语音识别实例
                if (window.app.speechRecognition) {
                    window.app.speechRecognition.abort();
                    window.app.isRecognizing = false;
                    recognitionStopped = true;
                    log("已发送停止命令到应用实例");
                }
            } catch (e) {
                log("停止应用实例的语音识别失败: " + e.toString());
            }
        }
        
        // 3. 尝试通过window.speechRecognition停止
        if (window.speechRecognition) {
            try {
                log("通过window.speechRecognition强制停止语音识别");
                window.speechRecognition.abort();
                recognitionStopped = true;
                log("已发送停止命令到window.speechRecognition");
            } catch (e) {
                log("停止window.speechRecognition的语音识别失败: " + e.toString());
            }
        }
        
        // 注意：移除了通过UI停止语音识别的代码，因为它会同时停止TTS音频播放
        
        // 4. 尝试获取浏览器中所有正在运行的语音识别实例并停止它们
        try {
            // 尝试找到可能在document或window上挂载的其他语音识别实例
            for (const key in window) {
                if (key.toLowerCase().includes('recognition') && typeof window[key] === 'object' && window[key] !== null) {
                    try {
                        if (typeof window[key].abort === 'function') {
                            log("找到并尝试停止其他语音识别实例: " + key);
                            window[key].abort();
                            recognitionStopped = true;
                        } else if (typeof window[key].stop === 'function') {
                            log("找到并尝试停止其他语音识别实例: " + key);
                            window[key].stop();
                            recognitionStopped = true;
                        }
                    } catch (innerError) {
                        log("停止其他语音识别实例失败: " + key + " - " + innerError.toString());
                    }
                }
            }
        } catch (e) {
            log("查找和停止其他语音识别实例失败: " + e.toString());
        }
        
        // 无论如何，重置所有可能的状态标志
        window.isRecognizing = false;
        if (window.app) {
            window.app.isRecognizing = false;
        }
        
        if (recognitionStopped) {
            log("已尝试多种方式停止语音识别");
        } else {
            log("警告: 未能找到任何可停止的语音识别实例，尝试更强力的方法");
            // 如果上面的方法都失败，尝试最后的手段
            try {
                // 创建一个新的语音识别实例并立即停止它，可能会触发底层浏览器API清理
                if ('webkitSpeechRecognition' in window) {
                    log("创建新实例并立即停止作为最后手段");
                    const tempRecognition = new window.webkitSpeechRecognition();
                    tempRecognition.abort();
                }
            } catch (e) {
                log("最后手段失败: " + e.toString());
            }
        }
        
        // 强制同步窗口状态
        syncRecognitionState(false);
        
        return recognitionStopped;
    }
    
    /**
     * 同步语音识别状态
     * @param {boolean} isActive 是否处于活跃状态
     */
    function syncRecognitionState(isActive) {
        log("同步语音识别状态: " + (isActive ? "活跃" : "非活跃"));
        window.isRecognizing = isActive;
        
        // 同步应用实例状态
        if (window.app && typeof window.app === 'object') {
            window.app.isRecognizing = isActive;
        }
        
        // 更新UI状态
        try {
            const micButton = document.getElementById('tts-chat-mic-button');
            if (micButton) {
                if (isActive) {
                    micButton.style.backgroundImage = 'url("/images/mic-anim.gif")';
                } else {
                    micButton.style.backgroundImage = 'url("/images/mic.gif")';
                }
            }
        } catch (e) {
            log("更新麦克风按钮UI失败: " + e.toString());
        }
    }
    
    /**
     * 销毁语音识别实例
     * 彻底清理语音识别实例并释放资源，确保下次创建全新实例
     */
    function destroySpeechRecognitionInstance() {
        log("销毁语音识别实例");
        
        // 处理全局变量中的实例
        if (globalSpeechRecognition) {
            try {
                // 先尝试停止和中止
                try {
                    globalSpeechRecognition.abort();
                    globalSpeechRecognition.stop();
                } catch (e) {
                    log("停止语音识别实例时出错: " + e.toString());
                }
                
                // 移除所有事件处理程序，避免内存泄漏
                globalSpeechRecognition.onstart = null;
                globalSpeechRecognition.onend = null;
                globalSpeechRecognition.onerror = null;
                globalSpeechRecognition.onresult = null;
                globalSpeechRecognition.onspeechend = null;
                globalSpeechRecognition.onnomatch = null;
                globalSpeechRecognition.onaudiostart = null;
                globalSpeechRecognition.onaudioend = null;
                globalSpeechRecognition.onsoundstart = null;
                globalSpeechRecognition.onsoundend = null;
                globalSpeechRecognition.onspeechstart = null;
                
                log("已清除全局语音识别实例的所有事件处理程序");
                
                // 设置为null，确保下次重新创建
                globalSpeechRecognition = null;
                window.globalSpeechRecognition = null;
                window.autoConversationState.speechRecognitionInstance = null;
                
                log("已销毁全局语音识别实例");
            } catch (e) {
                log("销毁全局语音识别实例失败: " + e.toString());
            }
        }
        
        // 处理window上可能存在的实例
        if (window.speechRecognition) {
            try {
                window.speechRecognition.abort();
                window.speechRecognition.stop();
                window.speechRecognition = null;
                log("已销毁window.speechRecognition实例");
            } catch (e) {
                log("销毁window.speechRecognition实例失败: " + e.toString());
            }
        }
        
        // 处理应用中可能存在的实例
        if (window.app && window.app.speechRecognition) {
            try {
                window.app.speechRecognition.abort();
                window.app.speechRecognition.stop();
                window.app.speechRecognition = null;
                log("已销毁应用中的语音识别实例");
            } catch (e) {
                log("销毁应用中的语音识别实例失败: " + e.toString());
            }
        }
        
        // 重置状态标志
        window.isRecognizing = false;
        if (window.app) {
            window.app.isRecognizing = false;
        }
        
        // 强制同步状态
        syncRecognitionState(false);
        
        log("语音识别实例销毁完成");
        
        // 额外检查所有变量状态
        log("检查销毁后的状态 - globalSpeechRecognition: " + (globalSpeechRecognition ? "存在" : "null") + 
            ", window.globalSpeechRecognition: " + (window.globalSpeechRecognition ? "存在" : "null") + 
            ", window.speechRecognition: " + (window.speechRecognition ? "存在" : "null"));
    }
    
    /**
     * 安全地停止语音识别，避免影响TTS播放
     * 此函数只使用程序方式停止语音识别，不会点击任何UI按钮
     */
    function safelyStopSpeechRecognition() {
        log("安全停止语音识别（避免影响TTS播放）");
        log("当前TTS播放状态：" + isTTSPlaying + "，语音识别状态：" + window.isRecognizing);
        
        // 清除语音超时检测
        clearSpeechTimeoutDetection();
        
        // 清除长时间无语音检测
        clearLongSilenceDetection();
        
        let recognitionStopped = false;
        
        // 强制重置状态
        window.isRecognizing = false;
        if (window.app) {
            window.app.isRecognizing = false;
        }
        
        // 1. 尝试通过全局语音识别对象停止
        if (window.globalSpeechRecognition) {
            try {
                log("通过全局对象安全停止语音识别");
                window.globalSpeechRecognition.abort();
                window.globalSpeechRecognition.stop();
                recognitionStopped = true;
                log("已发送停止命令到全局对象");
            } catch (e) {
                log("停止全局对象的语音识别失败: " + e.toString());
            }
        }
        
        // 2. 尝试通过应用实例停止
        if (window.app) {
            try {
                log("尝试通过应用实例安全停止语音识别");
                // 直接获取应用的语音识别实例
                if (window.app.speechRecognition) {
                    window.app.speechRecognition.abort();
                    window.app.speechRecognition.stop();
                    window.app.isRecognizing = false;
                    recognitionStopped = true;
                    log("已发送停止命令到应用实例");
                }
            } catch (e) {
                log("停止应用实例的语音识别失败: " + e.toString());
            }
        }
        
        // 3. 尝试通过window.speechRecognition停止
        if (window.speechRecognition) {
            try {
                log("通过window.speechRecognition安全停止语音识别");
                window.speechRecognition.abort();
                window.speechRecognition.stop();
                recognitionStopped = true;
                log("已发送停止命令到window.speechRecognition");
            } catch (e) {
                log("停止window.speechRecognition的语音识别失败: " + e.toString());
            }
        }
        
        // 4. 尝试获取浏览器中所有正在运行的语音识别实例并停止它们
        try {
            // 尝试找到可能在document或window上挂载的其他语音识别实例
            for (const key in window) {
                if (key.toLowerCase().includes('recognition') && typeof window[key] === 'object' && window[key] !== null && key !== 'webkitSpeechRecognition') {
                    try {
                        if (typeof window[key].abort === 'function') {
                            log("找到并尝试安全停止其他语音识别实例: " + key);
                            window[key].abort();
                            recognitionStopped = true;
                        } else if (typeof window[key].stop === 'function') {
                            log("找到并尝试安全停止其他语音识别实例: " + key);
                            window[key].stop();
                            recognitionStopped = true;
                        }
                    } catch (innerError) {
                        log("停止其他语音识别实例失败: " + key + " - " + innerError.toString());
                    }
                }
            }
        } catch (e) {
            log("查找和停止其他语音识别实例失败: " + e.toString());
        }
        
        // 5. 尝试创建一个新的实例并立即停止它，可能会触发底层浏览器API清理
        try {
            if ('webkitSpeechRecognition' in window) {
                log("创建新实例并立即停止作为额外措施");
                const tempRecognition = new window.webkitSpeechRecognition();
                tempRecognition.abort();
            }
        } catch (e) {
            log("创建和停止临时实例失败: " + e.toString());
        }
        
        // 无论如何，重置所有可能的状态标志
        window.isRecognizing = false;
        if (window.app) {
            window.app.isRecognizing = false;
        }
        
        // 强制同步窗口状态
        syncRecognitionState(false);
        
        // 记录停止时间，仅作为参考信息
        window.autoConversationState.lastRecognitionStopTime = Date.now();
        
        // 记录最终状态
        log("语音识别停止操作完成，当前状态：" + window.isRecognizing);
        
        if (recognitionStopped) {
            log("已安全停止语音识别，不影响TTS播放");
        } else {
            log("警告: 未能找到任何可停止的语音识别实例");
        }
        
        // 调用专门的函数销毁语音识别实例
        destroySpeechRecognitionInstance();
        
        return recognitionStopped;
    }

    /**
     * 初始化自动对话功能
     */
    function initAutoConversation() {
        log("初始化自动对话功能");
        
        // 查找并存储全局语音识别对象
        findSpeechRecognitionInstance();
        
        // 监听音频播放结束事件
        setupAudioEndListener();
        
        // 监听自动对话开关变化
        const autoConvSwitch = document.getElementById('auto-conversation-switch');
        if (autoConvSwitch) {
            autoConvSwitch.addEventListener('change', function() {
                log("自动对话模式: " + (autoConvSwitch.checked ? "开启" : "关闭"));
                // 可以在localStorage中保存用户偏好
                localStorage.setItem('autoConversationEnabled', autoConvSwitch.checked);
                
                // 如果关闭自动对话，退出沉默模式
                if (!autoConvSwitch.checked) {
                    window.autoConversationState.inSilentMode = false;
                    clearLongSilenceDetection();
                    log("自动对话关闭，退出沉默模式");
                }
            });
            
            // 从localStorage中读取之前的设置
            const savedState = localStorage.getItem('autoConversationEnabled');
            if (savedState !== null) {
                autoConvSwitch.checked = savedState === 'true';
                log("已从本地存储恢复设置: " + (savedState === 'true' ? "开启" : "关闭"));
            }
        } else {
            log("警告: 未找到自动对话开关元素");
        }
        
        // 添加手动触发按钮
        addManualTriggerButton();

        // 添加全局事件监听，更多的捕获可能
        setupGlobalEventListeners();
        
        // 添加MomentumCloudApplication响应监听 - 这是关键部分
        setupMomentumCloudResponseListener();
        
        // 启动定期状态检查
        startPeriodicStateCheck();
        
        // 初始化状态
        isTTSPlaying = false;
        if (window.autoConversationState) {
            window.autoConversationState.isTTSPlaying = false;
            window.autoConversationState.inSilentMode = false;
        }
        
        log("自动对话功能初始化完成，TTS播放状态：" + isTTSPlaying + "，沉默模式：" + window.autoConversationState.inSilentMode);
        
        // 创建沉默模式切换按钮
        createSilentModeToggle();
    }
    
    /**
     * 向页面注入脚本以直接捕获事件
     */
    function injectDirectCaptureScript() {
        try {
            // 创建一个脚本元素
            const script = document.createElement('script');
            script.textContent = `
                // 保存原始的Audio.prototype.play方法
                const originalAudioPlay = Audio.prototype.play;
                
                // 重写play方法
                Audio.prototype.play = function() {
                    console.log("[自动对话内联] 音频开始播放");
                    console.log("AUDIO_PLAY_START");
                    
                    // 在音频开始播放时停止语音识别
                    if (window.globalSpeechRecognition) {
                        console.log("[自动对话内联] 检测到音频开始播放，强制停止语音识别");
                        try {
                            // 强制停止，不管状态如何
                            window.globalSpeechRecognition.abort();
                            window.isRecognizing = false;
                            console.log("[自动对话内联] 已停止语音识别");
                            
                            // 300ms后再次检查并停止，以防有其他实例
                            setTimeout(function() {
                                try {
                                    if (window.isRecognizing || window.globalSpeechRecognition) {
                                        console.log("[自动对话内联] 检测到语音识别可能仍在运行，再次停止");
                                        window.globalSpeechRecognition.abort();
                                        window.isRecognizing = false;
                                    }
                                } catch (retryError) {
                                    console.log("[自动对话内联] 重试停止失败: " + retryError.toString());
                                }
                            }, 300);
                        } catch (e) {
                            console.log("[自动对话内联] 停止语音识别失败: " + e.toString());
                        }
                    }
                    
                    // 触发自定义事件
                    document.dispatchEvent(new CustomEvent('audio-play-start', {
                        detail: { source: 'audio_play', timestamp: Date.now() }
                    }));
                    
                    // 监听播放结束事件
                    this.addEventListener('ended', function() {
                        console.log("[自动对话内联] 音频播放结束");
                        console.log("AUDIO_PLAY_END");
                        
                        // 触发自定义事件
                        document.dispatchEvent(new CustomEvent('audio-play-end'));
                    }, { once: true });
                    
                    // 调用原始方法
                    return originalAudioPlay.apply(this, arguments);
                };
                
                // 监控MomentumCloudApplication对象
                function watchForMomentumCloudApp() {
                    if (window.MomentumCloudApplication && window.app) {
                        console.log("[自动对话内联] 找到MomentumCloudApplication实例");
                        
                        // 在全局范围内保存语音识别对象
                        if (window.app.speechRecognition) {
                            window.globalSpeechRecognition = window.app.speechRecognition;
                            console.log("[自动对话内联] 已保存语音识别对象到全局变量");
                        }
                        
                        // 监听处理响应方法
                        const originalHandleResponse = window.app.handleResponse;
                        window.app.handleResponse = function(response) {
                            // 调用原始方法
                            const result = originalHandleResponse.apply(this, arguments);
                            
                            // 检查音频播放开始事件
                            if (response && response.event === "AUDIO_PLAY_START") {
                                console.log("[自动对话内联] 捕获到AUDIO_PLAY_START事件");
                                console.log("AUDIO_PLAY_START");
                                
                                // 停止语音识别
                                if (window.globalSpeechRecognition) {
                                    console.log("[自动对话内联] 检测到音频开始播放，强制停止语音识别");
                                    try {
                                        // 强制停止，不管状态如何
                                        window.globalSpeechRecognition.abort();
                                        window.isRecognizing = false;
                                        console.log("[自动对话内联] 已停止语音识别");
                                        
                                        // 300ms后再次检查并停止，以防有其他实例
                                        setTimeout(function() {
                                            try {
                                                if (window.isRecognizing || window.globalSpeechRecognition) {
                                                    console.log("[自动对话内联] 检测到语音识别可能仍在运行，再次停止");
                                                    window.globalSpeechRecognition.abort();
                                                    window.isRecognizing = false;
                                                }
                                            } catch (retryError) {
                                                console.log("[自动对话内联] 重试停止失败: " + retryError.toString());
                                            }
                                        }, 300);
                                    } catch (e) {
                                        console.log("[自动对话内联] 停止语音识别失败: " + e.toString());
                                    }
                                }
                                
                                // 触发自定义事件
                                document.dispatchEvent(new CustomEvent('audio-play-start', {
                                    detail: { source: 'handle_response', timestamp: Date.now() }
                                }));
                            }
                            
                            // 检查音频播放结束事件
                            if (response && response.event === "AUDIO_PLAY_END") {
                                console.log("[自动对话内联] 捕获到AUDIO_PLAY_END事件");
                                console.log("AUDIO_PLAY_END");
                                
                                // 触发自定义事件
                                document.dispatchEvent(new CustomEvent('audio-play-end'));
                            }
                            
                            return result;
                        };
                    } else {
                        // 如果没有找到，稍后再尝试
                        setTimeout(watchForMomentumCloudApp, 1000);
                    }
                }
                
                // 开始监控
                watchForMomentumCloudApp();
                
                console.log("[自动对话内联] 直接捕获脚本已注入");
            `;
            
            // 将脚本添加到页面
            document.head.appendChild(script);
            log("已注入直接捕获脚本");
        } catch (e) {
            log("注入直接捕获脚本失败: " + e.toString());
        }
    }

    /**
     * 查找语音识别实例
     */
    function findSpeechRecognitionInstance() {
        log("查找语音识别实例");
        
        // 先检查内联脚本是否已经找到并存储了语音识别对象
        if (window.globalSpeechRecognition) {
            log("从内联脚本中获取到语音识别对象");
            globalSpeechRecognition = window.globalSpeechRecognition;
            window.autoConversationState.speechRecognitionInstance = window.globalSpeechRecognition;
        } else {
            // 尝试查找MomentumCloudApplication实例中的语音识别对象
            if (window.MomentumCloudApplication && window.app) {
                try {
                    const app = window.app;
                    if (app.speechRecognition) {
                        log("从应用实例中找到speechRecognition对象");
                        globalSpeechRecognition = app.speechRecognition;
                        window.autoConversationState.speechRecognitionInstance = app.speechRecognition;
                    }
                } catch (e) {
                    log("从应用实例获取speechRecognition失败: " + e.toString());
                }
            }
            
            // 如果上面的方法失败，尝试直接创建一个新的语音识别实例
            if (!globalSpeechRecognition && 'webkitSpeechRecognition' in window) {
                try {
                    log("创建新的webkitSpeechRecognition实例");
                    globalSpeechRecognition = new window.webkitSpeechRecognition();
                    globalSpeechRecognition.continuous = true;
                    globalSpeechRecognition.interimResults = true;
                    
                    // 设置事件处理函数
                    globalSpeechRecognition.onstart = function(event) {
                        log("直接创建的语音识别已开始");
                        window.isRecognizing = true;
                        
                        // 开始语音识别时初始化最后识别时间
                        lastSpeechDetectedTime = Date.now();
                        startSpeechTimeoutDetection();
                        
                        // 更新麦克风按钮外观
                        let micButton = document.getElementById('tts-chat-mic-button');
                        if (micButton) {
                            micButton.style.backgroundImage = 'url("/images/mic-anim.gif")';
                        }
                    };
                    
                    globalSpeechRecognition.onend = function(event) {
                        log("直接创建的语音识别已结束");
                        window.isRecognizing = false;
                        
                        // 清除语音超时检测
                        clearSpeechTimeoutDetection();
                        
                        // 更新麦克风按钮外观
                        let micButton = document.getElementById('tts-chat-mic-button');
                        if (micButton) {
                            micButton.style.backgroundImage = 'url("/images/mic.gif")';
                        }
                        
                        // 尝试自动提交
                        let submitBtn = document.getElementById('tts-chat-submit-button');
                        if (submitBtn) {
                            submitBtn.click();
                        }
                    };
                    
                    globalSpeechRecognition.onerror = function(event) {
                        log("语音识别错误: " + event.error);
                        
                        // 清除语音超时检测
                        clearSpeechTimeoutDetection();
                        
                        // 针对aborted错误的特殊处理
                        if (event.error === 'aborted') {
                            log("语音识别被中止，可能是由于资源冲突，将尝试自动恢复");
                            
                            // 确保所有语音识别实例都已停止
                            try {
                                if (globalSpeechRecognition) {
                                    globalSpeechRecognition.abort();
                                }
                                
                                // 如果存在其他语音识别实例，也尝试停止它们
                                if (window.speechRecognition) {
                                    window.speechRecognition.abort();
                                }
                                
                                // 重置识别状态
                                window.isRecognizing = false;
                            } catch (e) {
                                log("停止现有语音识别实例失败: " + e.toString());
                            }
                            
                            // 延迟后重试
                            setTimeout(function() {
                                // 检查自动对话开关是否打开
                                const autoConvSwitch = document.getElementById('auto-conversation-switch');
                                if (autoConvSwitch && autoConvSwitch.checked) {
                                    log("尝试在aborted错误后重新启动语音识别");
                                    triggerMicrophoneAfterDelay(2000);
                                }
                            }, 3000);
                        }
                    };
                    
                    globalSpeechRecognition.onresult = function(event) {
                        let interim_transcript = '';
                        let final_transcript = '';
                        
                        // 处理识别结果
                        for (let i = event.resultIndex; i < event.results.length; ++i) {
                            if (event.results[i].isFinal) {
                                final_transcript += event.results[i][0].transcript;
                            } else {
                                interim_transcript += event.results[i][0].transcript;
                            }
                        }
                        
                        // 每次收到结果，更新最后识别时间（用于其他用途）
                        lastSpeechDetectedTime = Date.now();

                        // 注意：不再重置定时器，让10秒定时器从开始就一直运行
                        
                        // 更新文本区域
                        if (interim_transcript.trim().length > 0) {
                            let textControl = document.getElementById('tts-chat-area');
                            if (textControl) {
                                textControl.value = interim_transcript;
                            }
                        }
                    };
                    
                    window.autoConversationState.speechRecognitionInstance = globalSpeechRecognition;
                } catch (e) {
                    log("创建语音识别实例失败: " + e.toString());
                }
            }
        }
        
        // 如果我们成功获取了原始的语音识别对象，扩展它的onresult事件
        if (globalSpeechRecognition) {
            try {
                // 保存原始的onresult处理程序
                const originalOnResult = globalSpeechRecognition.onresult;
                
                // 重写onresult事件处理
                globalSpeechRecognition.onresult = function(event) {
                    // 每次收到结果，更新最后识别时间（用于其他用途）
                    lastSpeechDetectedTime = Date.now();

                    // 注意：不再重置定时器，让10秒定时器从开始就一直运行
                    // 这样可以检测"10秒内是否有任何文字"而不是"是否有新文字"

                    // 调用原始的处理程序
                    if (originalOnResult) {
                        originalOnResult.call(this, event);
                    }
                };
                
                // 保存原始的onstart处理程序
                const originalOnStart = globalSpeechRecognition.onstart;
                
                // 重写onstart事件处理
                globalSpeechRecognition.onstart = function(event) {
                    // 开始语音识别时初始化最后识别时间
                    lastSpeechDetectedTime = Date.now();
                    startSpeechTimeoutDetection();
                    
                    // 调用原始的处理程序
                    if (originalOnStart) {
                        originalOnStart.call(this, event);
                    }
                };
                
                // 保存原始的onend处理程序
                const originalOnEnd = globalSpeechRecognition.onend;
                
                // 重写onend事件处理
                globalSpeechRecognition.onend = function(event) {
                    // 清除语音超时检测
                    clearSpeechTimeoutDetection();
                    
                    // 调用原始的处理程序
                    if (originalOnEnd) {
                        originalOnEnd.call(this, event);
                    }
                };
            } catch (e) {
                log("扩展语音识别事件失败: " + e.toString());
            }
            
            // 重新定义启动函数
            window.startSpeechRecognitionDirectly = function() {
                log("使用找到的实例直接启动语音识别");
                if (window.isRecognizing) {
                    log("语音识别已在进行中，不重复启动");
                    return false;
                }
                
                try {
                    // 清空文本区域
                    let textControl = document.getElementById('tts-chat-area');
                    if (textControl) {
                        textControl.value = '';
                    }
                    
                    // 设置语言
                    let langControl = document.getElementById('tts-chat-mic-lang-control');
                    if (langControl) {
                        globalSpeechRecognition.lang = langControl.value;
                    }
                    
                    // 开始识别
                    globalSpeechRecognition.start();
                    return true;
                } catch (e) {
                    log("启动语音识别失败: " + e.toString());
                    return false;
                }
            };
            
            log("语音识别对象初始化完成");
        } else {
            log("警告: 未能找到或创建语音识别对象");
        }
    }
    
    /**
     * 设置MomentumCloudApplication响应监听
     * 这是基于控制台日志中发现的事件模式
     */
    function setupMomentumCloudResponseListener() {
        log("设置MomentumCloudApplication响应监听");
        
        // 监听原始控制台日志以捕获事件
        const originalConsoleLog = console.log;
        console.log = function() {
            // 调用原始日志函数
            originalConsoleLog.apply(console, arguments);
            
            // 检查是否是我们感兴趣的事件
            if (arguments.length >= 1) {
                try {
                    // 首先检查字符串形式的事件
                    if (typeof arguments[0] === 'string') {
                        if (arguments[0] === "AUDIO_PLAY_START") {
                            log("检测到直接AUDIO_PLAY_START事件字符串");
                            
                            // 安全停止语音识别，避免影响TTS播放
                            safelyStopSpeechRecognition();
                            
                            // 设置状态标志
                            window.isRecognizing = false;
                            if (window.app) {
                                window.app.isRecognizing = false;
                            }
                            
                            // 触发自定义事件，确保其他监听器也能知道
                            document.dispatchEvent(new CustomEvent('audio-play-start', {
                                detail: { source: 'console_log', timestamp: Date.now() }
                            }));
                            
                            // 短暂延迟后再次检查并停止，以防语音识别在事件处理后被重新启动
                            setTimeout(function() {
                                if (window.isRecognizing) {
                                    log("检测到语音识别被重新启动，再次安全停止");
                                    safelyStopSpeechRecognition();
                                }
                            }, 200);
                        }
                        else if (arguments[0] === "AUDIO_PLAY_END") {
                            log("检测到直接AUDIO_PLAY_END事件字符串");
                            // 触发自定义事件，确保其他监听器也能知道
                            document.dispatchEvent(new CustomEvent('audio-play-end'));
                            triggerMicrophoneAfterDelay(800);
                        }
                    }
                    
                    // 尝试解析第二个参数为JSON
                    let eventData;
                    if (typeof arguments[1] === 'string') {
                        try {
                            eventData = JSON.parse(arguments[1]);
                        } catch(e) {
                            // 不是JSON，忽略
                        }
                    } else if (typeof arguments[1] === 'object') {
                        eventData = arguments[1];
                    }
                    
                    // 如果解析成功且包含我们需要的事件
                    if (eventData && eventData.event === "AUDIO_PLAY_START") {
                        log("检测到AUDIO_PLAY_START事件对象");
                        
                        // 安全停止语音识别，避免影响TTS播放
                        safelyStopSpeechRecognition();
                        
                        // 设置状态标志
                        window.isRecognizing = false;
                        if (window.app) {
                            window.app.isRecognizing = false;
                        }
                        
                        // 触发自定义事件，确保其他监听器也能知道
                        document.dispatchEvent(new CustomEvent('audio-play-start', {
                            detail: { source: 'event_data', timestamp: Date.now() }
                        }));
                        
                        // 短暂延迟后再次检查并停止，以防语音识别在事件处理后被重新启动
                        setTimeout(function() {
                            if (window.isRecognizing) {
                                log("检测到语音识别被重新启动，再次安全停止");
                                safelyStopSpeechRecognition();
                            }
                        }, 200);
                    }
                    else if (eventData && eventData.event === "AUDIO_PLAY_END") {
                        log("检测到AUDIO_PLAY_END事件对象");
                        // 触发自定义事件，确保其他监听器也能知道
                        document.dispatchEvent(new CustomEvent('audio-play-end'));
                        triggerMicrophoneAfterDelay(800);
                    }
                } catch(e) {
                    // 解析失败，忽略
                }
            }
        };
        
        // 直接监听MomentumCloudApplication对象
        if (window.MomentumCloudApplication) {
            log("找到MomentumCloudApplication对象，尝试监听事件");
            monitorMomentumCloudEvents();
        } else {
            // 如果对象还不存在，等待它被创建
            log("等待MomentumCloudApplication对象被创建");
            Object.defineProperty(window, 'MomentumCloudApplication', {
                set: function(newValue) {
                    this._MomentumCloudApplication = newValue;
                    log("MomentumCloudApplication对象被创建");
                    monitorMomentumCloudEvents();
                    return newValue;
                },
                get: function() {
                    return this._MomentumCloudApplication;
                },
                configurable: true
            });
        }
    }
    
    /**
     * 监控MomentumCloudApplication事件
     */
    function monitorMomentumCloudEvents() {
        log("开始监控MomentumCloudApplication事件");
        
        // 尝试找到应用实例
        let appInstance = null;
        
        // 检查是否有全局应用实例
        if (window.app && window.app instanceof MomentumCloudApplication) {
            appInstance = window.app;
            log("找到全局应用实例");
        }
        
        // 如果找到应用实例
        if (appInstance) {
            // 暴露应用实例的启动语音识别方法
            if (typeof appInstance.speechRecognition !== 'undefined') {
                window.startAppSpeechRecognition = function() {
                    log("通过应用实例启动语音识别");
                    if (!appInstance.isRecognizing) {
                        try {
                            // 清空文本域
                            let textControl = document.getElementById('tts-chat-area');
                            if (textControl) {
                                textControl.value = '';
                            }
                            // 设置语言
                            let langControl = document.getElementById('tts-chat-mic-lang-control');
                            if (langControl) {
                                appInstance.speechRecognition.lang = langControl.value;
                            }
                            // 开始识别
                            appInstance.speechRecognition.start();
                            return true;
                        } catch (e) {
                            log("启动语音识别失败: " + e.toString());
                            return false;
                        }
                    } else {
                        log("语音识别已在进行中");
                        return false;
                    }
                };
            }
            
            // 监听原始的handleResponse方法
            const originalHandleResponse = appInstance.handleResponse;
            if (typeof originalHandleResponse === 'function') {
                log("重写handleResponse方法");
                appInstance.handleResponse = function(response) {
                    // 调用原始方法
                    const result = originalHandleResponse.apply(this, arguments);
                    
                    // 检查是否是音频播放开始事件
                    if (response && response.event === "AUDIO_PLAY_START") {
                        log("通过handleResponse检测到AUDIO_PLAY_START事件");
                        
                        // 安全停止语音识别，避免影响TTS播放
                        safelyStopSpeechRecognition();
                        
                        // 确保状态标志被重置
                        window.isRecognizing = false;
                        appInstance.isRecognizing = false;
                        
                        // 短暂延迟后再次检查
                        setTimeout(function() {
                            if (window.isRecognizing || appInstance.isRecognizing) {
                                log("检测到语音识别仍在运行，再次安全停止");
                                safelyStopSpeechRecognition();
                            }
                        }, 200);
                    }
                    
                    // 检查是否是音频播放结束事件
                    if (response && response.event === "AUDIO_PLAY_END") {
                        log("通过handleResponse检测到AUDIO_PLAY_END事件");
                        triggerMicrophoneAfterDelay(800);
                    }
                    
                    return result;
                };
            }
        } else {
            // 如果没有找到应用实例，使用原型监听
            log("未找到应用实例，尝试通过原型监听");
            const prototype = MomentumCloudApplication.prototype;
            
            // 监听handleResponse方法
            if (prototype.handleResponse) {
                const originalProtoHandleResponse = prototype.handleResponse;
                prototype.handleResponse = function(response) {
                    // 调用原始方法
                    const result = originalProtoHandleResponse.apply(this, arguments);
                    
                    // 检查是否是音频播放开始事件
                    if (response && response.event === "AUDIO_PLAY_START") {
                        log("通过原型方法检测到AUDIO_PLAY_START事件");
                        
                        // 安全停止语音识别，避免影响TTS播放
                        safelyStopSpeechRecognition();
                        
                        // 确保状态标志被重置
                        window.isRecognizing = false;
                        if (window.app) {
                            window.app.isRecognizing = false;
                        }
                        
                        // 短暂延迟后再次检查
                        setTimeout(function() {
                            if (window.isRecognizing || (window.app && window.app.isRecognizing)) {
                                log("检测到语音识别仍在运行，再次安全停止");
                                safelyStopSpeechRecognition();
                            }
                        }, 200);
                    }
                    
                    // 检查是否是音频播放结束事件
                    if (response && response.event === "AUDIO_PLAY_END") {
                        log("通过原型方法检测到AUDIO_PLAY_END事件");
                        triggerMicrophoneAfterDelay(800);
                    }
                    
                    return result;
                };
            }
        }
    }

    /**
     * 设置全局事件监听
     */
    function setupGlobalEventListeners() {
        // 添加防重复触发的时间戳记录
        let lastAudioPlayStartTime = 0;
        let lastAudioPlayEndTime = 0;
        const minEventInterval = 500; // 最小事件间隔(毫秒)
        
        // 监听全局的audio-play-start事件
        document.addEventListener('audio-play-start', function(event) {
            const now = Date.now();
            // 检查是否在短时间内重复触发
            if (now - lastAudioPlayStartTime < minEventInterval) {
                log("忽略重复的audio-play-start事件，距上次触发时间太短");
                return;
            }
            
            lastAudioPlayStartTime = now;
            log("捕获到全局audio-play-start事件" + (event.detail ? ", 来源: " + event.detail.source : ""));
            
            // 设置TTS播放状态为true
            isTTSPlaying = true;
            window.autoConversationState.isTTSPlaying = true;
            log("TTS播放状态已设置为：" + isTTSPlaying);
            
            // 重置沉默模式标志并更新语音活动时间
            window.autoConversationState.inSilentMode = false;
            // 更新语音活动时间（TTS播放也算作语音活动）
            const currentTime = Date.now();
            lastSpeechActivityTime = currentTime;
            window.autoConversationState.lastSpeechActivityTime = currentTime;
            log("检测到TTS播放，重置沉默模式并更新语音活动时间");
            
            // 强制停止所有语音识别
            safelyStopSpeechRecognition();
            
            // 500ms后再次检查，确保彻底停止
            setTimeout(function() {
                if (window.isRecognizing) {
                    log("检测到语音识别仍在运行，再次尝试安全停止");
                    safelyStopSpeechRecognition();
                }
                
                // 再次确认状态
                window.isRecognizing = false;
                if (window.app) {
                    window.app.isRecognizing = false;
                }
                syncRecognitionState(false);
                log("TTS播放开始后，语音识别状态：" + window.isRecognizing);
            }, 500);
        });

        // 监听全局的audio-play-end事件
        document.addEventListener('audio-play-end', function(event) {
            const now = Date.now();
            // 检查是否在短时间内重复触发
            if (now - lastAudioPlayEndTime < minEventInterval) {
                log("忽略重复的audio-play-end事件，距上次触发时间太短");
                return;
            }
            
            lastAudioPlayEndTime = now;
            log("捕获到内联脚本触发的audio-play-end事件");
            
            // 设置TTS播放状态为false
            isTTSPlaying = false;
            window.autoConversationState.isTTSPlaying = false;
            log("TTS播放状态已设置为：" + isTTSPlaying);
            
            // 清空文本框，确保下一次语音识别开始时不会包含旧内容
            try {
                const textArea = document.getElementById('tts-chat-area');
                if (textArea) {
                    if (textArea.value && textArea.value.trim().length > 0) {
                        log("TTS播放结束后清空文本框，原内容: " + textArea.value);
                    }
                    textArea.value = '';
                }
            } catch (e) {
                log("TTS播放结束后清空文本框失败: " + e.toString());
            }
            
            // 确保语音识别已停止
            safelyStopSpeechRecognition();
            
            // 延迟后再触发麦克风
            setTimeout(function() {
                log("TTS播放结束，准备触发麦克风");
                triggerMicrophoneAfterDelay();
            }, 300);
        });

        // 监听全局的audioended事件
        document.addEventListener('audioended', function(event) {
            log("捕获到全局audioended事件");
            
            // 设置TTS播放状态为false
            isTTSPlaying = false;
            window.autoConversationState.isTTSPlaying = false;
            log("TTS播放状态已设置为：" + isTTSPlaying);
            
            // 清空文本框，确保下一次语音识别开始时不会包含旧内容
            try {
                const textArea = document.getElementById('tts-chat-area');
                if (textArea) {
                    if (textArea.value && textArea.value.trim().length > 0) {
                        log("audioended事件后清空文本框，原内容: " + textArea.value);
                    }
                    textArea.value = '';
                }
            } catch (e) {
                log("audioended事件后清空文本框失败: " + e.toString());
            }
            
            triggerMicrophoneAfterDelay();
        });

        // 尝试监听ttsChatStopButton的点击事件，这通常在语音结束后会被自动点击
        const stopButton = document.getElementById('tts-chat-stop-button');
        if (stopButton) {
            const originalClickHandler = stopButton.onclick;
            stopButton.onclick = function(event) {
                // 调用原始处理函数
                if (originalClickHandler) {
                    originalClickHandler.call(this, event);
                }
                
                log("检测到停止按钮点击，可能是语音播放结束");
                
                // 设置TTS播放状态为false
                isTTSPlaying = false;
                window.autoConversationState.isTTSPlaying = false;
                log("TTS播放状态已设置为：" + isTTSPlaying);
                
                // 清空文本框，确保下一次语音识别开始时不会包含旧内容
                try {
                    const textArea = document.getElementById('tts-chat-area');
                    if (textArea) {
                        if (textArea.value && textArea.value.trim().length > 0) {
                            log("停止按钮点击后清空文本框，原内容: " + textArea.value);
                        }
                        textArea.value = '';
                    }
                } catch (e) {
                    log("停止按钮点击后清空文本框失败: " + e.toString());
                }
                
                // 延迟触发麦克风，因为此时可能语音仍在结束过程中
                setTimeout(function() {
                    triggerMicrophoneAfterDelay();
                }, 500);
            };
        }

        // 每秒检查一次isRecognizing状态变化
        let lastRecognizingState = false;
        setInterval(function() {
            // 全局变量可能在不同位置定义
            const isRecognizing = window.isRecognizing || false;
            
            // 如果从识别状态变为非识别状态，可能是语音回复已经结束
            if (lastRecognizingState && !isRecognizing) {
                log("检测到语音识别状态变为关闭，可能是语音回复已结束");
                
                // 如果TTS不在播放，触发麦克风
                if (!isTTSPlaying && !(window.autoConversationState && window.autoConversationState.isTTSPlaying)) {
                    log("TTS未在播放，准备触发麦克风");
                triggerMicrophoneAfterDelay(1500); // 延迟更长一些
                } else {
                    log("TTS正在播放中，不触发麦克风");
                }
            }
            
            lastRecognizingState = isRecognizing;
        }, 1000);
        
        // 监听所有音频相关事件
        document.addEventListener('audioend', function(event) {
            log("捕获到audioend事件");
            
            // 设置TTS播放状态为false
            isTTSPlaying = false;
            window.autoConversationState.isTTSPlaying = false;
            log("TTS播放状态已设置为：" + isTTSPlaying);
            
            // 清空文本框，确保下一次语音识别开始时不会包含旧内容
            try {
                const textArea = document.getElementById('tts-chat-area');
                if (textArea) {
                    if (textArea.value && textArea.value.trim().length > 0) {
                        log("audioend事件后清空文本框，原内容: " + textArea.value);
                    }
                    textArea.value = '';
                }
            } catch (e) {
                log("audioend事件后清空文本框失败: " + e.toString());
            }
            
            triggerMicrophoneAfterDelay();
        });
        
        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            // Alt+M 组合键手动触发麦克风
            if (event.altKey && event.key === 'm') {
                log("检测到快捷键Alt+M，手动触发麦克风");
                triggerMicrophoneAfterDelay(100);
            }
        });

        // 监听唤醒事件，用于退出沉默模式
        document.addEventListener('keyword-wakeup', function(event) {
            log("捕获到关键词唤醒事件，退出沉默模式");
            
            // 重置沉默模式标志
            window.autoConversationState.inSilentMode = false;
            
            // 清除长时间无语音检测
            clearLongSilenceDetection();
        });
        
        // 添加手动退出沉默模式的方法
        window.exitSilentMode = function() {
            log("手动退出沉默模式");
            
            // 重置沉默模式标志
            window.autoConversationState.inSilentMode = false;
            
            // 清除长时间无语音检测
            clearLongSilenceDetection();
            
            // 触发麦克风
            triggerMicrophoneAfterDelay(100);
        };
    }
    
    /**
     * 设置音频播放结束事件监听
     */
    function setupAudioEndListener() {
        log("设置音频播放结束事件监听");

        // 方法1: 尝试重写HTMLAudioElement的onended属性
        const originalCreateElement = document.createElement;
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(document, tagName);
            if (tagName.toLowerCase() === 'audio') {
                const originalOnEnded = element.onended;
                element.addEventListener('ended', function(event) {
                    log("捕获到音频元素播放结束事件");
                    if (originalOnEnded) {
                        originalOnEnded.call(this, event);
                    }
                    
                    // 清空文本框，确保下一次语音识别开始时不会包含旧内容
                    try {
                        const textArea = document.getElementById('tts-chat-area');
                        if (textArea) {
                            if (textArea.value && textArea.value.trim().length > 0) {
                                log("音频元素播放结束后清空文本框，原内容: " + textArea.value);
                            }
                            textArea.value = '';
                        }
                    } catch (e) {
                        log("音频元素播放结束后清空文本框失败: " + e.toString());
                    }
                    
                    triggerMicrophoneAfterDelay();
                });
            }
            return element;
        };

        // 方法2: 周期性检查现有的音频元素
        setInterval(function() {
            const audioElements = document.querySelectorAll('audio');
            audioElements.forEach(function(audio) {
                if (!audio._autoConvMonitored) {
                    audio._autoConvMonitored = true;
                    log("为现有音频元素添加播放结束监听");
                    audio.addEventListener('ended', function() {
                        log("捕获到现有音频元素播放结束事件");
                        
                        // 清空文本框，确保下一次语音识别开始时不会包含旧内容
                        try {
                            const textArea = document.getElementById('tts-chat-area');
                            if (textArea) {
                                if (textArea.value && textArea.value.trim().length > 0) {
                                    log("现有音频元素播放结束后清空文本框，原内容: " + textArea.value);
                                }
                                textArea.value = '';
                            }
                        } catch (e) {
                            log("现有音频元素播放结束后清空文本框失败: " + e.toString());
                        }
                        
                        triggerMicrophoneAfterDelay();
                    });
                }
            });
        }, 2000);

        // 方法3: 检查WebRTC播放器对象
        tryWatchWebRtcPlayer();

        // 方法4: 使用MutationObserver监听DOM变化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeName === 'AUDIO') {
                        log("检测到新的音频元素添加到DOM");
                        node.addEventListener('ended', function() {
                            log("捕获到新添加音频元素的播放结束事件");
                            
                            // 清空文本框，确保下一次语音识别开始时不会包含旧内容
                            try {
                                const textArea = document.getElementById('tts-chat-area');
                                if (textArea) {
                                    if (textArea.value && textArea.value.trim().length > 0) {
                                        log("新添加音频元素播放结束后清空文本框，原内容: " + textArea.value);
                                    }
                                    textArea.value = '';
                                }
                            } catch (e) {
                                log("新添加音频元素播放结束后清空文本框失败: " + e.toString());
                            }
                            
                            triggerMicrophoneAfterDelay();
                        });
                    }
                });
            });
        });
        
        observer.observe(document.body, { 
            childList: true, 
            subtree: true 
        });
    }

    /**
     * 尝试监控WebRTC播放器对象
     */
    function tryWatchWebRtcPlayer() {
        log("尝试监控WebRTC播放器对象");
        
        // 检查是否已有webRtcPlayerObj对象
        if (window.webRtcPlayerObj) {
            extendAudioPlayback(window.webRtcPlayerObj);
        }
        
        // 定义一个监听器，尝试拦截webRtcPlayerObj的创建
        Object.defineProperty(window, 'webRtcPlayerObj', {
            set: function(newValue) {
                log("检测到webRtcPlayerObj设置");
                this._webRtcPlayerObj = newValue;
                if (newValue) {
                    extendAudioPlayback(newValue);
                }
            },
            get: function() {
                return this._webRtcPlayerObj;
            },
            configurable: true
        });
        
        // 也尝试监控MomentumCloudApplication
        if (window.MomentumCloudApplication) {
            const prototype = MomentumCloudApplication.prototype;
            if (prototype.playAudio) {
                extendMomentumCloudPlayAudio(prototype);
            }
        }
    }
    
    /**
     * 扩展音频播放功能
     */
    function extendAudioPlayback(playerObj) {
        log("扩展音频播放功能");
        
        if (!playerObj) {
            log("错误: 播放器对象为空");
            return;
        }
        
        // 保存原始的播放音频方法
        const originalPlayAudio = playerObj.playAudio;
        
        if (typeof originalPlayAudio === 'function') {
            log("替换playAudio方法");
            
            // 重写播放音频方法以添加我们的事件监听
            playerObj.playAudio = function(url) {
                log("播放音频: " + url);
                
                // 调用原始方法
                const result = originalPlayAudio.call(this, url);
                
                // 如果存在playAudioSource属性，添加事件监听
                if (this.playAudioSource) {
                    log("为playAudioSource添加onended处理");
                    const originalOnEnded = this.playAudioSource.onended;
                    
                    // 设置新的onended回调
                    this.playAudioSource.onended = function(event) {
                        log("音频播放结束");
                        // 调用原始回调
                        if (originalOnEnded) {
                            originalOnEnded.call(this, event);
                        }
                        
                        // 添加我们的功能 - 触发麦克风
                        triggerMicrophoneAfterDelay();
                    };
                } else {
                    log("警告: 播放器对象没有playAudioSource属性");
                    
                    // 如果没有明确的结束事件，使用一个估计的延迟后自动触发
                    if (url) {
                        // 假设音频平均长度为5秒
                        setTimeout(function() {
                            log("基于估计时间触发麦克风");
                            triggerMicrophoneAfterDelay();
                        }, 5000);
                    }
                }
                
                return result;
            };
        } else {
            log("警告: playAudio不是一个函数");
        }
    }
    
    /**
     * 扩展MomentumCloudApplication的playAudio方法
     */
    function extendMomentumCloudPlayAudio(prototype) {
        log("扩展MomentumCloudApplication的playAudio方法");
        
        const originalAppPlayAudio = prototype.playAudio;
        
        prototype.playAudio = function(url) {
            log("MomentumCloudApplication播放音频: " + url);
            
            const result = originalAppPlayAudio.call(this, url);
            
            if (this.playAudioSource) {
                log("为MomentumCloudApplication的playAudioSource添加onended处理");
                const originalOnEnded = this.playAudioSource.onended;
                
                this.playAudioSource.onended = ((audiosource, ev) => {
                    log("MomentumCloudApplication音频播放结束");
                    // 调用原始回调
                    if (originalOnEnded) {
                        originalOnEnded.call(this, audiosource, ev);
                    }
                    
                    // 添加我们的功能 - 触发麦克风
                    triggerMicrophoneAfterDelay();
                }).bind(this, this.playAudioSource);
            } else {
                log("警告: MomentumCloudApplication没有playAudioSource属性");
                
                // 使用估计延迟
                setTimeout(function() {
                    log("基于估计时间触发麦克风（MomentumCloud）");
                    triggerMicrophoneAfterDelay();
                }, 5000);
            }
            
            return result;
        };
    }
    
    /**
     * 添加手动触发按钮
     */
    function addManualTriggerButton() {
        // 在UI上添加一个手动触发按钮，以便在自动模式失效时手动启动麦克风
        try {
            // 创建一个浮动按钮，固定在页面右下角
            const triggerBtn = document.createElement('button');
            triggerBtn.id = 'manual-mic-trigger';
            triggerBtn.textContent = '🎤';
            triggerBtn.title = '手动激活麦克风 (Alt+M)';
            triggerBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 9999;
                width: 50px;
                height: 50px;
                border-radius: 25px;
                background-color: #007bff;
                color: white;
                border: none;
                box-shadow: 0 2px 5px rgba(0,0,0,0.3);
                cursor: pointer;
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            // 点击事件处理
            triggerBtn.addEventListener('click', function() {
                log("手动触发按钮被点击");
                triggerMicrophoneAfterDelay(100);
            });
            
            // 添加到页面
            document.body.appendChild(triggerBtn);
            log("已添加手动触发麦克风按钮");
        } catch (e) {
            log("添加手动触发按钮失败: " + e.toString());
        }
    }
    
    /**
     * 延迟一段时间后自动激活麦克风
     */
    function triggerMicrophoneAfterDelay(delay = 1000) {
        // 先清空文本框，防止新识别包含旧内容
        try {
            const textArea = document.getElementById('tts-chat-area');
            if (textArea) {
                if (textArea.value && textArea.value.trim().length > 0) {
                    log("在触发麦克风前清空文本框，原内容: " + textArea.value);
                }
                textArea.value = '';
            }
        } catch (e) {
            log("清空文本框失败: " + e.toString());
        }
        
        // 检查自动对话开关是否打开
        const autoConvSwitch = document.getElementById('auto-conversation-switch');
        if (!autoConvSwitch || !autoConvSwitch.checked) {
            log("自动对话开关未打开，不触发麦克风");
            return;
        }
        
        // 检查TTS是否正在播放
        if (isTTSPlaying || (window.autoConversationState && window.autoConversationState.isTTSPlaying)) {
            log("TTS正在播放中，不触发麦克风");
            return;
        }
        
        // 如果处于沉默模式，不自动触发麦克风
        if (window.autoConversationState.inSilentMode) {
            log("处于沉默模式，不自动触发麦克风，需要通过唤醒词激活");
            return;
        }
        
        // 检查是否已经超过10秒无语音活动
        const timeSinceActivity = Date.now() - lastSpeechActivityTime;
        if (timeSinceActivity >= 10000 && lastSpeechActivityTime > 0) {
            log(`距离上次语音活动已经过去${timeSinceActivity}毫秒，自动进入沉默模式`);
            enterSilentMode("触发麦克风前检测到超过10秒无语音活动");
            return;
        }
        
        // 先确保销毁任何可能正在运行的语音识别实例
        destroySpeechRecognitionInstance();
        
        // 记录激活尝试
        window.autoConversationState.micActivationAttempts++;
        
        // 延迟一段时间后自动点击麦克风按钮
        log("计划在" + delay + "毫秒后激活麦克风 (尝试 #" + window.autoConversationState.micActivationAttempts + ")");
        setTimeout(function() {
            // 再次检查TTS播放状态
                            if (isTTSPlaying || (window.autoConversationState && window.autoConversationState.isTTSPlaying)) {
                    log("延迟后检测到TTS仍在播放，取消麦克风激活");
                    return;
                }
                
                // 再次检查沉默模式
                if (window.autoConversationState.inSilentMode) {
                    log("延迟后检测到仍处于沉默模式，取消麦克风激活");
                    return;
                }
            
            // 再次检查是否仍有语音识别在运行
            if (window.isRecognizing) {
                log("检测到语音识别仍在运行，尝试彻底销毁实例");
                destroySpeechRecognitionInstance();
                // 短延迟后再尝试启动
                setTimeout(startRecognition, 300);
            } else {
                startRecognition();
            }
            
            function startRecognition() {
                // 再次清空文本框，确保新会话开始时文本框是空的
                try {
                    const textArea = document.getElementById('tts-chat-area');
                    if (textArea) {
                        if (textArea.value && textArea.value.trim().length > 0) {
                            log("在启动语音识别前再次清空文本框，原内容: " + textArea.value);
                        }
                        textArea.value = '';
                    }
                } catch (e) {
                    log("启动前清空文本框失败: " + e.toString());
                }
                
                // 最后一次检查TTS播放状态
                if (isTTSPlaying || (window.autoConversationState && window.autoConversationState.isTTSPlaying)) {
                    log("启动前最终检查发现TTS仍在播放，取消麦克风激活");
                    return;
                }
                
                // 最后一次检查沉默模式
                if (window.autoConversationState.inSilentMode) {
                    log("启动前最终检查发现仍处于沉默模式，取消麦克风激活");
                    return;
                }
                
                log("开始启动语音识别，当前TTS播放状态：" + isTTSPlaying);
                
                // 始终创建新的语音识别实例，而不是尝试重用全局实例
                log("创建全新的语音识别实例");
                
                // 首先销毁任何可能存在的旧实例
                destroySpeechRecognitionInstance();
                
                // 检查是否支持语音识别API
                if (!('webkitSpeechRecognition' in window)) {
                    log("错误：浏览器不支持语音识别API");
                    return;
                }
                
                try {
                    // 创建全新的语音识别实例
                    const newRecognition = new window.webkitSpeechRecognition();
                    
                    // 设置基本属性
                    newRecognition.continuous = true;  // 持续识别
                    newRecognition.interimResults = true;  // 实时返回识别结果
                    
                    // 设置语言
                    let langControl = document.getElementById('tts-chat-mic-lang-control');
                    if (langControl) {
                        newRecognition.lang = langControl.value;
                    } else {
                        // 默认使用中文
                        newRecognition.lang = 'zh-CN';
                    }
                    
                    // 清空文本域
                    let textControl = document.getElementById('tts-chat-area');
                    if (textControl) {
                        textControl.value = '';
                    }
                    
                    // 添加结果事件处理
                    newRecognition.onresult = function(event) {
                        // 更新最后语音活动时间
                        const currentTime = Date.now();
                        lastSpeechActivityTime = currentTime;
                        lastSpeechDetectedTime = currentTime;
                        window.autoConversationState.lastSpeechActivityTime = currentTime;
                        
                        log("检测到语音活动，更新时间戳");
                        
                        // 重置沉默模式标志
                        window.autoConversationState.inSilentMode = false;
                        
                        // 重启长时间无语音检测
                        startLongSilenceDetection();

                        // 注意：不再重启语音超时检测，让10秒定时器从开始就一直运行
                        
                        // 处理识别结果
                        let transcript = '';
                        for (let i = event.resultIndex; i < event.results.length; ++i) {
                            transcript += event.results[i][0].transcript;
                        }
                        
                        // 更新文本框（使用设置值而不是累加）
                        let textControl = document.getElementById('tts-chat-area');
                        if (textControl) {
                            textControl.value = transcript;
                        }
                    };
                    
                    // 添加开始事件处理
                    newRecognition.onstart = function() {
                        log("新创建的语音识别已开始");
                        syncRecognitionState(true);
                        
                        // 最后一次检查文本框，确保是空的
                        try {
                            const textArea = document.getElementById('tts-chat-area');
                            if (textArea && textArea.value && textArea.value.trim().length > 0) {
                                log("语音识别开始时发现文本框非空，强制清空: " + textArea.value);
                                textArea.value = '';
                            }
                        } catch (e) {
                            log("语音识别开始时清空文本框失败: " + e.toString());
                        }
                        
                        // 启动长时间无语音检测
                        startLongSilenceDetection();
                        
                        // 更新最后语音活动时间
                        lastSpeechActivityTime = Date.now();
                        lastSpeechDetectedTime = Date.now();
                    };
                    
                    // 添加结束事件处理
                    newRecognition.onend = function() {
                        log("新创建的语音识别已结束");
                        syncRecognitionState(false);
                        
                        // 清除长时间无语音检测
                        clearLongSilenceDetection();
                        
                        // 在结束时彻底销毁实例
                        destroySpeechRecognitionInstance();
                    };
                    
                    // 添加错误事件处理
                    newRecognition.onerror = function(event) {
                        log("语音识别错误: " + event.error);
                        syncRecognitionState(false);
                        
                        // 清除长时间无语音检测
                        clearLongSilenceDetection();
                        
                        // 在错误发生时彻底销毁实例
                        destroySpeechRecognitionInstance();
                    };
                    
                    // 启动新的语音识别实例
                    newRecognition.start();
                    
                    // 保存实例到全局变量，但只用于停止操作，不用于重新启动
                    globalSpeechRecognition = newRecognition;
                    window.globalSpeechRecognition = newRecognition;
                    window.autoConversationState.speechRecognitionInstance = newRecognition;
                    
                    // 更新状态
                    syncRecognitionState(true);
                    log("成功创建并启动新的语音识别实例");
                    
                    // 启动语音超时检测
                    startSpeechTimeoutDetection();
                    
                    return;
                } catch (e) {
                    log("创建并启动新的语音识别实例失败: " + e.toString());
                    syncRecognitionState(false);
                }
                
                // 我们已经使用了新方法创建语音识别实例，不再需要其他方法
                log("使用新的语音识别实例方法，忽略旧的按钮点击和其他调用方法");
            }
        }, delay);
    }

    // 页面卸载前尝试恢复原始方法
    window.addEventListener('beforeunload', function() {
        log("页面卸载，恢复原始方法");
        if (document._originalCreateElement) {
            document.createElement = document._originalCreateElement;
        }
        
        // 恢复console.log
        if (console._originalLog) {
            console.log = console._originalLog;
        }
    });

    // 立即在控制台显示一条消息，表明脚本已加载
    log("自动对话脚本已加载 v1.6");

    // 添加语音超时检测相关函数
    /**
     * 开始语音超时检测
     */
    function startSpeechTimeoutDetection() {
        // 如果TTS正在播放，不启动超时检测
        if (isTTSPlaying || (window.autoConversationState && window.autoConversationState.isTTSPlaying)) {
            log("TTS正在播放中，不启动语音超时检测");
            return;
        }

        // 清除之前的定时器
        clearSpeechTimeoutDetection();

        // 设置新的定时器 - 检查文本框是否有内容
        speechTimeoutId = setTimeout(() => {
            // 再次检查TTS播放状态
            if (isTTSPlaying || (window.autoConversationState && window.autoConversationState.isTTSPlaying)) {
                log("超时触发时检测到TTS正在播放，不执行超时操作");
                return;
            }

            log(`语音识别已进行${SPEECH_TIMEOUT_MS}毫秒，检查文本框内容`);

            // 如果还在识别中，则强制停止并检查内容
            if (window.isRecognizing) {
                // 彻底销毁语音识别实例
                destroySpeechRecognitionInstance();

                // 延迟一点时间后检查文本框内容
                setTimeout(() => {
                    // 检查文本框是否有任何内容
                    const textArea = document.getElementById('tts-chat-area');
                    if (textArea && textArea.value.trim().length > 0) {
                        log("检测到有文本内容，自动提交");
                        const submitBtn = document.getElementById('tts-chat-submit-button');
                        if (submitBtn) {
                            submitBtn.click();
                        }
                    } else {
                        log("文本框为空，10秒内没有识别到任何文字，触发沉默模式");
                        // 没有识别到任何文字，重新启动关键词监听
                        restartKeywordListening();
                    }
                }, 100);
            }
        }, SPEECH_TIMEOUT_MS);

        log(`已设置${SPEECH_TIMEOUT_MS}毫秒的沉默检测（检查文本框是否有内容）`);

        // 同时启动长时间无语音检测
        startLongSilenceDetection();
    }

    /**
     * 清除语音超时检测
     */
    function clearSpeechTimeoutDetection() {
        if (speechTimeoutId) {
            clearTimeout(speechTimeoutId);
            speechTimeoutId = null;
        }
    }

    // 添加定期检查和状态同步函数
    function startPeriodicStateCheck() {
        // 每500毫秒检查一次状态
        setInterval(function() {
            // 检查TTS播放状态和语音识别状态是否一致
            if (isTTSPlaying && window.isRecognizing) {
                log("检测到异常状态：TTS播放时语音识别仍在运行，尝试停止语音识别");
                safelyStopSpeechRecognition();
            }
            
            // 同步全局状态
            if (window.autoConversationState) {
                window.autoConversationState.isTTSPlaying = isTTSPlaying;
            }
            
            // 检查是否需要进入沉默模式 - 只依赖于最后语音活动时间
            if (!window.autoConversationState.inSilentMode && !isTTSPlaying) {
                const timeSinceActivity = Date.now() - lastSpeechActivityTime;
                
                // 超过10秒无语音活动，进入沉默模式
                if (timeSinceActivity >= 10000 && lastSpeechActivityTime > 0) {
                    log(`定期检查发现已${timeSinceActivity}毫秒没有语音活动，进入沉默模式`);
                    enterSilentMode("超过10秒无语音活动");
                }
            }
        }, 500);
        
        // 专门负责检测沉默模式的定时器，每1秒检查一次
        setInterval(function() {
            // 如果已经在沉默模式，不需要检查
            if (window.autoConversationState.inSilentMode) {
                return;
            }
            
            // 如果TTS正在播放，不检查
            if (isTTSPlaying) {
                // 更新最后语音活动时间，因为TTS播放也是一种语音活动
                lastSpeechActivityTime = Date.now();
                return;
            }
            
            // 检查上次语音活动时间，无论语音识别是否在运行
            const timeSinceActivity = Date.now() - lastSpeechActivityTime;
            
            // 如果超过10秒没有语音活动，进入沉默模式
            if (timeSinceActivity >= 10000 && lastSpeechActivityTime > 0) {
                // 检查是否超过9秒时进行一次警告
                if (timeSinceActivity >= 9000 && timeSinceActivity < 9500) {
                    log(`警告：已${timeSinceActivity}毫秒没有语音活动，即将进入沉默模式`);
                }
                
                // 超过10秒进入沉默模式
                if (timeSinceActivity >= 10000) {
                    log(`已${timeSinceActivity}毫秒没有语音活动，进入沉默模式`);
                    enterSilentMode("超过10秒无语音活动");
                }
            }
        }, 1000);
        
        log("已启动定期状态检查");
    }

    // 添加长时间无语音检测函数
    function startLongSilenceDetection() {
        // 如果TTS正在播放，更新语音活动时间但不启动检测
        if (isTTSPlaying || (window.autoConversationState && window.autoConversationState.isTTSPlaying)) {
            log("TTS正在播放中，更新语音活动时间但不启动长时间无语音检测");
            lastSpeechActivityTime = Date.now(); // 更新语音活动时间
                            return;
                        }
        
        // 如果已经处于沉默模式，不启动检测
        if (window.autoConversationState.inSilentMode) {
            log("已处于沉默模式，不启动长时间无语音检测");
            return;
        }
        
        // 清除之前的定时器
        clearLongSilenceDetection();
        
        // 更新最后语音活动时间
        lastSpeechActivityTime = Date.now();
        
        // 设置新的定时器 - 在定时器中监测整个10秒周期
        longSilenceTimeoutId = setTimeout(() => {
            // 再次检查TTS播放状态
            if (isTTSPlaying || (window.autoConversationState && window.autoConversationState.isTTSPlaying)) {
                log("长时间无语音检测触发时检测到TTS正在播放，不执行沉默操作");
                return;
            }
            
            // 再次检查沉默模式
            if (window.autoConversationState.inSilentMode) {
                log("长时间无语音检测触发时已处于沉默模式，不重复操作");
                return;
            }
            
            const idleTime = Date.now() - lastSpeechActivityTime;
            
            // 确保真的已经10秒无活动
            if (idleTime >= 10000) {
                log(`已${idleTime}毫秒没有语音活动，进入沉默模式`);
                
                // 使用统一的进入沉默模式函数
                enterSilentMode("长时间无语音检测");
            } else {
                log(`长时间无语音检测触发，但实际无活动时间只有${idleTime}毫秒，不满足条件`);
                // 如果不满足条件，可能是有中间活动，重新启动检测
                startLongSilenceDetection();
            }
        }, LONG_SILENCE_TIMEOUT_MS);
        
        log(`已设置${LONG_SILENCE_TIMEOUT_MS}毫秒的长时间无语音检测`);
    }

    /**
     * 清除长时间无语音检测定时器
     */
    function clearLongSilenceDetection() {
        if (longSilenceTimeoutId) {
            clearTimeout(longSilenceTimeoutId);
            longSilenceTimeoutId = null;
        }
    }

    /**
     * 进入沉默模式，需要通过关键词唤醒
     * @param {string} reason 进入沉默模式的原因
     */
    function enterSilentMode(reason) {
        // 如果已经处于沉默模式，不重复进入
        if (window.autoConversationState.inSilentMode) {
            log("已处于沉默模式，不重复进入");
                        return;
                    }
        
        // 如果TTS正在播放，不进入沉默模式
        if (isTTSPlaying || window.autoConversationState.isTTSPlaying) {
            log("TTS正在播放，不进入沉默模式");
            return;
        }
        
        log(`进入沉默模式，原因：${reason}`);
        
        // 停止语音识别
        safelyStopSpeechRecognition();
        
        // 记录进入沉默模式的时间
        window.autoConversationState.silentModeStartTime = Date.now();
        
        // 设置沉默模式标志
        window.autoConversationState.inSilentMode = true;
        
        // 启动关键词唤醒监听
        startKeywordListening();
        
        // 显示提示信息
        showSilentModeNotification();
        
        // 更新UI状态
        try {
            const silentBtn = document.getElementById('silent-mode-toggle');
            if (silentBtn) {
                silentBtn.style.backgroundColor = '#dc3545';
                silentBtn.textContent = '🔇';
                silentBtn.title = '退出沉默模式';
            }
        } catch (e) {
            log("更新沉默模式按钮状态失败: " + e.toString());
        }
    }

    // 显示沉默模式提示
    function showSilentModeNotification() {
        try {
            // 创建一个浮动提示
            const notification = document.createElement('div');
            notification.id = 'silent-mode-notification';
            notification.textContent = '已进入沉默模式，请说"你好"唤醒';
            notification.style.cssText = `
                position: fixed;
                bottom: 80px;
                left: 50%;
                transform: translateX(-50%);
                background-color: rgba(0, 0, 0, 0.7);
                color: white;
                padding: 10px 20px;
                border-radius: 20px;
                font-size: 14px;
                z-index: 9999;
                transition: opacity 0.5s;
            `;
            
            // 添加到页面
            document.body.appendChild(notification);
            
            // 3秒后淡出
            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 500);
            }, 3000);
                } catch (e) {
            log("显示沉默模式提示失败: " + e.toString());
        }
    }

    // 启动关键词唤醒监听
    function startKeywordListening() {
        // 先确保语音识别已停止
        safelyStopSpeechRecognition();
        
        // 检查是否存在关键词唤醒功能
        if (typeof window.keywordWakeup === 'object' && typeof window.keywordWakeup.start === 'function') {
            log("通过全局对象启动关键词唤醒监听");
            try {
                // 尝试先停止已有监听，避免重复监听
                if (typeof window.keywordWakeup.stop === 'function') {
                    window.keywordWakeup.stop();
                }
                // 确保沉默模式标志已设置
                window.autoConversationState.inSilentMode = true;
                // 然后启动
                window.keywordWakeup.start();
                log("成功启动关键词唤醒监听");
            } catch (e) {
                log("启动关键词唤醒监听失败: " + e.toString());
            }
        } else {
            log("未找到关键词唤醒功能，尝试加载");
            
            // 检查是否已加载keyword-wakeup.js
            if (!document.querySelector('script[src*="keyword-wakeup.js"]')) {
                log("尝试动态加载关键词唤醒脚本");
                
                const script = document.createElement('script');
                script.src = '/js/keyword-wakeup.js';
                script.onload = function() {
                    log("关键词唤醒脚本加载成功");
                    if (typeof window.keywordWakeup === 'object' && typeof window.keywordWakeup.start === 'function') {
                        // 确保沉默模式标志已设置
                        window.autoConversationState.inSilentMode = true;
                        window.keywordWakeup.start();
                        log("成功启动关键词唤醒监听（延迟加载）");
                    } else {
                        log("加载脚本后仍未找到keywordWakeup对象");
                    }
                };
                script.onerror = function() {
                    log("关键词唤醒脚本加载失败");
                };
                
                document.head.appendChild(script);
            } else {
                // 脚本已加载但对象不存在，尝试重新加载
                log("关键词唤醒脚本已加载，但未找到keywordWakeup对象，尝试重新加载");
                const existingScript = document.querySelector('script[src*="keyword-wakeup.js"]');
                if (existingScript && existingScript.parentNode) {
                    existingScript.parentNode.removeChild(existingScript);
                }
                
                // 短暂延迟后再次尝试加载
                setTimeout(() => {
                    const newScript = document.createElement('script');
                    newScript.src = '/js/keyword-wakeup.js?t=' + Date.now(); // 添加时间戳防止缓存
                    newScript.onload = function() {
                        if (typeof window.keywordWakeup === 'object' && typeof window.keywordWakeup.start === 'function') {
                            // 确保沉默模式标志已设置
                            window.autoConversationState.inSilentMode = true;
                            window.keywordWakeup.start();
                            log("成功启动关键词唤醒监听（重新加载）");
                        } else {
                            log("重新加载脚本后仍未找到keywordWakeup对象");
                        }
                    };
                    document.head.appendChild(newScript);
                }, 500);
            }
        }
    }

    // 创建沉默模式切换按钮
    function createSilentModeToggle() {
        try {
            // 创建一个浮动按钮，用于手动切换沉默模式
            const silentBtn = document.createElement('button');
            silentBtn.id = 'silent-mode-toggle';
            silentBtn.textContent = '🔇';
            silentBtn.title = '切换沉默模式';
            silentBtn.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 80px;
                z-index: 9999;
                width: 50px;
                height: 50px;
                border-radius: 25px;
                background-color: #6c757d;
                color: white;
                border: none;
                box-shadow: 0 2px 5px rgba(0,0,0,0.3);
                cursor: pointer;
                font-size: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background-color 0.3s;
            `;
            
            // 更新按钮状态
            function updateButtonState() {
                if (window.autoConversationState.inSilentMode) {
                    silentBtn.style.backgroundColor = '#dc3545';
                    silentBtn.textContent = '🔇';
                    silentBtn.title = '退出沉默模式';
                } else {
                    silentBtn.style.backgroundColor = '#6c757d';
                    silentBtn.textContent = '🔊';
                    silentBtn.title = '进入沉默模式';
                }
            }
            
            // 点击事件处理
            silentBtn.addEventListener('click', function() {
                // 切换沉默模式
                window.autoConversationState.inSilentMode = !window.autoConversationState.inSilentMode;
                
                if (window.autoConversationState.inSilentMode) {
                    log("手动进入沉默模式");
                    // 停止语音识别
                    safelyStopSpeechRecognition();
                    // 启动关键词唤醒监听
                    startKeywordListening();
                    // 显示提示
                    showSilentModeNotification();
                } else {
                    log("手动退出沉默模式");
                    // 触发麦克风
                    triggerMicrophoneAfterDelay(100);
                }
                
                // 更新按钮状态
                updateButtonState();
            });
            
            // 添加到页面
            document.body.appendChild(silentBtn);
            
            // 初始化按钮状态
            updateButtonState();
            
            log("已添加沉默模式切换按钮");
        } catch (e) {
            log("添加沉默模式切换按钮失败: " + e.toString());
        }
    }
})(); 