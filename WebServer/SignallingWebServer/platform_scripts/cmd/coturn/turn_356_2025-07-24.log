0: : log file opened: turn_356_2025-07-24.log
0: : Config file found: /coturn/..\..\..\turnserver.conf
0: : Black listing: 0.0.0.0-0.255.255.255
0: : Black listing: 10.0.0.0-10.255.255.255
0: : Black listing: 100.64.0.0-100.127.255.255
0: : Black listing: 127.0.0.0-127.255.255.255
0: : Black listing: ***********-***************
0: : Black listing: **********-**************
0: : Black listing: *********-***********
0: : Black listing: *********-***********
0: : Black listing: ***********-*************
0: : Black listing: ***********-***************
0: : Black listing: **********-**************
0: : Black listing: ************-**************
0: : Black listing: ***********-*************
0: : Black listing: 240.0.0.0-***************
0: : White listing: ************
0: : Relay address to use: ************
0: : Listener address to use: ************
0: : Config file found: /coturn/..\..\..\turnserver.conf
0: : 
RFC 3489/5389/5766/5780/6062/6156 STUN/TURN Server
Version Coturn-4.5.2 'dan Eider'
0: : 
Max number of open files/sockets allowed for this process: 3200
0: : 
Due to the open files/sockets limitation,
max supported number of TURN Sessions possible is: 1000 (approximately)
0: : 

==== Show him the instruments, Practical Frost: ====

0: : TLS supported
0: : DTLS supported
0: : DTLS 1.2 supported
0: : TURN/STUN ALPN supported
0: : Third-party authorization (oAuth) supported
0: : GCM (AEAD) supported
0: : OpenSSL compile-time version: OpenSSL 1.1.1f  31 Mar 2020 (0x1010106f)
0: : 
0: : SQLite is not supported
0: : Redis is not supported
0: : PostgreSQL is not supported
0: : MySQL is not supported
0: : MongoDB is not supported
0: : 
0: : Default Net Engine version: 2 (UDP thread per network endpoint)

=====================================================

0: : Domain name: 
0: : Default realm: PixelStreaming
1: : pid file created: C:\coturn.pid
1: : IO method (main listener thread): poll
1: : IPv4: On this platform, I am using alternative behavior of TTL according to RFC 5766.
1: : IPv6: On this platform, I am using alternative behavior of TTL (HOPLIMIT) according to RFC 6156.
1: : IPv4: On this platform, I am using alternative behavior of TOS according to RFC 5766.
1: : WARNING: I cannot support STUN CHANGE_REQUEST functionality because only one IP address is provided
1: : Wait for relay ports initialization...
1: :   relay ************ initialization...
1: :   relay ************ initialization done
1: : Relay ports initialization done
1: : IO method (general relay thread): poll
1: : turn server id=0 created
1: : IO method (general relay thread): poll
1: : turn server id=1 created
1: : IO method (general relay thread): poll
1: : turn server id=2 created
1: : IO method (general relay thread): poll
1: : turn server id=3 created
1: : IO method (general relay thread): poll
1: : turn server id=4 created
1: : IO method (general relay thread): poll
1: : turn server id=5 created
1: : IO method (general relay thread): poll
1: : turn server id=6 created
1: : IO method (general relay thread): poll
1: : turn server id=7 created
1: : IO method (general relay thread): poll
1: : turn server id=8 created
1: : IO method (general relay thread): poll
1: : turn server id=9 created
1: : IO method (general relay thread): poll
1: : turn server id=10 created
1: : IO method (general relay thread): poll
1: : turn server id=11 created
1: : IO method (general relay thread): poll
1: : turn server id=12 created
1: : IO method (general relay thread): poll
1: : turn server id=13 created
1: : IO method (general relay thread): poll
1: : turn server id=14 created
1: : IO method (general relay thread): poll
1: : turn server id=15 created
1: : IO method (general relay thread): poll
1: : turn server id=16 created
1: : IO method (general relay thread): poll
1: : turn server id=17 created
1: : IO method (general relay thread): poll
1: : turn server id=18 created
1: : IO method (general relay thread): poll
1: : turn server id=19 created
1: : IO method (general relay thread): poll
1: : turn server id=20 created
1: : IO method (general relay thread): poll
1: : turn server id=21 created
1: : IO method (general relay thread): poll
1: : turn server id=22 created
1: : IO method (general relay thread): poll
1: : turn server id=23 created
1: : IO method (general relay thread): poll
1: : turn server id=24 created
1: : IO method (general relay thread): poll
1: : turn server id=25 created
1: : IO method (general relay thread): poll
1: : turn server id=26 created
1: : IO method (general relay thread): poll
1: : turn server id=27 created
1: : IO method (general relay thread): poll
1: : turn server id=28 created
1: : IO method (general relay thread): poll
1: : turn server id=29 created
1: : IO method (general relay thread): poll
1: : turn server id=30 created
1: : IO method (udp listener/relay thread): poll
1: : IO method (general relay thread): poll
1: : turn server id=31 created
1: : turn server id=128 created
1: : IPv4. UDP listener opened on: ************:19303
1: : IPv4. TCP listener opened on : ************:19303
1: : Total UDP servers: 1
1: : Total General servers: 32
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (auth thread): poll
1: : IO method (admin thread): poll
