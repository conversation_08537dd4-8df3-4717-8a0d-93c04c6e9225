# Copyright Digital Domain, Inc. All Rights Reserved.

# build MomentumCloud frontend
if ($args -contains "--build") {
    . "$PSScriptRoot\Start_Common.ps1" $args --build --MomentumCloud
} else {
    . "$PSScriptRoot\Start_Common.ps1" $args --MomentumCloud
}

set_start_default_values "y" "y" # Set both TURN and STUN server defaults
use_args($args)
print_parameters

Push-Location $PSScriptRoot

# --------------------------------------------------------------------------- #
# Embed Start_TURNServer.ps1 script and don't run "Start_Common.ps1" again.
# --------------------------------------------------------------------------- #
$LocalIP = (Test-Connection -ComputerName (hostname) -Count 1  | Select IPV4Address).IPV4Address.IPAddressToString

Write-Output "Private IP: $LocalIP"

$TurnPort="19303"
$Pos = $global:TurnServer.LastIndexOf(":")
if ($Pos -ne -1) {
 $TurnPort = $global:TurnServer.Substring($Pos+ 1)
}
echo "TURN port: ${turnport}"
echo ""

Push-Location $PSScriptRoot

$TurnUsername = "PixelStreamingUser"
$TurnPassword = "AnotherTURNintheroad"
$Realm = "PixelStreaming"
$ProcessExe = ".\turnserver.exe"
$Arguments = "-c ..\..\..\turnserver.conf --allowed-peer-ip=$LocalIP -p $TurnPort -r $Realm -X $PublicIP -E $LocalIP -L $LocalIP --no-cli --no-tls --no-dtls --pidfile `"C:\coturn.pid`" -f -a -v -u $TurnUsername`:$TurnPassword"

# Add arguments passed to script to Arguments for executable
$Arguments += $args

Push-Location $PSScriptRoot\coturn\
Write-Output "Running: $ProcessExe $Arguments"
# pause
Start-Process -FilePath $ProcessExe -ArgumentList $Arguments #-NoNewWindow # DHTCL_MODIFY. Run turnserver in new windows
# DHTCL_MODIFY End
Pop-Location

Pop-Location
# --------------------------------------------------------------------------- #

$peerConnectionOptions = "{ \""iceServers\"": [{\""urls\"": [\""stun:" + $global:StunServer + "\"",\""turn:" + $global:TurnServer + "\""], \""username\"": \""PixelStreamingUser\"", \""credential\"": \""AnotherTURNintheroad\""}] }"

$ProcessExe = "platform_scripts\cmd\node\node.exe"
$Arguments = @("cirrus", "--peerConnectionOptions=""$peerConnectionOptions""", "--PublicIp=$global:PublicIp")
# Add arguments passed to script to Arguments for executable
$Arguments += $args

Push-Location $PSScriptRoot\..\..\
Write-Output "Running: $ProcessExe $Arguments"
Start-Process -FilePath $ProcessExe -ArgumentList $Arguments -Wait -NoNewWindow
Pop-Location

Pop-Location