/**
 * Azure语音识别配置API
 * 为前端提供Azure Speech服务配置
 */

const fs = require('fs');
const path = require('path');

/**
 * 读取配置文件
 */
function readConfig() {
    try {
        const configPath = path.join(__dirname, '../../Renderer/VirtualHuman/Content/Settings/mCloudConfig.ini');
        const configContent = fs.readFileSync(configPath, 'utf8');
        
        const config = {};
        let currentSection = '';
        
        configContent.split('\n').forEach(line => {
            line = line.trim();
            
            // 跳过注释和空行
            if (line.startsWith(';') || line.startsWith('#') || !line) {
                return;
            }
            
            // 处理节
            if (line.startsWith('[') && line.endsWith(']')) {
                currentSection = line.slice(1, -1);
                config[currentSection] = {};
                return;
            }
            
            // 处理键值对
            const equalIndex = line.indexOf('=');
            if (equalIndex > 0 && currentSection) {
                const key = line.substring(0, equalIndex).trim();
                let value = line.substring(equalIndex + 1).trim();
                
                // 移除引号
                if ((value.startsWith('"') && value.endsWith('"')) || 
                    (value.startsWith("'") && value.endsWith("'"))) {
                    value = value.slice(1, -1);
                }
                
                config[currentSection][key] = value;
            }
        });
        
        return config;
    } catch (error) {
        console.error('读取配置文件失败:', error);
        return {};
    }
}

/**
 * 获取语音识别配置
 */
function getSpeechRecognitionConfig() {
    const config = readConfig();
    
    // 从TTS配置中获取Azure信息（向后兼容）
    const ttsConfig = config.TTS || {};
    const speechConfig = config.SpeechRecognition || {};
    
    return {
        engine: speechConfig.SpeechEngine || '0', // 默认使用WebKit
        subscriptionKey: speechConfig.AzureSpeechKey || ttsConfig.AzureSubscriptionKey || '',
        region: speechConfig.AzureSpeechRegion || ttsConfig.AzureRegion || 'swedencentral',
        language: speechConfig.AzureSpeechLanguage || 'zh-CN'
    };
}

/**
 * Express路由处理器
 */
function setupSpeechConfigRoutes(app) {
    // 获取语音识别配置
    app.get('/api/speech-config', (req, res) => {
        try {
            const config = getSpeechRecognitionConfig();
            
            // 不返回完整的订阅密钥，只返回是否配置了
            const safeConfig = {
                engine: config.engine,
                hasSubscriptionKey: !!config.subscriptionKey,
                region: config.region,
                language: config.language
            };
            
            // 如果是本地请求，返回完整配置
            const isLocalRequest = req.ip === '127.0.0.1' || 
                                 req.ip === '::1' || 
                                 req.ip.startsWith('192.168.') ||
                                 req.ip.startsWith('10.') ||
                                 req.hostname === 'localhost';
            
            if (isLocalRequest) {
                safeConfig.subscriptionKey = config.subscriptionKey;
            }
            
            res.json(safeConfig);
        } catch (error) {
            console.error('获取语音配置失败:', error);
            res.status(500).json({ error: '获取配置失败' });
        }
    });
    
    // 测试Azure连接
    app.post('/api/speech-test', async (req, res) => {
        try {
            const config = getSpeechRecognitionConfig();
            
            if (!config.subscriptionKey) {
                return res.status(400).json({ error: 'Azure订阅密钥未配置' });
            }
            
            // 这里可以添加实际的Azure连接测试
            // 暂时返回成功
            res.json({ 
                success: true, 
                message: 'Azure语音服务配置正常',
                region: config.region,
                language: config.language
            });
            
        } catch (error) {
            console.error('测试Azure连接失败:', error);
            res.status(500).json({ error: '测试连接失败' });
        }
    });
}

module.exports = {
    setupSpeechConfigRoutes,
    getSpeechRecognitionConfig
};
