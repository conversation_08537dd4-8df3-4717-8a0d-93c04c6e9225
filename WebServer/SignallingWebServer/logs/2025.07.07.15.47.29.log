15:47:29.365 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
15:47:29.404 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
15:47:29.405 Redirecting http->https
15:47:29.411 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
15:47:29.418 WebSocket listening for Streamer connections on :8888
15:47:29.419 WebSocket listening for SFU connections on :8889
15:47:29.420 WebSocket listening for Players connections on :80
15:47:29.421 Http listening on *: 80
15:47:29.421 Https listening on *: 443
15:49:11.303 Streamer connected: ::1
15:49:11.303 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:49:11.304 [37m::1 <-[32m {"type":"identify"}
15:49:11.838 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:49:11.839 Registered new streamer: Streamer Component
15:49:22.763 player 1 (::ffff:*************) connected
15:49:22.764 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:49:22.764 [37m[players] <-[32m {"type":"playerCount","count":1}
15:49:22.764 [37m1 ->[34m {"type":"listStreamers"}
15:49:22.765 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
15:49:22.784 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
15:49:22.784 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
15:49:22.865 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 4881666386481045019 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Q4K2\r\na=ice-pwd:AfZVvpQbwUlBqPyee+WSmbnZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 17:90:DD:3F:6E:10:4B:70:65:6E:2A:74:17:60:33:60:2A:A0:11:C8:30:9F:F2:F0:DE:F8:FE:6B:8E:19:11:F4\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2506636303 2422124333\r\na=ssrc:2506636303 cname:2AJayNUyFiXNJDOy\r\na=ssrc:2506636303 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2422124333 cname:2AJayNUyFiXNJDOy\r\na=ssrc:2422124333 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Q4K2\r\na=ice-pwd:AfZVvpQbwUlBqPyee+WSmbnZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 17:90:DD:3F:6E:10:4B:70:65:6E:2A:74:17:60:33:60:2A:A0:11:C8:30:9F:F2:F0:DE:F8:FE:6B:8E:19:11:F4\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1313341150 cname:2AJayNUyFiXNJDOy\r\na=ssrc:1313341150 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Q4K2\r\na=ice-pwd:AfZVvpQbwUlBqPyee+WSmbnZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 17:90:DD:3F:6E:10:4B:70:65:6E:2A:74:17:60:33:60:2A:A0:11:C8:30:9F:F2:F0:DE:F8:FE:6B:8E:19:11:F4\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:49:22.867 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3107787159 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag Q4K2 network-id 1"}}
15:49:22.890 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 3885239488760556715 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:heDj\r\na=ice-pwd:70Mtz9GhYiuIpcIymZKEi6bn\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F2:B9:A7:71:2A:70:67:65:E7:07:A3:AB:E7:B0:C2:D5:48:03:5F:A1:8A:38:C5:17:CB:4A:09:FF:6A:D7:B0:54\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:heDj\r\na=ice-pwd:70Mtz9GhYiuIpcIymZKEi6bn\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F2:B9:A7:71:2A:70:67:65:E7:07:A3:AB:E7:B0:C2:D5:48:03:5F:A1:8A:38:C5:17:CB:4A:09:FF:6A:D7:B0:54\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 74356374-2208-40b5-8464-d26d9061a484\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:489108459 cname:Q0wHsqKLO7xQIvGV\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:heDj\r\na=ice-pwd:70Mtz9GhYiuIpcIymZKEi6bn\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F2:B9:A7:71:2A:70:67:65:E7:07:A3:AB:E7:B0:C2:D5:48:03:5F:A1:8A:38:C5:17:CB:4A:09:FF:6A:D7:B0:54\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:49:22.891 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1656588088 1 udp 2122260223 ************* 60433 typ host generation 0 ufrag heDj network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"heDj"}}
15:49:22.892 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1436848316 1 udp 2122194687 ************ 60434 typ host generation 0 ufrag heDj network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"heDj"}}
15:49:22.893 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3813772288 1 udp 2122129151 *************** 60435 typ host generation 0 ufrag heDj network-id 3","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"heDj"}}
15:49:22.898 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1045954930 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag Q4K2 network-id 4"}}
15:49:22.932 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3107787159 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag Q4K2 network-id 1"}}
15:49:22.966 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1045954930 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag Q4K2 network-id 4"}}
15:49:22.999 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3107787159 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag Q4K2 network-id 1"}}
15:49:23.033 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1045954930 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag Q4K2 network-id 4"}}
15:50:11.937 [37mStreamer Component ->[34m {"type":"ping","time":1754553011}
15:50:19.241 player 1 connection closed: 1001 - 
15:50:19.242 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
15:50:19.242 [37m[players] <-[32m {"type":"playerCount","count":0}
15:50:21.009 player 2 (::ffff:*************) connected
15:50:21.010 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:50:21.010 [37m[players] <-[32m {"type":"playerCount","count":1}
15:50:21.011 [37m2 ->[34m {"type":"listStreamers"}
15:50:21.011 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
15:50:21.029 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
15:50:21.030 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
15:50:21.096 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 345387533221831835 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:etjP\r\na=ice-pwd:snBkfgJfOT/7Le64W6M0QsbI\r\na=ice-options:trickle\r\na=fingerprint:sha-256 35:1C:C2:75:36:95:09:32:47:1B:AB:5E:D9:EC:B2:CD:5E:54:CE:7F:6D:36:01:C7:F7:EA:4B:41:4D:E1:36:FD\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1578639479 118472097\r\na=ssrc:1578639479 cname:EfACxBABY/5Swhxf\r\na=ssrc:1578639479 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:118472097 cname:EfACxBABY/5Swhxf\r\na=ssrc:118472097 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:etjP\r\na=ice-pwd:snBkfgJfOT/7Le64W6M0QsbI\r\na=ice-options:trickle\r\na=fingerprint:sha-256 35:1C:C2:75:36:95:09:32:47:1B:AB:5E:D9:EC:B2:CD:5E:54:CE:7F:6D:36:01:C7:F7:EA:4B:41:4D:E1:36:FD\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1056829075 cname:EfACxBABY/5Swhxf\r\na=ssrc:1056829075 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:etjP\r\na=ice-pwd:snBkfgJfOT/7Le64W6M0QsbI\r\na=ice-options:trickle\r\na=fingerprint:sha-256 35:1C:C2:75:36:95:09:32:47:1B:AB:5E:D9:EC:B2:CD:5E:54:CE:7F:6D:36:01:C7:F7:EA:4B:41:4D:E1:36:FD\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:50:21.120 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 7148588269554158118 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Ewxl\r\na=ice-pwd:1Co0+9uywe/tCJAethTC2ytD\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2F:D3:08:57:D9:C1:CD:51:05:A7:46:3C:67:0F:18:39:EA:32:24:A8:4F:46:F5:D5:91:96:8D:AC:BE:02:A7:FF\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Ewxl\r\na=ice-pwd:1Co0+9uywe/tCJAethTC2ytD\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2F:D3:08:57:D9:C1:CD:51:05:A7:46:3C:67:0F:18:39:EA:32:24:A8:4F:46:F5:D5:91:96:8D:AC:BE:02:A7:FF\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- f46713e3-651b-4755-b108-891e99e84896\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3684542784 cname:s0zARi07qTWvnQLk\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Ewxl\r\na=ice-pwd:1Co0+9uywe/tCJAethTC2ytD\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2F:D3:08:57:D9:C1:CD:51:05:A7:46:3C:67:0F:18:39:EA:32:24:A8:4F:46:F5:D5:91:96:8D:AC:BE:02:A7:FF\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:50:21.120 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1568345814 1 udp 2122260223 ************* 60652 typ host generation 0 ufrag Ewxl network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"Ewxl"}}
15:50:21.121 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1784813906 1 udp 2122194687 ************ 60653 typ host generation 0 ufrag Ewxl network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"Ewxl"}}
15:50:21.121 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3700884974 1 udp 2122129151 *************** 60654 typ host generation 0 ufrag Ewxl network-id 3","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"Ewxl"}}
15:50:21.129 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2829825382 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag etjP network-id 1"}}
15:50:21.163 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2018297473 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag etjP network-id 4"}}
15:50:21.196 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2829825382 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag etjP network-id 1"}}
15:50:21.230 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2018297473 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag etjP network-id 4"}}
15:50:21.264 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2829825382 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag etjP network-id 1"}}
15:50:21.297 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2018297473 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag etjP network-id 4"}}
15:51:11.951 [37mStreamer Component ->[34m {"type":"ping","time":1754553071}
15:52:11.945 [37mStreamer Component ->[34m {"type":"ping","time":1754553131}
15:53:11.910 [37mStreamer Component ->[34m {"type":"ping","time":1754553191}
15:54:11.965 [37mStreamer Component ->[34m {"type":"ping","time":1754553251}
15:54:47.334 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
15:54:47.335 unsubscribing all players on Streamer Component
15:54:47.336 player 2 connection closed: 1005 - 
15:54:47.336 [37m[players] <-[32m {"type":"playerCount","count":0}
