02:46:04.466 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "*************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
02:46:04.504 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:*************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
02:46:04.506 Redirecting http->https
02:46:04.511 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
02:46:04.518 WebSocket listening for Streamer connections on :8888
02:46:04.519 WebSocket listening for SFU connections on :8889
02:46:04.519 WebSocket listening for Players connections on :80
02:46:04.520 Http listening on *: 80
02:46:04.521 Https listening on *: 443
02:46:36.361 Streamer connected: ::1
02:46:36.361 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
02:46:36.362 [37m::1 <-[32m {"type":"identify"}
02:46:36.864 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
02:46:36.865 Registered new streamer: Streamer Component
02:47:36.959 [37mStreamer Component ->[34m {"type":"ping","time":1753642056}
02:48:36.939 [37mStreamer Component ->[34m {"type":"ping","time":1753642116}
02:49:36.913 [37mStreamer Component ->[34m {"type":"ping","time":1753642176}
02:50:36.961 [37mStreamer Component ->[34m {"type":"ping","time":1753642236}
02:51:36.976 [37mStreamer Component ->[34m {"type":"ping","time":1753642296}
02:52:36.972 [37mStreamer Component ->[34m {"type":"ping","time":1753642356}
02:53:36.943 [37mStreamer Component ->[34m {"type":"ping","time":1753642416}
02:54:36.948 [37mStreamer Component ->[34m {"type":"ping","time":1753642476}
02:55:36.918 [37mStreamer Component ->[34m {"type":"ping","time":1753642536}
02:56:36.932 [37mStreamer Component ->[34m {"type":"ping","time":1753642596}
02:57:36.946 [37mStreamer Component ->[34m {"type":"ping","time":1753642656}
02:58:36.964 [37mStreamer Component ->[34m {"type":"ping","time":1753642716}
02:59:36.924 [37mStreamer Component ->[34m {"type":"ping","time":1753642776}
03:00:36.928 [37mStreamer Component ->[34m {"type":"ping","time":1753642836}
03:01:36.965 [37mStreamer Component ->[34m {"type":"ping","time":1753642896}
03:02:36.977 [37mStreamer Component ->[34m {"type":"ping","time":1753642956}
03:03:36.959 [37mStreamer Component ->[34m {"type":"ping","time":1753643016}
03:04:36.927 [37mStreamer Component ->[34m {"type":"ping","time":1753643076}
03:05:36.930 [37mStreamer Component ->[34m {"type":"ping","time":1753643136}
03:06:36.957 [37mStreamer Component ->[34m {"type":"ping","time":1753643196}
03:06:39.899 player 1 (::1) connected
03:06:39.901 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:06:39.901 [37m[players] <-[32m {"type":"playerCount","count":1}
03:06:39.902 [37m1 ->[34m {"type":"listStreamers"}
03:06:39.902 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:06:39.933 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:06:39.934 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
03:06:40.015 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 5547915414247081685 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:GlRM\r\na=ice-pwd:Rrt536rSsjOLbZnghX8OOidB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 66:AB:CD:11:22:14:66:35:4E:1D:67:19:9C:E7:72:72:E0:58:2E:CB:A8:65:33:6D:55:47:35:DA:75:21:EE:86\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 147129466 3545469396\r\na=ssrc:147129466 cname:BF6DrlM9u6o0TRKC\r\na=ssrc:147129466 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3545469396 cname:BF6DrlM9u6o0TRKC\r\na=ssrc:3545469396 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:GlRM\r\na=ice-pwd:Rrt536rSsjOLbZnghX8OOidB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 66:AB:CD:11:22:14:66:35:4E:1D:67:19:9C:E7:72:72:E0:58:2E:CB:A8:65:33:6D:55:47:35:DA:75:21:EE:86\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2292192282 cname:BF6DrlM9u6o0TRKC\r\na=ssrc:2292192282 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:GlRM\r\na=ice-pwd:Rrt536rSsjOLbZnghX8OOidB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 66:AB:CD:11:22:14:66:35:4E:1D:67:19:9C:E7:72:72:E0:58:2E:CB:A8:65:33:6D:55:47:35:DA:75:21:EE:86\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:06:40.017 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4239026594 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag GlRM network-id 1"}}
03:06:40.049 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:250936400 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag GlRM network-id 4"}}
03:06:40.055 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 546480205954706398 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:TR6x\r\na=ice-pwd:BQhkdBX7GTm5d2B5RLwdmbez\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D9:D8:59:8E:9A:EB:65:AD:06:A1:39:97:92:E2:59:14:55:EE:2A:45:AD:D5:78:A4:0E:72:15:A8:AF:08:4D:73\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:TR6x\r\na=ice-pwd:BQhkdBX7GTm5d2B5RLwdmbez\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D9:D8:59:8E:9A:EB:65:AD:06:A1:39:97:92:E2:59:14:55:EE:2A:45:AD:D5:78:A4:0E:72:15:A8:AF:08:4D:73\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- d0a142a6-114c-4647-bcfc-df2ab0df3509\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:609310186 cname:3LVtIRRZhsu66jRn\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:TR6x\r\na=ice-pwd:BQhkdBX7GTm5d2B5RLwdmbez\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D9:D8:59:8E:9A:EB:65:AD:06:A1:39:97:92:E2:59:14:55:EE:2A:45:AD:D5:78:A4:0E:72:15:A8:AF:08:4D:73\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:06:40.057 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2723062409 1 udp 2122260223 ************ 61742 typ host generation 0 ufrag TR6x network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"TR6x"}}
03:06:40.057 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2236899266 1 udp 2122194687 ************* 61743 typ host generation 0 ufrag TR6x network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"TR6x"}}
03:06:40.082 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4239026594 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag GlRM network-id 1"}}
03:06:40.115 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:250936400 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag GlRM network-id 4"}}
03:06:40.149 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4239026594 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag GlRM network-id 1"}}
03:06:40.183 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:250936400 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag GlRM network-id 4"}}
03:06:40.216 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:33602870 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag GlRM network-id 1"}}
03:06:40.249 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4032702660 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag GlRM network-id 4"}}
03:06:40.283 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:33602870 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag GlRM network-id 1"}}
03:06:40.317 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4032702660 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag GlRM network-id 4"}}
03:06:40.351 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:33602870 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag GlRM network-id 1"}}
03:06:40.384 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4032702660 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag GlRM network-id 4"}}
03:06:40.418 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:782691373 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag GlRM network-id 1"}}
03:07:23.727 player 1 connection closed: 1001 - 
03:07:23.732 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
03:07:23.733 [37m[players] <-[32m {"type":"playerCount","count":0}
03:07:26.836 player 2 (::1) connected
03:07:26.838 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:07:26.839 [37m[players] <-[32m {"type":"playerCount","count":1}
03:07:26.841 [37m2 ->[34m {"type":"listStreamers"}
03:07:26.841 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:07:26.872 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:07:26.873 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
03:07:26.924 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 8882837050698270909 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:EjWU\r\na=ice-pwd:eBco3tEq7Taee8E7UtdMoaWm\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:B0:9C:89:45:79:E0:59:5D:5B:40:26:4E:56:20:B1:87:0B:46:20:F6:02:B6:70:7F:1B:75:94:93:A6:19:2A\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3253709627 2510077987\r\na=ssrc:3253709627 cname:Ygotr5/0VIrgEU3W\r\na=ssrc:3253709627 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2510077987 cname:Ygotr5/0VIrgEU3W\r\na=ssrc:2510077987 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:EjWU\r\na=ice-pwd:eBco3tEq7Taee8E7UtdMoaWm\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:B0:9C:89:45:79:E0:59:5D:5B:40:26:4E:56:20:B1:87:0B:46:20:F6:02:B6:70:7F:1B:75:94:93:A6:19:2A\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:638967121 cname:Ygotr5/0VIrgEU3W\r\na=ssrc:638967121 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:EjWU\r\na=ice-pwd:eBco3tEq7Taee8E7UtdMoaWm\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:B0:9C:89:45:79:E0:59:5D:5B:40:26:4E:56:20:B1:87:0B:46:20:F6:02:B6:70:7F:1B:75:94:93:A6:19:2A\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:07:26.957 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 8902470661768842443 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:TzAH\r\na=ice-pwd:DkbdQfqwGrWqeyXnxIabTceW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EE:EC:46:52:36:68:11:26:B2:01:DD:3E:29:0F:5D:8F:AE:30:14:50:1A:F4:31:D2:8C:66:07:05:15:D5:BB:A6\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:TzAH\r\na=ice-pwd:DkbdQfqwGrWqeyXnxIabTceW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EE:EC:46:52:36:68:11:26:B2:01:DD:3E:29:0F:5D:8F:AE:30:14:50:1A:F4:31:D2:8C:66:07:05:15:D5:BB:A6\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 96fe45d7-a468-4a5e-8895-18d4a0a819d4\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2770032264 cname:l6bn2+im2WCRSdei\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:TzAH\r\na=ice-pwd:DkbdQfqwGrWqeyXnxIabTceW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EE:EC:46:52:36:68:11:26:B2:01:DD:3E:29:0F:5D:8F:AE:30:14:50:1A:F4:31:D2:8C:66:07:05:15:D5:BB:A6\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:07:26.959 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:4292286470 1 udp 2122260223 ************ 64747 typ host generation 0 ufrag TzAH network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"TzAH"}}
03:07:26.961 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3637372237 1 udp 2122194687 ************* 64748 typ host generation 0 ufrag TzAH network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"TzAH"}}
03:07:26.961 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2961212314 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag EjWU network-id 1"}}
03:07:26.991 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2350202825 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag EjWU network-id 4"}}
03:07:27.024 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2961212314 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag EjWU network-id 1"}}
03:07:27.057 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2350202825 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag EjWU network-id 4"}}
03:07:27.091 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2961212314 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag EjWU network-id 1"}}
03:07:27.125 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2350202825 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag EjWU network-id 4"}}
03:07:27.158 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3461308674 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag EjWU network-id 1"}}
03:07:27.192 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4074429777 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag EjWU network-id 4"}}
03:07:36.968 [37mStreamer Component ->[34m {"type":"ping","time":1753643256}
03:08:36.938 [37mStreamer Component ->[34m {"type":"ping","time":1753643316}
03:09:36.928 [37mStreamer Component ->[34m {"type":"ping","time":1753643376}
03:10:36.931 [37mStreamer Component ->[34m {"type":"ping","time":1753643436}
03:11:36.939 [37mStreamer Component ->[34m {"type":"ping","time":1753643496}
03:12:36.923 [37mStreamer Component ->[34m {"type":"ping","time":1753643556}
03:13:36.979 [37mStreamer Component ->[34m {"type":"ping","time":1753643616}
03:13:38.287 player 2 connection closed: 1001 - 
03:13:38.292 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"2"}
03:13:38.293 [37m[players] <-[32m {"type":"playerCount","count":0}
03:13:41.428 player 3 (::1) connected
03:13:41.429 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:13:41.430 [37m[players] <-[32m {"type":"playerCount","count":1}
03:13:41.430 [37m3 ->[34m {"type":"listStreamers"}
03:13:41.430 [37m3 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:13:41.456 [37m3 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:13:41.457 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"3","dataChannel":true,"sfu":false,"sendOffer":true}
03:13:41.523 [37mStreamer Component -> 3[36m {"type":"offer","playerId":3,"sdp":"v=0\r\no=- 6980828175150614438 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uXqv\r\na=ice-pwd:rWtoai+j7SMxSSFX7889Zi5H\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:36:69:09:78:6E:7D:7A:F6:61:8C:2B:6B:52:5E:24:3F:E2:B8:39:2B:18:9F:8A:82:8B:F8:E1:77:39:84:DC\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3505988316 2725190870\r\na=ssrc:3505988316 cname:fkkeeoflEKAK0xx5\r\na=ssrc:3505988316 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2725190870 cname:fkkeeoflEKAK0xx5\r\na=ssrc:2725190870 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uXqv\r\na=ice-pwd:rWtoai+j7SMxSSFX7889Zi5H\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:36:69:09:78:6E:7D:7A:F6:61:8C:2B:6B:52:5E:24:3F:E2:B8:39:2B:18:9F:8A:82:8B:F8:E1:77:39:84:DC\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:966183607 cname:fkkeeoflEKAK0xx5\r\na=ssrc:966183607 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uXqv\r\na=ice-pwd:rWtoai+j7SMxSSFX7889Zi5H\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:36:69:09:78:6E:7D:7A:F6:61:8C:2B:6B:52:5E:24:3F:E2:B8:39:2B:18:9F:8A:82:8B:F8:E1:77:39:84:DC\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:13:41.556 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:146196690 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag uXqv network-id 1"}}
03:13:41.557 [37m3 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 280285809380838433 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:f/Iw\r\na=ice-pwd:bNUC8m5hgVV3zsGd+OGvoy+W\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9C:F7:E5:F0:E6:1B:BD:09:55:1C:28:4C:C7:55:57:FD:85:4F:AC:27:F6:72:E5:18:84:BB:BE:23:78:39:C7:A3\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:f/Iw\r\na=ice-pwd:bNUC8m5hgVV3zsGd+OGvoy+W\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9C:F7:E5:F0:E6:1B:BD:09:55:1C:28:4C:C7:55:57:FD:85:4F:AC:27:F6:72:E5:18:84:BB:BE:23:78:39:C7:A3\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 83e5f1ab-6413-4f08-b0e0-0de9d4f401bc\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:570831039 cname:9QUxao+wi3eOOGNh\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:f/Iw\r\na=ice-pwd:bNUC8m5hgVV3zsGd+OGvoy+W\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9C:F7:E5:F0:E6:1B:BD:09:55:1C:28:4C:C7:55:57:FD:85:4F:AC:27:F6:72:E5:18:84:BB:BE:23:78:39:C7:A3\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:13:41.559 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:993725494 1 udp 2122260223 ************ 63215 typ host generation 0 ufrag f/Iw network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"f/Iw"}}
03:13:41.559 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3658649299 1 udp 2122194687 ************* 63216 typ host generation 0 ufrag f/Iw network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"f/Iw"}}
03:13:41.589 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2666416886 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag uXqv network-id 4"}}
03:13:41.622 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:146196690 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag uXqv network-id 1"}}
03:13:41.656 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2666416886 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag uXqv network-id 4"}}
03:13:41.689 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:146196690 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag uXqv network-id 1"}}
03:13:41.723 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2666416886 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag uXqv network-id 4"}}
03:13:41.757 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1982393823 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag uXqv network-id 1"}}
03:13:41.790 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3765465083 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag uXqv network-id 4"}}
03:14:14.253 player 3 connection closed: 1001 - 
03:14:14.255 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"3"}
03:14:14.261 [37m[players] <-[32m {"type":"playerCount","count":0}
03:14:22.846 player 4 (::1) connected
03:14:22.847 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:14:22.847 [37m[players] <-[32m {"type":"playerCount","count":1}
03:14:22.849 [37m4 ->[34m {"type":"listStreamers"}
03:14:22.849 [37m4 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:14:22.876 [37m4 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:14:22.877 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"4","dataChannel":true,"sfu":false,"sendOffer":true}
03:14:22.941 [37mStreamer Component -> 4[36m {"type":"offer","playerId":4,"sdp":"v=0\r\no=- 7933218488809829363 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:vtp+\r\na=ice-pwd:lPCY9VJK5TYA9r2JDzzq69AZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D6:D5:38:56:7B:ED:4B:12:0B:53:62:8A:10:C6:51:0B:9C:50:7C:DB:0C:B0:9D:60:02:79:38:57:E4:D7:56:AD\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 675237058 2492465551\r\na=ssrc:675237058 cname:nmexbIw949l6Brw/\r\na=ssrc:675237058 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2492465551 cname:nmexbIw949l6Brw/\r\na=ssrc:2492465551 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:vtp+\r\na=ice-pwd:lPCY9VJK5TYA9r2JDzzq69AZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D6:D5:38:56:7B:ED:4B:12:0B:53:62:8A:10:C6:51:0B:9C:50:7C:DB:0C:B0:9D:60:02:79:38:57:E4:D7:56:AD\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1062609105 cname:nmexbIw949l6Brw/\r\na=ssrc:1062609105 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:vtp+\r\na=ice-pwd:lPCY9VJK5TYA9r2JDzzq69AZ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D6:D5:38:56:7B:ED:4B:12:0B:53:62:8A:10:C6:51:0B:9C:50:7C:DB:0C:B0:9D:60:02:79:38:57:E4:D7:56:AD\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:14:22.973 [37m4 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 1491107731730066671 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:RfNI\r\na=ice-pwd:/DB16KvmFxol1kHMRf07Ra23\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:FC:C7:81:85:11:70:8D:44:B5:4B:97:B3:63:2F:B1:CA:24:27:DF:D5:9C:CD:25:8D:14:1E:E6:C2:9B:B5:A0\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:RfNI\r\na=ice-pwd:/DB16KvmFxol1kHMRf07Ra23\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:FC:C7:81:85:11:70:8D:44:B5:4B:97:B3:63:2F:B1:CA:24:27:DF:D5:9C:CD:25:8D:14:1E:E6:C2:9B:B5:A0\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 47f7c0e7-e5d4-47c8-9527-696d060fa040\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1256368198 cname:l8YEihRcGwfbOdiJ\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:RfNI\r\na=ice-pwd:/DB16KvmFxol1kHMRf07Ra23\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:FC:C7:81:85:11:70:8D:44:B5:4B:97:B3:63:2F:B1:CA:24:27:DF:D5:9C:CD:25:8D:14:1E:E6:C2:9B:B5:A0\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:14:22.975 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2040041130 1 udp 2122260223 ************ 49973 typ host generation 0 ufrag RfNI network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"RfNI"}}
03:14:22.976 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2561739855 1 udp 2122194687 ************* 49974 typ host generation 0 ufrag RfNI network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"RfNI"}}
03:14:22.976 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1198183012 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag vtp+ network-id 1"}}
03:14:23.008 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3040102294 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag vtp+ network-id 4"}}
03:14:23.042 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1198183012 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag vtp+ network-id 1"}}
03:14:23.075 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3040102294 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag vtp+ network-id 4"}}
03:14:23.109 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1198183012 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag vtp+ network-id 1"}}
03:14:23.143 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3040102294 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag vtp+ network-id 4"}}
03:14:23.176 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3116369648 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag vtp+ network-id 1"}}
03:14:23.210 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1268683522 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag vtp+ network-id 4"}}
03:14:36.958 [37mStreamer Component ->[34m {"type":"ping","time":1753643676}
03:15:36.964 [37mStreamer Component ->[34m {"type":"ping","time":1753643736}
03:16:36.961 [37mStreamer Component ->[34m {"type":"ping","time":1753643796}
03:17:12.646 player 4 connection closed: 1001 - 
03:17:12.647 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"4"}
03:17:12.651 [37m[players] <-[32m {"type":"playerCount","count":0}
03:17:21.124 player 5 (::1) connected
03:17:21.125 [37m5 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:17:21.126 [37m[players] <-[32m {"type":"playerCount","count":1}
03:17:21.126 [37m5 ->[34m {"type":"listStreamers"}
03:17:21.126 [37m5 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:17:21.152 [37m5 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:17:21.152 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"5","dataChannel":true,"sfu":false,"sendOffer":true}
03:17:21.227 [37mStreamer Component -> 5[36m {"type":"offer","playerId":5,"sdp":"v=0\r\no=- 3083558482026986382 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:FLsX\r\na=ice-pwd:Ugxi2wBVAo6rzewQiQOaHycs\r\na=ice-options:trickle\r\na=fingerprint:sha-256 44:C8:C3:07:91:49:9F:74:C3:BC:79:53:A0:26:06:D7:F2:A8:72:89:BD:B7:7E:9B:DE:3B:0D:6B:A8:7B:4C:E8\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2475142354 3382223844\r\na=ssrc:2475142354 cname:yIGFmpAjk6bKutnJ\r\na=ssrc:2475142354 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3382223844 cname:yIGFmpAjk6bKutnJ\r\na=ssrc:3382223844 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:FLsX\r\na=ice-pwd:Ugxi2wBVAo6rzewQiQOaHycs\r\na=ice-options:trickle\r\na=fingerprint:sha-256 44:C8:C3:07:91:49:9F:74:C3:BC:79:53:A0:26:06:D7:F2:A8:72:89:BD:B7:7E:9B:DE:3B:0D:6B:A8:7B:4C:E8\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3961854899 cname:yIGFmpAjk6bKutnJ\r\na=ssrc:3961854899 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:FLsX\r\na=ice-pwd:Ugxi2wBVAo6rzewQiQOaHycs\r\na=ice-options:trickle\r\na=fingerprint:sha-256 44:C8:C3:07:91:49:9F:74:C3:BC:79:53:A0:26:06:D7:F2:A8:72:89:BD:B7:7E:9B:DE:3B:0D:6B:A8:7B:4C:E8\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:17:21.258 [37m5 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 6667178287243053835 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:7B0n\r\na=ice-pwd:n0SNA+x3aP3i+Pyy32HqdsS0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 38:A2:3C:D6:4C:FF:F0:51:F6:A2:08:22:BF:4B:44:D4:5C:3B:B8:17:6F:71:B2:83:C9:30:98:D0:28:88:62:DF\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:7B0n\r\na=ice-pwd:n0SNA+x3aP3i+Pyy32HqdsS0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 38:A2:3C:D6:4C:FF:F0:51:F6:A2:08:22:BF:4B:44:D4:5C:3B:B8:17:6F:71:B2:83:C9:30:98:D0:28:88:62:DF\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- e645fa08-7798-48b2-a977-7af97d3841d6\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1585533755 cname:rHZW6xp4KRb3xSX0\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:7B0n\r\na=ice-pwd:n0SNA+x3aP3i+Pyy32HqdsS0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 38:A2:3C:D6:4C:FF:F0:51:F6:A2:08:22:BF:4B:44:D4:5C:3B:B8:17:6F:71:B2:83:C9:30:98:D0:28:88:62:DF\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:17:21.260 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1726414814 1 udp 2122260223 ************ 59226 typ host generation 0 ufrag 7B0n network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"7B0n"}}
03:17:21.261 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2278522171 1 udp 2122194687 ************* 59227 typ host generation 0 ufrag 7B0n network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"7B0n"}}
03:17:21.262 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:920898902 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag FLsX network-id 1"}}
03:17:21.295 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2696630130 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag FLsX network-id 4"}}
03:17:21.328 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:920898902 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag FLsX network-id 1"}}
03:17:21.362 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2696630130 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag FLsX network-id 4"}}
03:17:21.396 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:920898902 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag FLsX network-id 1"}}
03:17:21.429 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2696630130 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag FLsX network-id 4"}}
03:17:21.462 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1216211035 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag FLsX network-id 1"}}
03:17:21.496 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3726994047 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag FLsX network-id 4"}}
03:17:36.956 [37mStreamer Component ->[34m {"type":"ping","time":1753643856}
03:18:28.789 player 5 connection closed: 1001 - 
03:18:28.796 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"5"}
03:18:28.798 [37m[players] <-[32m {"type":"playerCount","count":0}
03:18:30.948 player 6 (::1) connected
03:18:30.949 [37m6 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:18:30.949 [37m[players] <-[32m {"type":"playerCount","count":1}
03:18:30.951 [37m6 ->[34m {"type":"listStreamers"}
03:18:30.951 [37m6 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:18:30.976 [37m6 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:18:30.977 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"6","dataChannel":true,"sfu":false,"sendOffer":true}
03:18:31.026 [37mStreamer Component -> 6[36m {"type":"offer","playerId":6,"sdp":"v=0\r\no=- 6068023606563000261 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uIBK\r\na=ice-pwd:DbIXpJ4pgxhKo61HvIWFin2J\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CA:25:7B:63:70:B1:DD:2D:BF:0F:96:85:30:CD:4D:DC:63:03:F8:86:9E:E9:23:2D:51:23:AC:FA:C9:BC:C7:23\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2778275723 2967345501\r\na=ssrc:2778275723 cname:3D/ggEHOVZ9Bgy0w\r\na=ssrc:2778275723 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2967345501 cname:3D/ggEHOVZ9Bgy0w\r\na=ssrc:2967345501 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uIBK\r\na=ice-pwd:DbIXpJ4pgxhKo61HvIWFin2J\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CA:25:7B:63:70:B1:DD:2D:BF:0F:96:85:30:CD:4D:DC:63:03:F8:86:9E:E9:23:2D:51:23:AC:FA:C9:BC:C7:23\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1709362232 cname:3D/ggEHOVZ9Bgy0w\r\na=ssrc:1709362232 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uIBK\r\na=ice-pwd:DbIXpJ4pgxhKo61HvIWFin2J\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CA:25:7B:63:70:B1:DD:2D:BF:0F:96:85:30:CD:4D:DC:63:03:F8:86:9E:E9:23:2D:51:23:AC:FA:C9:BC:C7:23\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:18:31.057 [37m6 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 1226841448300701768 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:jHZp\r\na=ice-pwd:60V3irqZgpC4t8VhPXu+PpGN\r\na=ice-options:trickle\r\na=fingerprint:sha-256 18:D1:7B:06:E9:57:25:B6:68:38:D7:25:05:F1:12:2B:9E:20:8E:57:E5:5E:B1:85:DC:AE:22:41:78:77:D7:25\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:jHZp\r\na=ice-pwd:60V3irqZgpC4t8VhPXu+PpGN\r\na=ice-options:trickle\r\na=fingerprint:sha-256 18:D1:7B:06:E9:57:25:B6:68:38:D7:25:05:F1:12:2B:9E:20:8E:57:E5:5E:B1:85:DC:AE:22:41:78:77:D7:25\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 6da140d9-c1dd-4add-b62d-480d147d7d2c\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1084427893 cname:wEilVRYiLJsmXAvh\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:jHZp\r\na=ice-pwd:60V3irqZgpC4t8VhPXu+PpGN\r\na=ice-options:trickle\r\na=fingerprint:sha-256 18:D1:7B:06:E9:57:25:B6:68:38:D7:25:05:F1:12:2B:9E:20:8E:57:E5:5E:B1:85:DC:AE:22:41:78:77:D7:25\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:18:31.059 [37m6 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:871053480 1 udp 2122260223 ************ 54592 typ host generation 0 ufrag jHZp network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"jHZp"}}
03:18:31.059 [37m6 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:351389155 1 udp 2122194687 ************* 54593 typ host generation 0 ufrag jHZp network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"jHZp"}}
03:18:31.059 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4266904926 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag uIBK network-id 1"}}
03:18:31.094 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3267784973 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag uIBK network-id 4"}}
03:18:31.127 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4266904926 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag uIBK network-id 1"}}
03:18:31.161 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3267784973 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag uIBK network-id 4"}}
03:18:31.195 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4266904926 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag uIBK network-id 1"}}
03:18:31.229 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3267784973 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag uIBK network-id 4"}}
03:18:31.262 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2157719494 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag uIBK network-id 1"}}
03:18:31.296 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3154744213 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag uIBK network-id 4"}}
03:18:36.919 [37mStreamer Component ->[34m {"type":"ping","time":1753643916}
03:19:13.896 player 6 connection closed: 1001 - 
03:19:13.897 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"6"}
03:19:13.902 [37m[players] <-[32m {"type":"playerCount","count":0}
03:19:14.769 player 7 (::1) connected
03:19:14.770 [37m7 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:19:14.771 [37m[players] <-[32m {"type":"playerCount","count":1}
03:19:14.771 [37m7 ->[34m {"type":"listStreamers"}
03:19:14.771 [37m7 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:19:14.799 [37m7 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:19:14.801 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"7","dataChannel":true,"sfu":false,"sendOffer":true}
03:19:14.846 [37mStreamer Component -> 7[36m {"type":"offer","playerId":7,"sdp":"v=0\r\no=- 2635246289381198248 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:KKst\r\na=ice-pwd:sBfWaR5mYCahpLeor6b2fCtm\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E4:8A:FC:74:63:2D:60:A0:CB:65:05:6B:57:90:45:C6:13:EC:2D:A2:19:EB:5E:42:8F:73:4C:CC:BE:53:83:F6\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1241345782 682288236\r\na=ssrc:1241345782 cname:0O5DtoBethSuMZ6c\r\na=ssrc:1241345782 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:682288236 cname:0O5DtoBethSuMZ6c\r\na=ssrc:682288236 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:KKst\r\na=ice-pwd:sBfWaR5mYCahpLeor6b2fCtm\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E4:8A:FC:74:63:2D:60:A0:CB:65:05:6B:57:90:45:C6:13:EC:2D:A2:19:EB:5E:42:8F:73:4C:CC:BE:53:83:F6\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1628446650 cname:0O5DtoBethSuMZ6c\r\na=ssrc:1628446650 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:KKst\r\na=ice-pwd:sBfWaR5mYCahpLeor6b2fCtm\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E4:8A:FC:74:63:2D:60:A0:CB:65:05:6B:57:90:45:C6:13:EC:2D:A2:19:EB:5E:42:8F:73:4C:CC:BE:53:83:F6\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:19:14.879 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:540454411 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag KKst network-id 1"}}
03:19:14.901 [37m7 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2384361579754060969 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:APJe\r\na=ice-pwd:dR7tk84u41IiTg2etEicE0bW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 80:AC:EA:9F:F2:A3:96:E7:89:51:E3:25:3D:D8:AE:30:68:EE:AB:98:2D:AA:6C:AB:1B:DC:F1:D9:0B:7E:72:AF\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:APJe\r\na=ice-pwd:dR7tk84u41IiTg2etEicE0bW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 80:AC:EA:9F:F2:A3:96:E7:89:51:E3:25:3D:D8:AE:30:68:EE:AB:98:2D:AA:6C:AB:1B:DC:F1:D9:0B:7E:72:AF\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 6423c806-ca60-4395-b8c4-63ceac86ad2d\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:700851516 cname:n2yW10FTC2LHF6a5\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:APJe\r\na=ice-pwd:dR7tk84u41IiTg2etEicE0bW\r\na=ice-options:trickle\r\na=fingerprint:sha-256 80:AC:EA:9F:F2:A3:96:E7:89:51:E3:25:3D:D8:AE:30:68:EE:AB:98:2D:AA:6C:AB:1B:DC:F1:D9:0B:7E:72:AF\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:19:14.903 [37m7 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:4267235139 1 udp 2122260223 ************ 49767 typ host generation 0 ufrag APJe network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"APJe"}}
03:19:14.903 [37m7 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:527521190 1 udp 2122194687 ************* 49768 typ host generation 0 ufrag APJe network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"APJe"}}
03:19:14.912 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3530046457 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag KKst network-id 4"}}
03:19:14.946 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:540454411 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag KKst network-id 1"}}
03:19:14.980 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3530046457 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag KKst network-id 4"}}
03:19:15.013 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:540454411 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag KKst network-id 1"}}
03:19:15.046 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3530046457 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag KKst network-id 4"}}
03:19:15.079 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3734796959 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag KKst network-id 1"}}
03:19:15.113 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:750972781 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag KKst network-id 4"}}
03:19:15.147 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3734796959 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag KKst network-id 1"}}
03:19:15.181 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:750972781 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag KKst network-id 4"}}
03:19:15.215 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3734796959 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag KKst network-id 1"}}
03:19:15.248 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:750972781 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag KKst network-id 4"}}
03:19:15.282 [37mStreamer Component -> 7[36m {"type":"iceCandidate","playerId":7,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4063898500 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag KKst network-id 1"}}
03:19:36.943 [37mStreamer Component ->[34m {"type":"ping","time":1753643976}
03:20:36.991 [37mStreamer Component ->[34m {"type":"ping","time":1753644036}
03:21:36.955 [37mStreamer Component ->[34m {"type":"ping","time":1753644096}
03:21:56.547 player 7 connection closed: 1001 - 
03:21:56.549 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"7"}
03:21:56.550 [37m[players] <-[32m {"type":"playerCount","count":0}
03:21:58.478 player 8 (::1) connected
03:21:58.479 [37m8 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:21:58.480 [37m[players] <-[32m {"type":"playerCount","count":1}
03:21:58.481 [37m8 ->[34m {"type":"listStreamers"}
03:21:58.481 [37m8 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:21:58.504 [37m8 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:21:58.505 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"8","dataChannel":true,"sfu":false,"sendOffer":true}
03:21:58.572 [37mStreamer Component -> 8[36m {"type":"offer","playerId":8,"sdp":"v=0\r\no=- 8240398866568164565 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:iPv2\r\na=ice-pwd:UCH+TA7pu1M8PRAFE1DNaRNi\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9C:59:29:48:93:10:C4:03:64:E9:0C:88:01:3A:9E:DC:17:5A:46:13:77:93:17:8B:32:56:3F:F9:9A:CD:5F:3E\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2483624091 1563810636\r\na=ssrc:2483624091 cname:/xm+ExEx2FIvekYR\r\na=ssrc:2483624091 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1563810636 cname:/xm+ExEx2FIvekYR\r\na=ssrc:1563810636 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:iPv2\r\na=ice-pwd:UCH+TA7pu1M8PRAFE1DNaRNi\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9C:59:29:48:93:10:C4:03:64:E9:0C:88:01:3A:9E:DC:17:5A:46:13:77:93:17:8B:32:56:3F:F9:9A:CD:5F:3E\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1971636380 cname:/xm+ExEx2FIvekYR\r\na=ssrc:1971636380 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:iPv2\r\na=ice-pwd:UCH+TA7pu1M8PRAFE1DNaRNi\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9C:59:29:48:93:10:C4:03:64:E9:0C:88:01:3A:9E:DC:17:5A:46:13:77:93:17:8B:32:56:3F:F9:9A:CD:5F:3E\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:21:58.606 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:430398621 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag iPv2 network-id 1"}}
03:21:58.607 [37m8 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 1898449739234569275 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:+DB5\r\na=ice-pwd:PkFwPal3molehOXxrKA9nZQh\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FB:FF:8E:92:02:5B:F2:07:E3:86:B5:2F:F7:74:C2:22:AD:E0:18:B3:77:B9:CE:BC:CA:68:14:99:5D:19:71:6D\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:+DB5\r\na=ice-pwd:PkFwPal3molehOXxrKA9nZQh\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FB:FF:8E:92:02:5B:F2:07:E3:86:B5:2F:F7:74:C2:22:AD:E0:18:B3:77:B9:CE:BC:CA:68:14:99:5D:19:71:6D\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 9e9a667b-82f3-4049-a313-df2ce3290890\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2075607427 cname:Usj7RtA5GzJxu2sr\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:+DB5\r\na=ice-pwd:PkFwPal3molehOXxrKA9nZQh\r\na=ice-options:trickle\r\na=fingerprint:sha-256 FB:FF:8E:92:02:5B:F2:07:E3:86:B5:2F:F7:74:C2:22:AD:E0:18:B3:77:B9:CE:BC:CA:68:14:99:5D:19:71:6D\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:21:58.608 [37m8 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:507786213 1 udp 2122260223 ************ 50506 typ host generation 0 ufrag +DB5 network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"+DB5"}}
03:21:58.608 [37m8 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:4285378816 1 udp 2122194687 ************* 50507 typ host generation 0 ufrag +DB5 network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"+DB5"}}
03:21:58.639 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:624091342 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag iPv2 network-id 4"}}
03:21:58.673 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:430398621 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag iPv2 network-id 1"}}
03:21:58.707 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:624091342 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag iPv2 network-id 4"}}
03:21:58.740 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:430398621 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag iPv2 network-id 1"}}
03:21:58.774 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:624091342 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag iPv2 network-id 4"}}
03:21:58.807 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1734917637 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag iPv2 network-id 1"}}
03:21:58.841 [37mStreamer Component -> 8[36m {"type":"iceCandidate","playerId":8,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1543307862 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag iPv2 network-id 4"}}
03:22:36.960 [37mStreamer Component ->[34m {"type":"ping","time":1753644156}
03:23:36.984 [37mStreamer Component ->[34m {"type":"ping","time":1753644216}
03:24:36.933 [37mStreamer Component ->[34m {"type":"ping","time":1753644276}
03:25:36.969 [37mStreamer Component ->[34m {"type":"ping","time":1753644336}
03:26:36.959 [37mStreamer Component ->[34m {"type":"ping","time":1753644396}
03:27:36.960 [37mStreamer Component ->[34m {"type":"ping","time":1753644456}
03:28:36.930 [37mStreamer Component ->[34m {"type":"ping","time":1753644516}
03:29:36.974 [37mStreamer Component ->[34m {"type":"ping","time":1753644576}
03:30:36.962 [37mStreamer Component ->[34m {"type":"ping","time":1753644636}
03:31:36.958 [37mStreamer Component ->[34m {"type":"ping","time":1753644696}
03:31:47.273 player 8 connection closed: 1001 - 
03:31:47.277 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"8"}
03:31:47.278 [37m[players] <-[32m {"type":"playerCount","count":0}
03:31:48.324 player 9 (::1) connected
03:31:48.325 [37m9 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:31:48.326 [37m[players] <-[32m {"type":"playerCount","count":1}
03:31:48.327 [37m9 ->[34m {"type":"listStreamers"}
03:31:48.327 [37m9 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:31:48.353 [37m9 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:31:48.354 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"9","dataChannel":true,"sfu":false,"sendOffer":true}
03:31:48.405 [37mStreamer Component -> 9[36m {"type":"offer","playerId":9,"sdp":"v=0\r\no=- 2945343896956306738 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:zrQU\r\na=ice-pwd:j8XB6hNZ3hllUxkC5Tru1nrx\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8D:31:8F:75:77:FC:1C:F9:79:B1:C8:A2:06:BF:05:44:36:B2:74:1F:19:35:EC:63:99:ED:BD:52:AF:69:2B:74\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 4227433667 1251174116\r\na=ssrc:4227433667 cname:61iycEEeLpU/qkms\r\na=ssrc:4227433667 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1251174116 cname:61iycEEeLpU/qkms\r\na=ssrc:1251174116 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:zrQU\r\na=ice-pwd:j8XB6hNZ3hllUxkC5Tru1nrx\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8D:31:8F:75:77:FC:1C:F9:79:B1:C8:A2:06:BF:05:44:36:B2:74:1F:19:35:EC:63:99:ED:BD:52:AF:69:2B:74\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1906553796 cname:61iycEEeLpU/qkms\r\na=ssrc:1906553796 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:zrQU\r\na=ice-pwd:j8XB6hNZ3hllUxkC5Tru1nrx\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8D:31:8F:75:77:FC:1C:F9:79:B1:C8:A2:06:BF:05:44:36:B2:74:1F:19:35:EC:63:99:ED:BD:52:AF:69:2B:74\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:31:48.438 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:770053171 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag zrQU network-id 1"}}
03:31:48.443 [37m9 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2813208852257645280 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:WuNc\r\na=ice-pwd:Sq+M+0+i3S5+FzSYigYE7ctt\r\na=ice-options:trickle\r\na=fingerprint:sha-256 47:C4:25:E3:9D:0E:55:F5:DF:98:85:0B:0D:F5:E6:4D:83:85:8A:AB:55:D6:34:8B:26:E2:EA:0E:37:3C:1F:9D\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:WuNc\r\na=ice-pwd:Sq+M+0+i3S5+FzSYigYE7ctt\r\na=ice-options:trickle\r\na=fingerprint:sha-256 47:C4:25:E3:9D:0E:55:F5:DF:98:85:0B:0D:F5:E6:4D:83:85:8A:AB:55:D6:34:8B:26:E2:EA:0E:37:3C:1F:9D\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 3dd336bd-4018-484b-9912-5c8dfeff5eee\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:4162089008 cname:aqA6KXvXkvNPuNrf\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:WuNc\r\na=ice-pwd:Sq+M+0+i3S5+FzSYigYE7ctt\r\na=ice-options:trickle\r\na=fingerprint:sha-256 47:C4:25:E3:9D:0E:55:F5:DF:98:85:0B:0D:F5:E6:4D:83:85:8A:AB:55:D6:34:8B:26:E2:EA:0E:37:3C:1F:9D\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:31:48.444 [37m9 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3125543886 1 udp 2122260223 ************ 65532 typ host generation 0 ufrag WuNc network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"WuNc"}}
03:31:48.444 [37m9 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2733695679 1 udp 2122194687 ************* 65533 typ host generation 0 ufrag WuNc network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"WuNc"}}
03:31:48.471 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3753411009 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag zrQU network-id 4"}}
03:31:48.505 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:770053171 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag zrQU network-id 1"}}
03:31:48.538 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3753411009 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag zrQU network-id 4"}}
03:31:48.572 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:770053171 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag zrQU network-id 1"}}
03:31:48.606 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3753411009 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag zrQU network-id 4"}}
03:31:48.640 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3545022631 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag zrQU network-id 1"}}
03:31:48.673 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:554849621 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag zrQU network-id 4"}}
03:31:48.707 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3545022631 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag zrQU network-id 1"}}
03:31:48.740 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:554849621 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag zrQU network-id 4"}}
03:31:48.774 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3545022631 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag zrQU network-id 1"}}
03:31:48.808 [37mStreamer Component -> 9[36m {"type":"iceCandidate","playerId":9,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:554849621 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag zrQU network-id 4"}}
03:32:36.985 [37mStreamer Component ->[34m {"type":"ping","time":1753644756}
03:32:39.241 player 9 connection closed: 1001 - 
03:32:39.245 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"9"}
03:32:39.245 [37m[players] <-[32m {"type":"playerCount","count":0}
03:32:39.861 player 10 (::1) connected
03:32:39.863 [37m10 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:32:39.864 [37m[players] <-[32m {"type":"playerCount","count":1}
03:32:39.865 [37m10 ->[34m {"type":"listStreamers"}
03:32:39.865 [37m10 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:32:39.896 [37m10 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:32:39.897 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"10","dataChannel":true,"sfu":false,"sendOffer":true}
03:32:39.962 [37mStreamer Component -> 10[36m {"type":"offer","playerId":10,"sdp":"v=0\r\no=- 7486085301579371408 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:1lH7\r\na=ice-pwd:UK/HuopsTYwPkCIQ8DgtDEjc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 76:D8:86:5B:0D:93:DF:16:3A:BA:41:9C:55:B1:BD:65:71:D0:70:B3:CC:0B:0D:6E:23:0F:6F:4A:37:28:2C:E3\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 721143286 1046675929\r\na=ssrc:721143286 cname:itewUihFl7XIA0gW\r\na=ssrc:721143286 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1046675929 cname:itewUihFl7XIA0gW\r\na=ssrc:1046675929 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:1lH7\r\na=ice-pwd:UK/HuopsTYwPkCIQ8DgtDEjc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 76:D8:86:5B:0D:93:DF:16:3A:BA:41:9C:55:B1:BD:65:71:D0:70:B3:CC:0B:0D:6E:23:0F:6F:4A:37:28:2C:E3\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:562907616 cname:itewUihFl7XIA0gW\r\na=ssrc:562907616 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:1lH7\r\na=ice-pwd:UK/HuopsTYwPkCIQ8DgtDEjc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 76:D8:86:5B:0D:93:DF:16:3A:BA:41:9C:55:B1:BD:65:71:D0:70:B3:CC:0B:0D:6E:23:0F:6F:4A:37:28:2C:E3\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:32:39.995 [37mStreamer Component -> 10[36m {"type":"iceCandidate","playerId":10,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:41128678 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag 1lH7 network-id 1"}}
03:32:39.997 [37m10 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 4939117565841037096 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:nmrp\r\na=ice-pwd:ZJxSMsPpEjyW03DIaw0Z960p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:5C:AE:46:F3:0A:85:C0:C9:B6:2C:22:D9:57:54:BC:22:99:8D:1B:B9:2B:91:0E:66:E8:99:CF:C1:2E:77:F8\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:nmrp\r\na=ice-pwd:ZJxSMsPpEjyW03DIaw0Z960p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:5C:AE:46:F3:0A:85:C0:C9:B6:2C:22:D9:57:54:BC:22:99:8D:1B:B9:2B:91:0E:66:E8:99:CF:C1:2E:77:F8\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 758c793f-58db-4b8c-981e-3606a918bdad\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3647946037 cname:H8ZBd/8O4k/GZ4At\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:nmrp\r\na=ice-pwd:ZJxSMsPpEjyW03DIaw0Z960p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:5C:AE:46:F3:0A:85:C0:C9:B6:2C:22:D9:57:54:BC:22:99:8D:1B:B9:2B:91:0E:66:E8:99:CF:C1:2E:77:F8\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:32:39.998 [37m10 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3700640581 1 udp 2122260223 ************ 49482 typ host generation 0 ufrag nmrp network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"nmrp"}}
03:32:39.998 [37m10 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:4220106254 1 udp 2122194687 ************* 49483 typ host generation 0 ufrag nmrp network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"nmrp"}}
03:32:40.029 [37mStreamer Component -> 10[36m {"type":"iceCandidate","playerId":10,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4029482772 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag 1lH7 network-id 4"}}
03:32:40.062 [37mStreamer Component -> 10[36m {"type":"iceCandidate","playerId":10,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:41128678 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag 1lH7 network-id 1"}}
03:32:40.097 [37mStreamer Component -> 10[36m {"type":"iceCandidate","playerId":10,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4029482772 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag 1lH7 network-id 4"}}
03:32:40.130 [37mStreamer Component -> 10[36m {"type":"iceCandidate","playerId":10,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:41128678 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag 1lH7 network-id 1"}}
03:32:40.164 [37mStreamer Component -> 10[36m {"type":"iceCandidate","playerId":10,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4029482772 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag 1lH7 network-id 4"}}
03:32:40.198 [37mStreamer Component -> 10[36m {"type":"iceCandidate","playerId":10,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4242097778 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag 1lH7 network-id 1"}}
03:32:40.231 [37mStreamer Component -> 10[36m {"type":"iceCandidate","playerId":10,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:243782528 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag 1lH7 network-id 4"}}
03:32:57.218 player 10 connection closed: 1001 - 
03:32:57.220 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"10"}
03:32:57.224 [37m[players] <-[32m {"type":"playerCount","count":0}
03:32:58.236 player 11 (::1) connected
03:32:58.237 [37m11 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
03:32:58.237 [37m[players] <-[32m {"type":"playerCount","count":1}
03:32:58.237 [37m11 ->[34m {"type":"listStreamers"}
03:32:58.238 [37m11 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
03:32:58.262 [37m11 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
03:32:58.264 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"11","dataChannel":true,"sfu":false,"sendOffer":true}
03:32:58.324 [37mStreamer Component -> 11[36m {"type":"offer","playerId":11,"sdp":"v=0\r\no=- 7710550809309718837 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:6m5r\r\na=ice-pwd:8WkCbMVytjYMWOsYhzwFoCWA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 7F:C2:75:1F:A7:FF:38:4E:17:7F:B0:44:52:B7:2F:06:DD:A3:B5:75:F7:5B:88:16:E6:9B:3C:51:1D:4A:F4:4A\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1620113038 708356914\r\na=ssrc:1620113038 cname:6iXxhhQJk1zLYTOF\r\na=ssrc:1620113038 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:708356914 cname:6iXxhhQJk1zLYTOF\r\na=ssrc:708356914 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:6m5r\r\na=ice-pwd:8WkCbMVytjYMWOsYhzwFoCWA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 7F:C2:75:1F:A7:FF:38:4E:17:7F:B0:44:52:B7:2F:06:DD:A3:B5:75:F7:5B:88:16:E6:9B:3C:51:1D:4A:F4:4A\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1290881729 cname:6iXxhhQJk1zLYTOF\r\na=ssrc:1290881729 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:6m5r\r\na=ice-pwd:8WkCbMVytjYMWOsYhzwFoCWA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 7F:C2:75:1F:A7:FF:38:4E:17:7F:B0:44:52:B7:2F:06:DD:A3:B5:75:F7:5B:88:16:E6:9B:3C:51:1D:4A:F4:4A\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:32:58.356 [37m11 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 545238616006574356 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:JbxO\r\na=ice-pwd:lcM4OJ5FwPZ7KevwOlj4BBuQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 10:99:EA:1D:B3:96:7E:6F:EA:23:68:D5:23:05:9B:7B:AB:6D:55:DC:5B:5F:F0:CA:CC:23:88:ED:EE:6A:B0:F1\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:JbxO\r\na=ice-pwd:lcM4OJ5FwPZ7KevwOlj4BBuQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 10:99:EA:1D:B3:96:7E:6F:EA:23:68:D5:23:05:9B:7B:AB:6D:55:DC:5B:5F:F0:CA:CC:23:88:ED:EE:6A:B0:F1\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- a6d0fc0e-d9d6-495e-8aee-46939047a033\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:4161732026 cname:263ZfRoj2mrJwcYj\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:JbxO\r\na=ice-pwd:lcM4OJ5FwPZ7KevwOlj4BBuQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 10:99:EA:1D:B3:96:7E:6F:EA:23:68:D5:23:05:9B:7B:AB:6D:55:DC:5B:5F:F0:CA:CC:23:88:ED:EE:6A:B0:F1\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
03:32:58.358 [37m11 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2923247337 1 udp 2122260223 ************ 49367 typ host generation 0 ufrag JbxO network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"JbxO"}}
03:32:58.358 [37m11 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2301086626 1 udp 2122194687 ************* 49368 typ host generation 0 ufrag JbxO network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"JbxO"}}
03:32:58.358 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4226591457 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag 6m5r network-id 1"}}
03:32:58.391 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:162676499 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag 6m5r network-id 4"}}
03:32:58.425 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4226591457 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag 6m5r network-id 1"}}
03:32:58.459 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:162676499 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag 6m5r network-id 4"}}
03:32:58.492 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4226591457 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag 6m5r network-id 1"}}
03:32:58.526 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:162676499 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag 6m5r network-id 4"}}
03:32:58.560 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:88505973 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag 6m5r network-id 1"}}
03:32:58.594 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4145605511 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag 6m5r network-id 4"}}
03:32:58.627 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:88505973 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag 6m5r network-id 1"}}
03:32:58.661 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4145605511 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag 6m5r network-id 4"}}
03:32:58.694 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:88505973 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag 6m5r network-id 1"}}
03:32:58.728 [37mStreamer Component -> 11[36m {"type":"iceCandidate","playerId":11,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4145605511 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag 6m5r network-id 4"}}
03:33:36.962 [37mStreamer Component ->[34m {"type":"ping","time":1753644816}
03:34:36.934 [37mStreamer Component ->[34m {"type":"ping","time":1753644876}
03:35:36.953 [37mStreamer Component ->[34m {"type":"ping","time":1753644936}
03:36:36.939 [37mStreamer Component ->[34m {"type":"ping","time":1753644996}
03:37:36.963 [37mStreamer Component ->[34m {"type":"ping","time":1753645056}
03:38:36.966 [37mStreamer Component ->[34m {"type":"ping","time":1753645116}
03:39:36.951 [37mStreamer Component ->[34m {"type":"ping","time":1753645176}
03:40:36.925 [37mStreamer Component ->[34m {"type":"ping","time":1753645236}
03:41:36.942 [37mStreamer Component ->[34m {"type":"ping","time":1753645296}
