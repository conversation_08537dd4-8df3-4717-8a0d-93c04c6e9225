15:02:02.199 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
15:02:02.595 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
15:02:02.597 Redirecting http->https
15:02:02.620 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
15:02:02.735 WebSocket listening for Streamer connections on :8888
15:02:02.736 WebSocket listening for SFU connections on :8889
15:02:02.736 WebSocket listening for Players connections on :80
15:02:02.737 Http listening on *: 80
15:02:02.738 Https listening on *: 443
15:07:16.298 Streamer connected: ::1
15:07:16.299 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:07:16.300 [37m::1 <-[32m {"type":"identify"}
15:07:17.714 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:07:17.715 Registered new streamer: Streamer Component
15:07:34.778 player 1 (::1) connected
15:07:34.779 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:07:34.779 [37m[players] <-[32m {"type":"playerCount","count":1}
15:07:34.779 [37m1 ->[34m {"type":"listStreamers"}
15:07:34.780 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
15:07:34.923 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
15:07:34.923 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
15:07:35.055 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 8560778985772303120 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:umRo\r\na=ice-pwd:7Fo+gOcX9OxSX12Iw1ZCJQsC\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D4:13:1E:61:3D:FB:8B:53:F8:12:3D:BA:EB:27:21:67:DD:33:FF:5D:5D:B0:CA:E4:9B:8C:69:BF:44:6D:DC:91\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1324415480 473698725\r\na=ssrc:1324415480 cname:P7CK6RCrxBTBDcIH\r\na=ssrc:1324415480 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:473698725 cname:P7CK6RCrxBTBDcIH\r\na=ssrc:473698725 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:umRo\r\na=ice-pwd:7Fo+gOcX9OxSX12Iw1ZCJQsC\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D4:13:1E:61:3D:FB:8B:53:F8:12:3D:BA:EB:27:21:67:DD:33:FF:5D:5D:B0:CA:E4:9B:8C:69:BF:44:6D:DC:91\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:47370363 cname:P7CK6RCrxBTBDcIH\r\na=ssrc:47370363 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:umRo\r\na=ice-pwd:7Fo+gOcX9OxSX12Iw1ZCJQsC\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D4:13:1E:61:3D:FB:8B:53:F8:12:3D:BA:EB:27:21:67:DD:33:FF:5D:5D:B0:CA:E4:9B:8C:69:BF:44:6D:DC:91\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:07:35.057 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2945122098 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag umRo network-id 1"}}
15:07:35.087 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3258566819 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag umRo network-id 4"}}
15:07:35.090 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5438054520683823646 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:iXKM\r\na=ice-pwd:OkcbYW4axjTWG5vgUXlxy4SA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1B:5F:AD:1F:27:7F:30:98:25:D9:C4:4F:6C:A3:3E:7F:96:75:A1:6A:0E:D1:CE:E1:94:F9:98:67:BA:20:60:36\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:iXKM\r\na=ice-pwd:OkcbYW4axjTWG5vgUXlxy4SA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1B:5F:AD:1F:27:7F:30:98:25:D9:C4:4F:6C:A3:3E:7F:96:75:A1:6A:0E:D1:CE:E1:94:F9:98:67:BA:20:60:36\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 2bcf620d-1da4-4d5b-a791-f1a59576a3be\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1653592819 cname:ssjoj3SquTnF/ugU\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:iXKM\r\na=ice-pwd:OkcbYW4axjTWG5vgUXlxy4SA\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1B:5F:AD:1F:27:7F:30:98:25:D9:C4:4F:6C:A3:3E:7F:96:75:A1:6A:0E:D1:CE:E1:94:F9:98:67:BA:20:60:36\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:07:35.095 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:784485431 1 udp 2122260223 ************ 58151 typ host generation 0 ufrag iXKM network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"iXKM"}}
15:07:35.096 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3981584583 1 udp 2122194687 ************* 58152 typ host generation 0 ufrag iXKM network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"iXKM"}}
15:07:35.120 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2945122098 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag umRo network-id 1"}}
15:07:35.154 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3258566819 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag umRo network-id 4"}}
15:07:35.187 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2945122098 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag umRo network-id 1"}}
15:07:35.221 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3258566819 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag umRo network-id 4"}}
15:07:35.254 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3507804735 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag umRo network-id 1"}}
15:07:35.287 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3165121966 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag umRo network-id 4"}}
15:07:35.321 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3507804735 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag umRo network-id 1"}}
15:07:35.355 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3165121966 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag umRo network-id 4"}}
15:07:35.388 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3507804735 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag umRo network-id 1"}}
15:07:35.422 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3165121966 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag umRo network-id 4"}}
15:08:17.847 [37mStreamer Component ->[34m {"type":"ping","time":1754550497}
15:08:25.434 player 1 connection closed: 1001 - 
15:08:25.435 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
15:08:25.435 [37m[players] <-[32m {"type":"playerCount","count":0}
15:08:26.318 player 2 (::1) connected
15:08:26.319 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:08:26.320 [37m[players] <-[32m {"type":"playerCount","count":1}
15:08:26.328 [37m2 ->[34m {"type":"listStreamers"}
15:08:26.329 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
15:08:26.357 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
15:08:26.358 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
15:08:26.425 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 1692008915477670411 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:RLKM\r\na=ice-pwd:wLXUyKnOqlnGwmH+JNbBEMKQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B6:78:C4:5B:28:03:4A:B5:1D:53:5C:36:04:82:D5:8F:8B:BE:56:A0:B9:86:7F:34:8B:F8:BB:A4:1C:D5:C3:39\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 357135038 3741672935\r\na=ssrc:357135038 cname:qS1UyZwTZyhvRofi\r\na=ssrc:357135038 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3741672935 cname:qS1UyZwTZyhvRofi\r\na=ssrc:3741672935 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:RLKM\r\na=ice-pwd:wLXUyKnOqlnGwmH+JNbBEMKQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B6:78:C4:5B:28:03:4A:B5:1D:53:5C:36:04:82:D5:8F:8B:BE:56:A0:B9:86:7F:34:8B:F8:BB:A4:1C:D5:C3:39\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:953727578 cname:qS1UyZwTZyhvRofi\r\na=ssrc:953727578 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:RLKM\r\na=ice-pwd:wLXUyKnOqlnGwmH+JNbBEMKQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B6:78:C4:5B:28:03:4A:B5:1D:53:5C:36:04:82:D5:8F:8B:BE:56:A0:B9:86:7F:34:8B:F8:BB:A4:1C:D5:C3:39\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:08:26.459 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2511831690 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag RLKM network-id 1"}}
15:08:26.465 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 9208670358388054877 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:fuQJ\r\na=ice-pwd:FDQFpNy/S9sAEqIi3VV6LUlu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 ED:73:93:5D:37:52:A1:99:04:63:F3:5D:28:A8:22:01:64:27:78:DA:E4:6B:91:5A:9C:7D:BC:8B:BE:A7:22:57\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:fuQJ\r\na=ice-pwd:FDQFpNy/S9sAEqIi3VV6LUlu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 ED:73:93:5D:37:52:A1:99:04:63:F3:5D:28:A8:22:01:64:27:78:DA:E4:6B:91:5A:9C:7D:BC:8B:BE:A7:22:57\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 10f07a01-aa79-4b1c-8498-7460d40874b5\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1759057040 cname:zYNliEt7zPw0gkKP\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:fuQJ\r\na=ice-pwd:FDQFpNy/S9sAEqIi3VV6LUlu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 ED:73:93:5D:37:52:A1:99:04:63:F3:5D:28:A8:22:01:64:27:78:DA:E4:6B:91:5A:9C:7D:BC:8B:BE:A7:22:57\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:08:26.466 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3206355479 1 udp 2122260223 ************ 54401 typ host generation 0 ufrag fuQJ network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"fuQJ"}}
15:08:26.466 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2089647847 1 udp 2122194687 ************* 54402 typ host generation 0 ufrag fuQJ network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"fuQJ"}}
15:08:26.492 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:315789935 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag RLKM network-id 4"}}
15:08:26.526 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2511831690 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag RLKM network-id 1"}}
15:08:26.560 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:315789935 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag RLKM network-id 4"}}
15:08:26.594 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2511831690 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag RLKM network-id 1"}}
15:08:26.628 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:315789935 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag RLKM network-id 4"}}
15:08:26.661 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3950534674 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag RLKM network-id 1"}}
15:08:26.694 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1813868791 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag RLKM network-id 4"}}
15:08:26.728 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3950534674 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag RLKM network-id 1"}}
15:08:26.761 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1813868791 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag RLKM network-id 4"}}
15:08:26.795 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3950534674 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag RLKM network-id 1"}}
15:08:26.829 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1813868791 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag RLKM network-id 4"}}
15:09:17.888 [37mStreamer Component ->[34m {"type":"ping","time":1754550557}
15:10:17.868 [37mStreamer Component ->[34m {"type":"ping","time":1754550617}
15:11:17.866 [37mStreamer Component ->[34m {"type":"ping","time":1754550677}
15:12:17.841 [37mStreamer Component ->[34m {"type":"ping","time":1754550737}
15:13:17.876 [37mStreamer Component ->[34m {"type":"ping","time":1754550797}
15:14:17.863 [37mStreamer Component ->[34m {"type":"ping","time":1754550857}
15:15:17.857 [37mStreamer Component ->[34m {"type":"ping","time":1754550917}
15:16:17.876 [37mStreamer Component ->[34m {"type":"ping","time":1754550977}
15:17:17.867 [37mStreamer Component ->[34m {"type":"ping","time":1754551037}
15:18:17.857 [37mStreamer Component ->[34m {"type":"ping","time":1754551097}
15:19:17.849 [37mStreamer Component ->[34m {"type":"ping","time":1754551157}
15:20:17.837 [37mStreamer Component ->[34m {"type":"ping","time":1754551217}
15:21:17.851 [37mStreamer Component ->[34m {"type":"ping","time":1754551277}
15:22:17.867 [37mStreamer Component ->[34m {"type":"ping","time":1754551337}
15:23:17.878 [37mStreamer Component ->[34m {"type":"ping","time":1754551397}
15:24:17.856 [37mStreamer Component ->[34m {"type":"ping","time":1754551457}
15:25:17.870 [37mStreamer Component ->[34m {"type":"ping","time":1754551517}
15:26:17.858 [37mStreamer Component ->[34m {"type":"ping","time":1754551577}
15:27:17.867 [37mStreamer Component ->[34m {"type":"ping","time":1754551637}
15:28:17.849 [37mStreamer Component ->[34m {"type":"ping","time":1754551697}
15:29:17.864 [37mStreamer Component ->[34m {"type":"ping","time":1754551757}
15:30:17.833 [37mStreamer Component ->[34m {"type":"ping","time":1754551817}
15:31:17.833 [37mStreamer Component ->[34m {"type":"ping","time":1754551877}
15:32:17.866 [37mStreamer Component ->[34m {"type":"ping","time":1754551937}
15:33:17.829 [37mStreamer Component ->[34m {"type":"ping","time":1754551997}
15:34:17.856 [37mStreamer Component ->[34m {"type":"ping","time":1754552057}
15:35:17.868 [37mStreamer Component ->[34m {"type":"ping","time":1754552117}
15:36:17.871 [37mStreamer Component ->[34m {"type":"ping","time":1754552177}
15:37:17.861 [37mStreamer Component ->[34m {"type":"ping","time":1754552237}
15:38:17.857 [37mStreamer Component ->[34m {"type":"ping","time":1754552297}
15:39:17.887 [37mStreamer Component ->[34m {"type":"ping","time":1754552357}
15:40:17.858 [37mStreamer Component ->[34m {"type":"ping","time":1754552417}
15:41:17.853 [37mStreamer Component ->[34m {"type":"ping","time":1754552477}
15:42:12.152 streamer Streamer Component disconnected: 1006 - 
15:42:12.153 unsubscribing all players on Streamer Component
15:42:12.154 player 2 connection closed: 1005 - 
15:42:12.155 [37m[players] <-[32m {"type":"playerCount","count":0}
15:45:51.067 player 3 (::ffff:192.168.0.133) connected
15:45:51.068 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:45:51.068 [37m[players] <-[32m {"type":"playerCount","count":1}
15:45:51.069 [37m3 ->[34m {"type":"listStreamers"}
15:45:51.069 [37m3 <-[32m {"type":"streamerList","ids":[]}
15:45:58.933 player 3 connection closed: 1005 - 
15:45:58.934 [37m[players] <-[32m {"type":"playerCount","count":0}
15:46:01.950 player 4 (::ffff:192.168.0.133) connected
15:46:01.951 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:46:01.952 [37m[players] <-[32m {"type":"playerCount","count":1}
15:46:01.952 [37m4 ->[34m {"type":"listStreamers"}
15:46:01.952 [37m4 <-[32m {"type":"streamerList","ids":[]}
15:46:29.913 player 4 connection closed: 1001 - 
15:46:29.914 [37m[players] <-[32m {"type":"playerCount","count":0}
15:46:32.195 player 5 (::ffff:192.168.0.133) connected
15:46:32.196 [37m5 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:46:32.196 [37m[players] <-[32m {"type":"playerCount","count":1}
15:46:32.197 [37m5 ->[34m {"type":"listStreamers"}
15:46:32.197 [37m5 <-[32m {"type":"streamerList","ids":[]}
15:46:46.422 player 5 connection closed: 1001 - 
15:46:46.422 [37m[players] <-[32m {"type":"playerCount","count":0}
15:46:49.444 player 6 (::ffff:192.168.0.133) connected
15:46:49.445 [37m6 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:46:49.445 [37m[players] <-[32m {"type":"playerCount","count":1}
15:46:49.446 [37m6 ->[34m {"type":"listStreamers"}
15:46:49.446 [37m6 <-[32m {"type":"streamerList","ids":[]}
15:46:59.108 player 6 connection closed: 1005 - 
15:46:59.109 [37m[players] <-[32m {"type":"playerCount","count":0}
15:47:02.124 player 7 (::ffff:192.168.0.133) connected
15:47:02.124 [37m7 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:47:02.125 [37m[players] <-[32m {"type":"playerCount","count":1}
15:47:02.125 [37m7 ->[34m {"type":"listStreamers"}
15:47:02.125 [37m7 <-[32m {"type":"streamerList","ids":[]}
