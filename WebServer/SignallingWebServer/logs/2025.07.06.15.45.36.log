15:45:36.469 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
15:45:36.508 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
15:45:36.510 Redirecting http->https
15:45:36.515 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
15:45:36.523 WebSocket listening for Streamer connections on :8888
15:45:36.523 WebSocket listening for SFU connections on :8889
15:45:36.524 WebSocket listening for Players connections on :80
15:45:36.525 Http listening on *: 80
15:45:36.525 Https listening on *: 443
15:48:41.576 Streamer connected: ::1
15:48:41.577 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:48:41.578 [37m::1 <-[32m {"type":"identify"}
15:48:42.039 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:48:42.039 Registered new streamer: Streamer Component
15:49:42.138 [37mStreamer Component ->[34m {"type":"ping","time":1754466582}
15:50:42.125 [37mStreamer Component ->[34m {"type":"ping","time":1754466642}
15:51:42.118 [37mStreamer Component ->[34m {"type":"ping","time":1754466702}
15:52:43.065 [37mStreamer Component ->[34m {"type":"ping","time":1754466763}
15:53:43.027 [37mStreamer Component ->[34m {"type":"ping","time":1754466823}
15:54:01.873 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
15:54:01.874 unsubscribing all players on Streamer Component
