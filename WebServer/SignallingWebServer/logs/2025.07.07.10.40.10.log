10:40:10.181 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
10:40:10.221 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
10:40:10.223 Redirecting http->https
10:40:10.228 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
10:40:10.234 WebSocket listening for Streamer connections on :8888
10:40:10.235 WebSocket listening for SFU connections on :8889
10:40:10.236 WebSocket listening for Players connections on :80
10:40:10.237 Http listening on *: 80
10:40:10.237 Https listening on *: 443
10:40:40.197 Streamer connected: ::1
10:40:40.197 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:40:40.198 [37m::1 <-[32m {"type":"identify"}
10:40:40.699 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
10:40:40.699 Registered new streamer: Streamer Component
10:41:34.249 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
10:41:34.250 unsubscribing all players on Streamer Component
