18:10:45.376 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
18:10:45.415 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
18:10:45.417 Redirecting http->https
18:10:45.422 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
18:10:45.429 WebSocket listening for Streamer connections on :8888
18:10:45.430 WebSocket listening for SFU connections on :8889
18:10:45.430 WebSocket listening for Players connections on :80
18:10:45.431 Http listening on *: 80
18:10:45.432 Https listening on *: 443
18:11:17.937 Streamer connected: ::1
18:11:17.938 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:11:17.939 [37m::1 <-[32m {"type":"identify"}
18:11:18.434 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:11:18.435 Registered new streamer: Streamer Component
18:12:18.550 [37mStreamer Component ->[34m {"type":"ping","time":1754475138}
18:13:18.545 [37mStreamer Component ->[34m {"type":"ping","time":1754475198}
18:14:10.197 player 1 (::1) connected
18:14:10.198 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:14:10.207 [37m[players] <-[32m {"type":"playerCount","count":1}
18:14:10.208 [37m1 ->[34m {"type":"listStreamers"}
18:14:10.209 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
18:14:10.231 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
18:14:10.231 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
18:14:10.356 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 7626397235751783326 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Azpc\r\na=ice-pwd:1U2ZQvTrj79JdWzJbo1tpJk2\r\na=ice-options:trickle\r\na=fingerprint:sha-256 06:C2:EE:84:51:42:A1:BD:18:09:0E:18:F1:FA:81:82:E2:6D:4C:06:D6:E1:AD:A5:41:E5:7B:F1:4F:84:B2:01\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2635091537 1183577163\r\na=ssrc:2635091537 cname:m1WrtHeUQ7qyU06c\r\na=ssrc:2635091537 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1183577163 cname:m1WrtHeUQ7qyU06c\r\na=ssrc:1183577163 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Azpc\r\na=ice-pwd:1U2ZQvTrj79JdWzJbo1tpJk2\r\na=ice-options:trickle\r\na=fingerprint:sha-256 06:C2:EE:84:51:42:A1:BD:18:09:0E:18:F1:FA:81:82:E2:6D:4C:06:D6:E1:AD:A5:41:E5:7B:F1:4F:84:B2:01\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:690724399 cname:m1WrtHeUQ7qyU06c\r\na=ssrc:690724399 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Azpc\r\na=ice-pwd:1U2ZQvTrj79JdWzJbo1tpJk2\r\na=ice-options:trickle\r\na=fingerprint:sha-256 06:C2:EE:84:51:42:A1:BD:18:09:0E:18:F1:FA:81:82:E2:6D:4C:06:D6:E1:AD:A5:41:E5:7B:F1:4F:84:B2:01\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
18:14:10.358 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3350027943 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag Azpc network-id 1"}}
18:14:10.386 [37m1 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 8064639708876192223 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:eAL7\r\na=ice-pwd:SvOaOJ9xqZ+VSMfX3fc1EXng\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F9:47:EA:A7:2A:9A:E8:80:BF:56:8C:9E:65:8E:3A:01:44:35:86:E3:D9:98:53:63:6F:D1:AE:E0:5E:F6:AC:56\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:eAL7\r\na=ice-pwd:SvOaOJ9xqZ+VSMfX3fc1EXng\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F9:47:EA:A7:2A:9A:E8:80:BF:56:8C:9E:65:8E:3A:01:44:35:86:E3:D9:98:53:63:6F:D1:AE:E0:5E:F6:AC:56\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 43e99325-16a1-4964-b8d7-6f8d4b58429a\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3599436384 cname:xhzRghrGrUeVYkUu\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:eAL7\r\na=ice-pwd:SvOaOJ9xqZ+VSMfX3fc1EXng\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F9:47:EA:A7:2A:9A:E8:80:BF:56:8C:9E:65:8E:3A:01:44:35:86:E3:D9:98:53:63:6F:D1:AE:E0:5E:F6:AC:56\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
18:14:10.386 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3972347809 1 udp 2122260223 ************ 65474 typ host generation 0 ufrag eAL7 network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"eAL7"}}
18:14:10.387 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3628061637 1 udp 2122194687 ************* 65475 typ host generation 0 ufrag eAL7 network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"eAL7"}}
18:14:10.388 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3760238036 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag Azpc network-id 2"}}
18:14:10.422 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3350027943 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag Azpc network-id 1"}}
18:14:10.456 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3760238036 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag Azpc network-id 2"}}
18:14:10.490 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3350027943 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag Azpc network-id 1"}}
18:14:10.524 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3760238036 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag Azpc network-id 2"}}
18:14:10.557 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:956795443 1 tcp 1518280447 ************ 49152 typ host tcptype passive generation 0 ufrag Azpc network-id 1"}}
18:14:10.591 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:512391488 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag Azpc network-id 2"}}
18:14:10.625 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:956795443 1 tcp 1518280447 ************ 49153 typ host tcptype passive generation 0 ufrag Azpc network-id 1"}}
18:14:10.658 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:512391488 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag Azpc network-id 2"}}
18:14:10.692 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:956795443 1 tcp 1518280447 ************ 49154 typ host tcptype passive generation 0 ufrag Azpc network-id 1"}}
18:14:10.726 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:512391488 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag Azpc network-id 2"}}
18:14:10.949 player 1 connection closed: 1001 - 
18:14:10.950 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
18:14:10.951 [37m[players] <-[32m {"type":"playerCount","count":0}
18:14:11.903 player 2 (::1) connected
18:14:11.904 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:14:11.904 [37m[players] <-[32m {"type":"playerCount","count":1}
18:14:11.904 [37m2 ->[34m {"type":"listStreamers"}
18:14:11.905 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
18:14:11.928 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
18:14:11.928 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
18:14:12.011 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 851468619897043719 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:CcJS\r\na=ice-pwd:ZCsnW7bFw4mkkALi2/vYMS3I\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E6:40:C9:19:5B:D9:7F:97:AF:4B:63:6A:4A:BD:22:B1:49:2A:D8:76:5D:7A:9D:1A:DF:D6:10:35:45:1C:3B:68\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 4111545743 482243015\r\na=ssrc:4111545743 cname:ovkSIgnlbHeNbRTs\r\na=ssrc:4111545743 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:482243015 cname:ovkSIgnlbHeNbRTs\r\na=ssrc:482243015 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:CcJS\r\na=ice-pwd:ZCsnW7bFw4mkkALi2/vYMS3I\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E6:40:C9:19:5B:D9:7F:97:AF:4B:63:6A:4A:BD:22:B1:49:2A:D8:76:5D:7A:9D:1A:DF:D6:10:35:45:1C:3B:68\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2724853849 cname:ovkSIgnlbHeNbRTs\r\na=ssrc:2724853849 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:CcJS\r\na=ice-pwd:ZCsnW7bFw4mkkALi2/vYMS3I\r\na=ice-options:trickle\r\na=fingerprint:sha-256 E6:40:C9:19:5B:D9:7F:97:AF:4B:63:6A:4A:BD:22:B1:49:2A:D8:76:5D:7A:9D:1A:DF:D6:10:35:45:1C:3B:68\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
18:14:12.043 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 420685785543159625 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uaEZ\r\na=ice-pwd:Tz7r21/wUV0eT+33urewmB0e\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9D:79:7E:D6:93:A3:1C:7A:6B:9C:00:31:90:50:02:AF:08:20:C9:C7:B1:A4:44:E4:FC:D6:8B:EC:A9:6B:F6:B8\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uaEZ\r\na=ice-pwd:Tz7r21/wUV0eT+33urewmB0e\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9D:79:7E:D6:93:A3:1C:7A:6B:9C:00:31:90:50:02:AF:08:20:C9:C7:B1:A4:44:E4:FC:D6:8B:EC:A9:6B:F6:B8\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- d9e08f79-92a6-4bfc-8d38-acbbb2020e41\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:4003079542 cname:W6zD4q9kQOey6tAF\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uaEZ\r\na=ice-pwd:Tz7r21/wUV0eT+33urewmB0e\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9D:79:7E:D6:93:A3:1C:7A:6B:9C:00:31:90:50:02:AF:08:20:C9:C7:B1:A4:44:E4:FC:D6:8B:EC:A9:6B:F6:B8\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
18:14:12.044 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:772727035 1 udp 2122260223 ************ 51424 typ host generation 0 ufrag uaEZ network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"uaEZ"}}
18:14:12.045 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2007829874 1 udp 2122194687 ************* 51425 typ host generation 0 ufrag uaEZ network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"uaEZ"}}
18:14:12.045 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1540350121 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag CcJS network-id 1"}}
18:14:12.078 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:294900296 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag CcJS network-id 2"}}
18:14:12.111 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1540350121 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag CcJS network-id 1"}}
18:14:12.144 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:294900296 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag CcJS network-id 2"}}
18:14:12.178 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1540350121 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag CcJS network-id 1"}}
18:14:12.212 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:294900296 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag CcJS network-id 2"}}
18:14:18.551 [37mStreamer Component ->[34m {"type":"ping","time":1754475258}
18:15:18.576 [37mStreamer Component ->[34m {"type":"ping","time":1754475318}
18:16:18.536 [37mStreamer Component ->[34m {"type":"ping","time":1754475378}
18:17:18.589 [37mStreamer Component ->[34m {"type":"ping","time":1754475438}
18:18:18.580 [37mStreamer Component ->[34m {"type":"ping","time":1754475498}
18:19:18.545 [37mStreamer Component ->[34m {"type":"ping","time":1754475558}
18:20:18.599 [37mStreamer Component ->[34m {"type":"ping","time":1754475618}
18:21:18.542 [37mStreamer Component ->[34m {"type":"ping","time":1754475678}
18:22:18.564 [37mStreamer Component ->[34m {"type":"ping","time":1754475738}
18:22:30.032 streamer Streamer Component disconnected: 1006 - 
18:22:30.033 unsubscribing all players on Streamer Component
18:22:30.034 player 2 connection closed: 1005 - 
18:22:30.035 [37m[players] <-[32m {"type":"playerCount","count":0}
18:22:32.121 Streamer connected: ::1
18:22:32.122 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:22:32.123 [37m::1 <-[32m {"type":"identify"}
18:22:32.253 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:22:32.254 Registered new streamer: Streamer Component
18:22:36.574 streamer Streamer Component disconnected: 1006 - 
18:22:36.576 unsubscribing all players on Streamer Component
18:22:38.622 Streamer connected: ::1
18:22:38.623 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:22:38.623 [37m::1 <-[32m {"type":"identify"}
18:22:38.720 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:22:38.721 Registered new streamer: Streamer Component
18:23:38.655 [37mStreamer Component ->[34m {"type":"ping","time":1754475818}
18:24:38.719 [37mStreamer Component ->[34m {"type":"ping","time":1754475880}
18:25:40.302 [37mStreamer Component ->[34m {"type":"ping","time":1754475940}
18:26:34.847 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
18:26:34.848 unsubscribing all players on Streamer Component
