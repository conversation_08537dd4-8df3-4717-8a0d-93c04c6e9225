15:57:21.858 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
15:57:21.897 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
15:57:21.899 Redirecting http->https
15:57:21.904 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
15:57:21.910 WebSocket listening for Streamer connections on :8888
15:57:21.911 WebSocket listening for SFU connections on :8889
15:57:21.912 WebSocket listening for Players connections on :80
15:57:21.913 Http listening on *: 80
15:57:21.913 Https listening on *: 443
15:59:05.021 Streamer connected: ::1
15:59:05.022 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:59:05.023 [37m::1 <-[32m {"type":"identify"}
15:59:05.519 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:59:05.519 Registered new streamer: Streamer Component
16:00:05.594 [37mStreamer Component ->[34m {"type":"ping","time":1754467205}
16:01:05.621 [37mStreamer Component ->[34m {"type":"ping","time":1754467265}
16:02:05.593 [37mStreamer Component ->[34m {"type":"ping","time":1754467325}
16:02:47.792 player 1 (::1) connected
16:02:47.793 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
16:02:47.793 [37m[players] <-[32m {"type":"playerCount","count":1}
16:02:47.793 [37m1 ->[34m {"type":"listStreamers"}
16:02:47.794 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
16:02:47.824 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
16:02:47.825 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
16:02:47.935 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 6602675747137980352 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:IC+D\r\na=ice-pwd:Fj5GMwpY7kZvQakpDPvScdKJ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:CB:49:53:15:1C:54:FB:1A:19:9F:FD:72:41:C8:F2:B6:DF:56:C9:89:96:FE:1A:E9:C3:04:75:24:67:3E:65\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2144059869 4274907724\r\na=ssrc:2144059869 cname:lr4/ev/pGOk7oUXw\r\na=ssrc:2144059869 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:4274907724 cname:lr4/ev/pGOk7oUXw\r\na=ssrc:4274907724 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:IC+D\r\na=ice-pwd:Fj5GMwpY7kZvQakpDPvScdKJ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:CB:49:53:15:1C:54:FB:1A:19:9F:FD:72:41:C8:F2:B6:DF:56:C9:89:96:FE:1A:E9:C3:04:75:24:67:3E:65\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3830617981 cname:lr4/ev/pGOk7oUXw\r\na=ssrc:3830617981 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:IC+D\r\na=ice-pwd:Fj5GMwpY7kZvQakpDPvScdKJ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:CB:49:53:15:1C:54:FB:1A:19:9F:FD:72:41:C8:F2:B6:DF:56:C9:89:96:FE:1A:E9:C3:04:75:24:67:3E:65\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
16:02:47.936 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3058372446 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag IC+D network-id 1"}}
16:02:47.968 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2445737005 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag IC+D network-id 2"}}
16:02:47.974 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 1298086574904043389 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:UBaO\r\na=ice-pwd:VkxVPxihhlCku8+iD7hgJ/OV\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:61:A7:0D:15:00:72:89:F6:5E:9C:44:36:66:E3:CD:0B:90:79:8E:3D:26:2A:45:DC:1B:FA:3D:44:D5:07:21\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:UBaO\r\na=ice-pwd:VkxVPxihhlCku8+iD7hgJ/OV\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:61:A7:0D:15:00:72:89:F6:5E:9C:44:36:66:E3:CD:0B:90:79:8E:3D:26:2A:45:DC:1B:FA:3D:44:D5:07:21\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 27956eba-fd26-4c4b-ba06-ba77653bd727\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1963295971 cname:pyJQRtFCoXEEkJDt\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:UBaO\r\na=ice-pwd:VkxVPxihhlCku8+iD7hgJ/OV\r\na=ice-options:trickle\r\na=fingerprint:sha-256 45:61:A7:0D:15:00:72:89:F6:5E:9C:44:36:66:E3:CD:0B:90:79:8E:3D:26:2A:45:DC:1B:FA:3D:44:D5:07:21\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
16:02:47.977 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1159935498 1 udp 2122260223 ************ 50264 typ host generation 0 ufrag UBaO network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"UBaO"}}
16:02:47.977 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3508448867 1 udp 2122194687 ************* 50265 typ host generation 0 ufrag UBaO network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"UBaO"}}
16:02:48.001 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3058372446 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag IC+D network-id 1"}}
16:02:48.035 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2445737005 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag IC+D network-id 2"}}
16:02:48.068 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3058372446 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag IC+D network-id 1"}}
16:02:48.102 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2445737005 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag IC+D network-id 2"}}
16:02:48.135 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1222761418 1 tcp 1518280447 ************ 49152 typ host tcptype passive generation 0 ufrag IC+D network-id 1"}}
16:02:48.169 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1869359289 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag IC+D network-id 2"}}
16:02:48.203 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1222761418 1 tcp 1518280447 ************ 49153 typ host tcptype passive generation 0 ufrag IC+D network-id 1"}}
16:02:48.236 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1869359289 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag IC+D network-id 2"}}
16:02:48.270 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1222761418 1 tcp 1518280447 ************ 49154 typ host tcptype passive generation 0 ufrag IC+D network-id 1"}}
16:02:48.303 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1869359289 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag IC+D network-id 2"}}
16:03:05.636 [37mStreamer Component ->[34m {"type":"ping","time":1754467385}
16:04:05.604 [37mStreamer Component ->[34m {"type":"ping","time":1754467445}
16:05:05.645 [37mStreamer Component ->[34m {"type":"ping","time":1754467505}
16:06:05.626 [37mStreamer Component ->[34m {"type":"ping","time":1754467565}
16:07:05.600 [37mStreamer Component ->[34m {"type":"ping","time":1754467625}
16:08:05.614 [37mStreamer Component ->[34m {"type":"ping","time":1754467685}
16:09:05.614 [37mStreamer Component ->[34m {"type":"ping","time":1754467745}
16:10:05.666 [37mStreamer Component ->[34m {"type":"ping","time":1754467805}
16:11:05.631 [37mStreamer Component ->[34m {"type":"ping","time":1754467865}
16:12:05.598 [37mStreamer Component ->[34m {"type":"ping","time":1754467925}
16:13:05.588 [37mStreamer Component ->[34m {"type":"ping","time":1754467985}
16:14:05.621 [37mStreamer Component ->[34m {"type":"ping","time":1754468045}
16:15:05.643 [37mStreamer Component ->[34m {"type":"ping","time":1754468105}
16:16:05.623 [37mStreamer Component ->[34m {"type":"ping","time":1754468165}
16:17:05.658 [37mStreamer Component ->[34m {"type":"ping","time":1754468225}
16:18:05.620 [37mStreamer Component ->[34m {"type":"ping","time":1754468285}
16:19:05.586 [37mStreamer Component ->[34m {"type":"ping","time":1754468345}
16:20:05.659 [37mStreamer Component ->[34m {"type":"ping","time":1754468405}
16:21:05.662 [37mStreamer Component ->[34m {"type":"ping","time":1754468465}
16:22:05.643 [37mStreamer Component ->[34m {"type":"ping","time":1754468525}
16:23:05.655 [37mStreamer Component ->[34m {"type":"ping","time":1754468585}
16:24:05.578 [37mStreamer Component ->[34m {"type":"ping","time":1754468645}
16:25:05.637 [37mStreamer Component ->[34m {"type":"ping","time":1754468705}
16:26:05.636 [37mStreamer Component ->[34m {"type":"ping","time":1754468765}
16:27:05.650 [37mStreamer Component ->[34m {"type":"ping","time":1754468825}
16:28:05.595 [37mStreamer Component ->[34m {"type":"ping","time":1754468885}
16:29:05.643 [37mStreamer Component ->[34m {"type":"ping","time":1754468945}
16:30:05.612 [37mStreamer Component ->[34m {"type":"ping","time":1754469005}
16:31:05.648 [37mStreamer Component ->[34m {"type":"ping","time":1754469065}
16:32:05.627 [37mStreamer Component ->[34m {"type":"ping","time":1754469125}
16:33:05.667 [37mStreamer Component ->[34m {"type":"ping","time":1754469185}
16:34:05.633 [37mStreamer Component ->[34m {"type":"ping","time":1754469245}
16:35:05.606 [37mStreamer Component ->[34m {"type":"ping","time":1754469305}
16:36:05.622 [37mStreamer Component ->[34m {"type":"ping","time":1754469365}
16:37:05.635 [37mStreamer Component ->[34m {"type":"ping","time":1754469425}
16:38:05.612 [37mStreamer Component ->[34m {"type":"ping","time":1754469485}
16:39:05.654 [37mStreamer Component ->[34m {"type":"ping","time":1754469545}
16:40:05.623 [37mStreamer Component ->[34m {"type":"ping","time":1754469605}
16:41:05.626 [37mStreamer Component ->[34m {"type":"ping","time":1754469665}
16:42:05.660 [37mStreamer Component ->[34m {"type":"ping","time":1754469725}
16:43:05.573 [37mStreamer Component ->[34m {"type":"ping","time":1754469785}
16:44:07.921 streamer Streamer Component disconnected: 1006 - 
16:44:07.922 unsubscribing all players on Streamer Component
16:44:07.923 player 1 connection closed: 1005 - 
16:44:07.924 [37m[players] <-[32m {"type":"playerCount","count":0}
16:50:04.261 player 2 (::1) connected
16:50:04.262 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
16:50:04.263 [37m[players] <-[32m {"type":"playerCount","count":1}
16:50:04.263 [37m2 ->[34m {"type":"listStreamers"}
16:50:04.263 [37m2 <-[32m {"type":"streamerList","ids":[]}
16:50:05.644 player 2 connection closed: 1001 - 
16:50:05.646 [37m[players] <-[32m {"type":"playerCount","count":0}
