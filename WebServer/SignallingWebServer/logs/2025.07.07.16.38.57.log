16:38:57.587 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
16:38:57.628 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
16:38:57.630 Redirecting http->https
16:38:57.636 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
16:38:57.643 WebSocket listening for Streamer connections on :8888
16:38:57.644 WebSocket listening for SFU connections on :8889
16:38:57.644 WebSocket listening for Players connections on :80
16:38:57.645 设置服务器端视频播放HTTP API
16:38:57.646 Http listening on *: 80
16:38:57.646 Https listening on *: 443
16:40:56.509 Streamer connected: ::1
16:40:56.509 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
16:40:56.510 [37m::1 <-[32m {"type":"identify"}
16:40:57.008 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
16:40:57.009 Registered new streamer: Streamer Component
16:41:06.187 player 1 (::ffff:*************) connected
16:41:06.189 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
16:41:06.189 [37m[players] <-[32m {"type":"playerCount","count":1}
16:41:06.190 [37m1 ->[34m {"type":"listStreamers"}
16:41:06.190 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
16:41:06.208 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
16:41:06.208 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
16:41:06.301 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 3084072882198541696 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mTbd\r\na=ice-pwd:4pmOUlrDMMApVaB/Xg0sFlp4\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D2:5C:B8:2C:7E:75:8D:2E:AC:52:4C:5F:53:50:E2:F6:07:79:88:EA:A9:79:C8:34:D9:57:B1:01:57:75:51:E7\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2783184091 489507754\r\na=ssrc:2783184091 cname:GuB+jZKgCf674JAP\r\na=ssrc:2783184091 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:489507754 cname:GuB+jZKgCf674JAP\r\na=ssrc:489507754 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mTbd\r\na=ice-pwd:4pmOUlrDMMApVaB/Xg0sFlp4\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D2:5C:B8:2C:7E:75:8D:2E:AC:52:4C:5F:53:50:E2:F6:07:79:88:EA:A9:79:C8:34:D9:57:B1:01:57:75:51:E7\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1379746385 cname:GuB+jZKgCf674JAP\r\na=ssrc:1379746385 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:mTbd\r\na=ice-pwd:4pmOUlrDMMApVaB/Xg0sFlp4\r\na=ice-options:trickle\r\na=fingerprint:sha-256 D2:5C:B8:2C:7E:75:8D:2E:AC:52:4C:5F:53:50:E2:F6:07:79:88:EA:A9:79:C8:34:D9:57:B1:01:57:75:51:E7\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
16:41:06.302 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:466709250 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag mTbd network-id 1"}}
16:41:06.325 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 9122705802529352279 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:W/Gw\r\na=ice-pwd:EFSu5ufk8cbd9uOCk6GwQwnF\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:68:2D:39:5E:EE:8A:57:7E:F3:90:A6:CD:E0:A0:0B:DA:CC:14:D4:60:9B:0B:25:6A:B1:E8:E5:8A:1B:F4:D7\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:W/Gw\r\na=ice-pwd:EFSu5ufk8cbd9uOCk6GwQwnF\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:68:2D:39:5E:EE:8A:57:7E:F3:90:A6:CD:E0:A0:0B:DA:CC:14:D4:60:9B:0B:25:6A:B1:E8:E5:8A:1B:F4:D7\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 993785e6-26c3-4a7a-8be4-91a416bcee4a\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:4048814417 cname:4eZhhrRkt5AVQ4Ts\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:W/Gw\r\na=ice-pwd:EFSu5ufk8cbd9uOCk6GwQwnF\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:68:2D:39:5E:EE:8A:57:7E:F3:90:A6:CD:E0:A0:0B:DA:CC:14:D4:60:9B:0B:25:6A:B1:E8:E5:8A:1B:F4:D7\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
16:41:06.326 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3418112154 1 udp 2122260223 ************* 51388 typ host generation 0 ufrag W/Gw network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"W/Gw"}}
16:41:06.327 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:4238694174 1 udp 2122194687 ************ 51389 typ host generation 0 ufrag W/Gw network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"W/Gw"}}
16:41:06.328 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1246776226 1 udp 2122129151 *************** 51390 typ host generation 0 ufrag W/Gw network-id 3","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"W/Gw"}}
16:41:06.329 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3409320165 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag mTbd network-id 4"}}
16:41:06.400 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:466709250 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag mTbd network-id 1"}}
16:41:06.442 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3409320165 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag mTbd network-id 4"}}
16:41:06.473 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:466709250 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag mTbd network-id 1"}}
16:41:06.492 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3409320165 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag mTbd network-id 4"}}
16:41:17.010 收到服务器端视频播放请求: {
  type: 'PLAY_SERVER_VIDEO',
  videoPath: 'videos/squat_start_technique.mp4',
  withAudio: true,
  audioFile: 'output.mp3'
}
16:41:17.011 在服务器端播放视频: videos/squat_start_technique.mp4
16:41:17.041 已连接到Electron视频播放器（服务器端）
16:41:17.041 已发送视频播放指令到Electron播放器: { type: 'PLAY_VIDEO', videoPath: 'videos/squat_start_technique.mp4' }
16:41:57.145 [37mStreamer Component ->[34m {"type":"ping","time":1754556117}
16:42:18.464 Electron播放器视频播放结束
16:42:18.467 与Electron视频播放器的连接已关闭（服务器端）
16:42:57.143 [37mStreamer Component ->[34m {"type":"ping","time":1754556177}
16:43:57.125 [37mStreamer Component ->[34m {"type":"ping","time":1754556237}
16:44:57.133 [37mStreamer Component ->[34m {"type":"ping","time":1754556297}
16:45:57.152 [37mStreamer Component ->[34m {"type":"ping","time":1754556357}
16:46:57.135 [37mStreamer Component ->[34m {"type":"ping","time":1754556417}
16:47:57.143 [37mStreamer Component ->[34m {"type":"ping","time":1754556477}
16:48:57.148 [37mStreamer Component ->[34m {"type":"ping","time":1754556537}
16:49:57.134 [37mStreamer Component ->[34m {"type":"ping","time":1754556597}
16:50:57.138 [37mStreamer Component ->[34m {"type":"ping","time":1754556657}
16:51:57.164 [37mStreamer Component ->[34m {"type":"ping","time":1754556717}
16:52:57.128 [37mStreamer Component ->[34m {"type":"ping","time":1754556777}
16:53:57.159 [37mStreamer Component ->[34m {"type":"ping","time":1754556837}
16:54:57.141 [37mStreamer Component ->[34m {"type":"ping","time":1754556897}
16:55:57.151 [37mStreamer Component ->[34m {"type":"ping","time":1754556957}
16:56:57.182 [37mStreamer Component ->[34m {"type":"ping","time":1754557017}
16:57:57.155 [37mStreamer Component ->[34m {"type":"ping","time":1754557077}
16:58:57.148 [37mStreamer Component ->[34m {"type":"ping","time":1754557137}
16:59:57.164 [37mStreamer Component ->[34m {"type":"ping","time":1754557197}
17:00:55.387 请求视频播放器配置文件: H:\cloud6\MomentumCloud\Renderer\VirtualHuman\Binaries\Win64\video-player-config.json
17:00:56.151 player 2 (::1) connected
17:00:56.151 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
17:00:56.151 [37m[players] <-[32m {"type":"playerCount","count":2}
17:00:56.152 [37m2 ->[34m {"type":"listStreamers"}
17:00:56.152 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
17:00:56.181 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
17:00:56.182 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
17:00:56.251 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 2504878767912332202 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:BfyE\r\na=ice-pwd:tB5to8AXHCTtLgCW1TFe15f4\r\na=ice-options:trickle\r\na=fingerprint:sha-256 85:16:5F:52:08:CC:62:E9:1C:76:15:82:CF:27:3B:25:58:F6:6C:19:D6:AE:A9:EE:D4:81:9D:03:C7:B4:7A:72\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3044464978 2908643282\r\na=ssrc:3044464978 cname:YzPY22yB5x0L4fwX\r\na=ssrc:3044464978 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2908643282 cname:YzPY22yB5x0L4fwX\r\na=ssrc:2908643282 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:BfyE\r\na=ice-pwd:tB5to8AXHCTtLgCW1TFe15f4\r\na=ice-options:trickle\r\na=fingerprint:sha-256 85:16:5F:52:08:CC:62:E9:1C:76:15:82:CF:27:3B:25:58:F6:6C:19:D6:AE:A9:EE:D4:81:9D:03:C7:B4:7A:72\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3326917422 cname:YzPY22yB5x0L4fwX\r\na=ssrc:3326917422 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:BfyE\r\na=ice-pwd:tB5to8AXHCTtLgCW1TFe15f4\r\na=ice-options:trickle\r\na=fingerprint:sha-256 85:16:5F:52:08:CC:62:E9:1C:76:15:82:CF:27:3B:25:58:F6:6C:19:D6:AE:A9:EE:D4:81:9D:03:C7:B4:7A:72\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:00:56.284 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2185952846 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag BfyE network-id 1"}}
17:00:56.298 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 6683133404250186700 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:/3Qa\r\na=ice-pwd:fPA9RYuC2sYegt6IoHOkiw1D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3B:8E:60:04:18:41:F3:0A:14:78:67:F1:33:2F:EB:36:51:86:40:36:49:84:7C:59:FB:49:25:55:4B:36:80:8F\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:/3Qa\r\na=ice-pwd:fPA9RYuC2sYegt6IoHOkiw1D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3B:8E:60:04:18:41:F3:0A:14:78:67:F1:33:2F:EB:36:51:86:40:36:49:84:7C:59:FB:49:25:55:4B:36:80:8F\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 90055e03-b850-4335-831f-ea3c3ee92eaf\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:880472287 cname:ueuGwXis4YpMMIQ1\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:/3Qa\r\na=ice-pwd:fPA9RYuC2sYegt6IoHOkiw1D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3B:8E:60:04:18:41:F3:0A:14:78:67:F1:33:2F:EB:36:51:86:40:36:49:84:7C:59:FB:49:25:55:4B:36:80:8F\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:00:56.302 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2369355886 1 udp 2122260223 ************ 55320 typ host generation 0 ufrag /3Qa network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"/3Qa"}}
17:00:56.303 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:299094419 1 udp 2122194687 ************* 55321 typ host generation 0 ufrag /3Qa network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"/3Qa"}}
17:00:56.318 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1387105705 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag BfyE network-id 4"}}
17:00:56.351 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2185952846 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag BfyE network-id 1"}}
17:00:56.385 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1387105705 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag BfyE network-id 4"}}
17:00:56.419 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2185952846 1 udp 2122260223 ************* 49155 typ host generation 0 ufrag BfyE network-id 1"}}
17:00:56.452 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1387105705 1 udp 2122194687 ************ 49155 typ host generation 0 ufrag BfyE network-id 4"}}
17:00:56.485 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2095065818 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag BfyE network-id 1"}}
17:00:56.518 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2886162749 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag BfyE network-id 4"}}
17:00:57.129 [37mStreamer Component ->[34m {"type":"ping","time":1754557257}
17:00:59.052 player 2 connection closed: 1001 - 
17:00:59.053 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"2"}
17:00:59.055 [37m[players] <-[32m {"type":"playerCount","count":1}
17:01:01.877 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
17:01:01.877 unsubscribing all players on Streamer Component
17:01:01.878 player 1 connection closed: 1005 - 
17:01:01.879 [37m[players] <-[32m {"type":"playerCount","count":0}
