16:38:57.587 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
16:38:57.628 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
16:38:57.630 Redirecting http->https
16:38:57.636 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
16:38:57.643 WebSocket listening for Streamer connections on :8888
16:38:57.644 WebSocket listening for SFU connections on :8889
16:38:57.644 WebSocket listening for Players connections on :80
16:38:57.645 设置服务器端视频播放HTTP API
16:38:57.646 Http listening on *: 80
16:38:57.646 Https listening on *: 443
