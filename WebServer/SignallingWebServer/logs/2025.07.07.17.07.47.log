17:07:47.184 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
17:07:47.226 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
17:07:47.228 Redirecting http->https
17:07:47.233 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
17:07:47.240 WebSocket listening for Streamer connections on :8888
17:07:47.241 WebSocket listening for SFU connections on :8889
17:07:47.242 WebSocket listening for Players connections on :80
17:07:47.243 设置服务器端视频播放HTTP API
17:07:47.244 Http listening on *: 80
17:07:47.244 Https listening on *: 443
