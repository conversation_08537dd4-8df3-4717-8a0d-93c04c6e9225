10:20:32.480 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
10:20:32.519 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
10:20:32.521 Redirecting http->https
10:20:32.526 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
10:20:32.533 WebSocket listening for Streamer connections on :8888
10:20:32.534 WebSocket listening for SFU connections on :8889
10:20:32.534 WebSocket listening for Players connections on :80
10:20:32.535 Http listening on *: 80
10:20:32.536 Https listening on *: 443
10:20:55.139 Streamer connected: ::1
10:20:55.140 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:20:55.140 [37m::1 <-[32m {"type":"identify"}
10:20:55.640 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
10:20:55.640 Registered new streamer: Streamer Component
10:20:57.845 player 1 (::1) connected
10:20:57.846 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:20:57.846 [37m[players] <-[32m {"type":"playerCount","count":1}
10:20:57.847 [37m1 ->[34m {"type":"listStreamers"}
10:20:57.848 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
10:20:57.870 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
10:20:57.871 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
10:20:57.970 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 8686363501410405338 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:HWqm\r\na=ice-pwd:pv3a67Wgi0cVR9RCI2rKd4nM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:A9:80:D9:9A:2A:C1:20:B1:75:C4:2E:EE:F5:FD:21:8F:A2:31:1A:37:52:75:5D:17:AA:25:E2:FE:72:93:77\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 595900814 967574007\r\na=ssrc:595900814 cname:7xG1F7i+J8JVzQR5\r\na=ssrc:595900814 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:967574007 cname:7xG1F7i+J8JVzQR5\r\na=ssrc:967574007 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:HWqm\r\na=ice-pwd:pv3a67Wgi0cVR9RCI2rKd4nM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:A9:80:D9:9A:2A:C1:20:B1:75:C4:2E:EE:F5:FD:21:8F:A2:31:1A:37:52:75:5D:17:AA:25:E2:FE:72:93:77\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2972493902 cname:7xG1F7i+J8JVzQR5\r\na=ssrc:2972493902 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:HWqm\r\na=ice-pwd:pv3a67Wgi0cVR9RCI2rKd4nM\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:A9:80:D9:9A:2A:C1:20:B1:75:C4:2E:EE:F5:FD:21:8F:A2:31:1A:37:52:75:5D:17:AA:25:E2:FE:72:93:77\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:20:57.972 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:838944557 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag HWqm network-id 1"}}
10:20:58.003 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1108073108 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag HWqm network-id 2"}}
10:20:58.012 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 3695595598559553189 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:oqxk\r\na=ice-pwd:6Sg8/bZD6SQL0R0/uIuCcqXK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F8:31:76:30:75:7B:5D:F9:DF:43:7D:0B:08:CA:04:A6:D7:BE:98:A6:9E:C7:DA:94:07:65:C8:4A:D9:C0:69:37\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:oqxk\r\na=ice-pwd:6Sg8/bZD6SQL0R0/uIuCcqXK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F8:31:76:30:75:7B:5D:F9:DF:43:7D:0B:08:CA:04:A6:D7:BE:98:A6:9E:C7:DA:94:07:65:C8:4A:D9:C0:69:37\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 9c4b1b26-ea76-4d1e-97a1-e488719b8918\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1791978829 cname:I7eQ533ETChuGWWn\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:oqxk\r\na=ice-pwd:6Sg8/bZD6SQL0R0/uIuCcqXK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F8:31:76:30:75:7B:5D:F9:DF:43:7D:0B:08:CA:04:A6:D7:BE:98:A6:9E:C7:DA:94:07:65:C8:4A:D9:C0:69:37\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:20:58.013 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3303233238 1 udp 2122260223 *********** 54171 typ host generation 0 ufrag oqxk network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"oqxk"}}
10:20:58.013 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:836846238 1 udp 2122194687 ************* 54172 typ host generation 0 ufrag oqxk network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"oqxk"}}
10:20:58.037 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:838944557 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag HWqm network-id 1"}}
10:20:58.070 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1108073108 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag HWqm network-id 2"}}
10:20:58.104 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:838944557 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag HWqm network-id 1"}}
10:20:58.138 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1108073108 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag HWqm network-id 2"}}
10:20:58.172 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3433796537 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag HWqm network-id 1"}}
10:20:58.205 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3164671488 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag HWqm network-id 2"}}
10:20:58.239 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3433796537 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag HWqm network-id 1"}}
10:20:58.272 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3164671488 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag HWqm network-id 2"}}
10:20:58.306 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3433796537 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag HWqm network-id 1"}}
10:20:58.340 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3164671488 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag HWqm network-id 2"}}
10:21:55.756 [37mStreamer Component ->[34m {"type":"ping","time":1753842115}
10:22:16.269 player 1 connection closed: 1001 - 
10:22:16.271 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
10:22:16.271 [37m[players] <-[32m {"type":"playerCount","count":0}
10:22:17.825 player 2 (::1) connected
10:22:17.825 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:22:17.826 [37m[players] <-[32m {"type":"playerCount","count":1}
10:22:17.826 [37m2 ->[34m {"type":"listStreamers"}
10:22:17.826 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
10:22:17.850 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
10:22:17.850 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
10:22:17.890 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 6225954522441842195 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:B3+c\r\na=ice-pwd:w8uxnp6r21BU7FEDlqA7SBSt\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3C:41:D6:38:78:90:43:BE:2E:AD:AF:D8:B8:26:A3:D0:EB:50:6B:C6:3C:43:19:B5:A1:3B:2F:05:87:4D:04:F3\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2408133727 816070064\r\na=ssrc:2408133727 cname:kpLEEBH0ntaKbZAE\r\na=ssrc:2408133727 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:816070064 cname:kpLEEBH0ntaKbZAE\r\na=ssrc:816070064 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:B3+c\r\na=ice-pwd:w8uxnp6r21BU7FEDlqA7SBSt\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3C:41:D6:38:78:90:43:BE:2E:AD:AF:D8:B8:26:A3:D0:EB:50:6B:C6:3C:43:19:B5:A1:3B:2F:05:87:4D:04:F3\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3829539743 cname:kpLEEBH0ntaKbZAE\r\na=ssrc:3829539743 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:B3+c\r\na=ice-pwd:w8uxnp6r21BU7FEDlqA7SBSt\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3C:41:D6:38:78:90:43:BE:2E:AD:AF:D8:B8:26:A3:D0:EB:50:6B:C6:3C:43:19:B5:A1:3B:2F:05:87:4D:04:F3\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:22:17.923 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3801301993 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag B3+c network-id 1"}}
10:22:17.931 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 722358678271179998 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Iit7\r\na=ice-pwd:v0byG4mONjtZxHghnD9zRbNv\r\na=ice-options:trickle\r\na=fingerprint:sha-256 99:BD:F8:C6:69:6E:22:66:7F:F1:1B:AD:49:20:25:73:D2:81:B7:3E:54:E9:9B:F4:EE:76:0C:A0:E5:A9:3A:2E\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Iit7\r\na=ice-pwd:v0byG4mONjtZxHghnD9zRbNv\r\na=ice-options:trickle\r\na=fingerprint:sha-256 99:BD:F8:C6:69:6E:22:66:7F:F1:1B:AD:49:20:25:73:D2:81:B7:3E:54:E9:9B:F4:EE:76:0C:A0:E5:A9:3A:2E\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 113beec5-0d7a-42b8-898a-d2e37a89b763\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1331524477 cname:dlQ/RJK7hHfx3Y2p\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Iit7\r\na=ice-pwd:v0byG4mONjtZxHghnD9zRbNv\r\na=ice-options:trickle\r\na=fingerprint:sha-256 99:BD:F8:C6:69:6E:22:66:7F:F1:1B:AD:49:20:25:73:D2:81:B7:3E:54:E9:9B:F4:EE:76:0C:A0:E5:A9:3A:2E\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:22:17.932 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2257497390 1 udp 2122260223 *********** 63537 typ host generation 0 ufrag Iit7 network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"Iit7"}}
10:22:17.932 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1938602342 1 udp 2122194687 ************* 63538 typ host generation 0 ufrag Iit7 network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"Iit7"}}
10:22:17.957 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2459557456 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag B3+c network-id 2"}}
10:22:17.990 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3801301993 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag B3+c network-id 1"}}
10:22:18.024 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2459557456 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag B3+c network-id 2"}}
10:22:18.057 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3801301993 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag B3+c network-id 1"}}
10:22:18.090 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2459557456 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag B3+c network-id 2"}}
10:22:55.785 [37mStreamer Component ->[34m {"type":"ping","time":1753842175}
10:23:55.767 [37mStreamer Component ->[34m {"type":"ping","time":1753842235}
10:24:02.747 player 2 connection closed: 1001 - 
10:24:02.751 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"2"}
10:24:02.752 [37m[players] <-[32m {"type":"playerCount","count":0}
10:24:03.465 player 3 (::1) connected
10:24:03.466 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:24:03.467 [37m[players] <-[32m {"type":"playerCount","count":1}
10:24:03.468 [37m3 ->[34m {"type":"listStreamers"}
10:24:03.468 [37m3 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
10:24:03.498 [37m3 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
10:24:03.500 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"3","dataChannel":true,"sfu":false,"sendOffer":true}
10:24:03.575 [37mStreamer Component -> 3[36m {"type":"offer","playerId":3,"sdp":"v=0\r\no=- 2420746046759510153 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:pv1W\r\na=ice-pwd:fodnWzimktv/fWOCe8T7uWtG\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1F:20:C8:1C:C2:54:9E:0B:D6:F0:3F:9A:FC:2D:55:27:0A:35:85:9F:AD:D1:58:7A:58:DC:9C:04:97:8E:54:7C\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 785604417 2651824708\r\na=ssrc:785604417 cname:/FJFfk/0953c2742\r\na=ssrc:785604417 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2651824708 cname:/FJFfk/0953c2742\r\na=ssrc:2651824708 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:pv1W\r\na=ice-pwd:fodnWzimktv/fWOCe8T7uWtG\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1F:20:C8:1C:C2:54:9E:0B:D6:F0:3F:9A:FC:2D:55:27:0A:35:85:9F:AD:D1:58:7A:58:DC:9C:04:97:8E:54:7C\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:892021194 cname:/FJFfk/0953c2742\r\na=ssrc:892021194 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:pv1W\r\na=ice-pwd:fodnWzimktv/fWOCe8T7uWtG\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1F:20:C8:1C:C2:54:9E:0B:D6:F0:3F:9A:FC:2D:55:27:0A:35:85:9F:AD:D1:58:7A:58:DC:9C:04:97:8E:54:7C\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:24:03.608 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:158844656 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag pv1W network-id 1"}}
10:24:03.623 [37m3 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 105343145314557983 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:QHKA\r\na=ice-pwd:jvpjgpxMWn+Df33e1xt1BwdF\r\na=ice-options:trickle\r\na=fingerprint:sha-256 26:11:C2:FF:95:1D:22:EA:E2:7C:5D:7B:03:D9:FB:74:A5:20:66:C1:D7:B0:16:68:A0:E3:70:1E:BC:B2:20:E5\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:QHKA\r\na=ice-pwd:jvpjgpxMWn+Df33e1xt1BwdF\r\na=ice-options:trickle\r\na=fingerprint:sha-256 26:11:C2:FF:95:1D:22:EA:E2:7C:5D:7B:03:D9:FB:74:A5:20:66:C1:D7:B0:16:68:A0:E3:70:1E:BC:B2:20:E5\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 49cd3270-a376-45cf-8b6a-bcbce4a58a08\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:4146804234 cname:WtjMg+u7IUgf5R6I\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:QHKA\r\na=ice-pwd:jvpjgpxMWn+Df33e1xt1BwdF\r\na=ice-options:trickle\r\na=fingerprint:sha-256 26:11:C2:FF:95:1D:22:EA:E2:7C:5D:7B:03:D9:FB:74:A5:20:66:C1:D7:B0:16:68:A0:E3:70:1E:BC:B2:20:E5\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:24:03.634 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:439721086 1 udp 2122260223 *********** 49847 typ host generation 0 ufrag QHKA network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"QHKA"}}
10:24:03.636 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1657231304 1 udp 2122194687 ************* 49848 typ host generation 0 ufrag QHKA network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"QHKA"}}
10:24:03.642 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2038258505 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag pv1W network-id 2"}}
10:24:03.676 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:158844656 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag pv1W network-id 1"}}
10:24:03.709 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2038258505 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag pv1W network-id 2"}}
10:24:03.743 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:158844656 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag pv1W network-id 1"}}
10:24:03.777 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2038258505 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag pv1W network-id 2"}}
10:24:55.762 [37mStreamer Component ->[34m {"type":"ping","time":1753842295}
10:25:55.775 [37mStreamer Component ->[34m {"type":"ping","time":1753842355}
10:26:55.744 [37mStreamer Component ->[34m {"type":"ping","time":1753842415}
10:27:23.269 player 3 connection closed: 1001 - 
10:27:23.273 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"3"}
10:27:23.274 [37m[players] <-[32m {"type":"playerCount","count":0}
10:27:24.054 player 4 (::1) connected
10:27:24.056 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:27:24.056 [37m[players] <-[32m {"type":"playerCount","count":1}
10:27:24.060 [37m4 ->[34m {"type":"listStreamers"}
10:27:24.060 [37m4 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
10:27:24.093 [37m4 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
10:27:24.094 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"4","dataChannel":true,"sfu":false,"sendOffer":true}
10:27:24.155 [37mStreamer Component -> 4[36m {"type":"offer","playerId":4,"sdp":"v=0\r\no=- 944797095959682515 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:QuT+\r\na=ice-pwd:OzhnPGQfdChtXwOQ8/YqTG1S\r\na=ice-options:trickle\r\na=fingerprint:sha-256 18:4F:16:25:02:89:D7:B9:FB:C0:51:18:25:07:7C:CC:7A:1A:40:B3:A8:60:20:C4:D3:0B:2A:05:62:1D:2D:17\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1990199056 1026196723\r\na=ssrc:1990199056 cname:jICM4Zwt98dDt3K4\r\na=ssrc:1990199056 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1026196723 cname:jICM4Zwt98dDt3K4\r\na=ssrc:1026196723 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:QuT+\r\na=ice-pwd:OzhnPGQfdChtXwOQ8/YqTG1S\r\na=ice-options:trickle\r\na=fingerprint:sha-256 18:4F:16:25:02:89:D7:B9:FB:C0:51:18:25:07:7C:CC:7A:1A:40:B3:A8:60:20:C4:D3:0B:2A:05:62:1D:2D:17\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:5212211 cname:jICM4Zwt98dDt3K4\r\na=ssrc:5212211 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:QuT+\r\na=ice-pwd:OzhnPGQfdChtXwOQ8/YqTG1S\r\na=ice-options:trickle\r\na=fingerprint:sha-256 18:4F:16:25:02:89:D7:B9:FB:C0:51:18:25:07:7C:CC:7A:1A:40:B3:A8:60:20:C4:D3:0B:2A:05:62:1D:2D:17\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:27:24.188 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2250225276 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag QuT+ network-id 1"}}
10:27:24.222 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3866525817 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag QuT+ network-id 2"}}
10:27:24.229 [37m4 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 3769701364333153982 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:DI5J\r\na=ice-pwd:HVjDuuS59WgfAlWEYK4w8nwB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3D:07:F2:C8:21:63:C2:6A:8F:21:C9:C7:48:DD:B5:2E:26:DF:51:E3:D7:CA:40:4C:D6:4C:00:28:F4:42:E5:AF\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:DI5J\r\na=ice-pwd:HVjDuuS59WgfAlWEYK4w8nwB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3D:07:F2:C8:21:63:C2:6A:8F:21:C9:C7:48:DD:B5:2E:26:DF:51:E3:D7:CA:40:4C:D6:4C:00:28:F4:42:E5:AF\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 86b3ea2c-d3bb-424d-a2a3-bb921315653f\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2772919069 cname:YWMM7hcgykNzFpil\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:DI5J\r\na=ice-pwd:HVjDuuS59WgfAlWEYK4w8nwB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3D:07:F2:C8:21:63:C2:6A:8F:21:C9:C7:48:DD:B5:2E:26:DF:51:E3:D7:CA:40:4C:D6:4C:00:28:F4:42:E5:AF\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:27:24.255 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2250225276 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag QuT+ network-id 1"}}
10:27:24.261 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2853498506 1 udp 2122260223 *********** 57956 typ host generation 0 ufrag DI5J network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"DI5J"}}
10:27:24.261 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1595341506 1 udp 2122194687 ************* 57957 typ host generation 0 ufrag DI5J network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"DI5J"}}
10:27:24.289 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3866525817 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag QuT+ network-id 2"}}
10:27:24.323 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2250225276 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag QuT+ network-id 1"}}
10:27:24.356 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3866525817 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag QuT+ network-id 2"}}
10:27:24.390 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4174402788 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag QuT+ network-id 1"}}
10:27:24.424 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2562286305 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag QuT+ network-id 2"}}
10:27:24.457 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4174402788 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag QuT+ network-id 1"}}
10:27:24.491 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2562286305 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag QuT+ network-id 2"}}
10:27:24.525 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4174402788 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag QuT+ network-id 1"}}
10:27:24.558 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2562286305 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag QuT+ network-id 2"}}
10:27:55.752 [37mStreamer Component ->[34m {"type":"ping","time":1753842475}
10:28:55.770 [37mStreamer Component ->[34m {"type":"ping","time":1753842535}
10:29:55.779 [37mStreamer Component ->[34m {"type":"ping","time":1753842595}
10:30:21.675 player 4 connection closed: 1001 - 
10:30:21.679 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"4"}
10:30:21.679 [37m[players] <-[32m {"type":"playerCount","count":0}
10:30:22.444 player 5 (::1) connected
10:30:22.445 [37m5 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:30:22.445 [37m[players] <-[32m {"type":"playerCount","count":1}
10:30:22.445 [37m5 ->[34m {"type":"listStreamers"}
10:30:22.446 [37m5 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
10:30:22.475 [37m5 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
10:30:22.475 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"5","dataChannel":true,"sfu":false,"sendOffer":true}
10:30:22.528 [37mStreamer Component -> 5[36m {"type":"offer","playerId":5,"sdp":"v=0\r\no=- 2147716258401404652 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:e+RF\r\na=ice-pwd:eGHB/it2C8GSizLeGzzh4cVc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B7:15:02:D0:9B:6E:CB:AF:E8:7A:89:44:90:14:36:1B:B3:57:81:8C:84:40:96:C5:8E:FF:DA:8E:FF:DF:17:6F\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2470665432 3578868765\r\na=ssrc:2470665432 cname:CAT2hX+3m5ZyriQX\r\na=ssrc:2470665432 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3578868765 cname:CAT2hX+3m5ZyriQX\r\na=ssrc:3578868765 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:e+RF\r\na=ice-pwd:eGHB/it2C8GSizLeGzzh4cVc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B7:15:02:D0:9B:6E:CB:AF:E8:7A:89:44:90:14:36:1B:B3:57:81:8C:84:40:96:C5:8E:FF:DA:8E:FF:DF:17:6F\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2572509053 cname:CAT2hX+3m5ZyriQX\r\na=ssrc:2572509053 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:e+RF\r\na=ice-pwd:eGHB/it2C8GSizLeGzzh4cVc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 B7:15:02:D0:9B:6E:CB:AF:E8:7A:89:44:90:14:36:1B:B3:57:81:8C:84:40:96:C5:8E:FF:DA:8E:FF:DF:17:6F\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:30:22.562 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:539835679 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag e+RF network-id 1"}}
10:30:22.586 [37m5 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 653201756426308569 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:jXVk\r\na=ice-pwd:TuJe0PZQX4LgQTcsrI+bbNaR\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:D1:8E:D2:87:FC:4A:08:3C:E1:57:FD:A4:3C:98:D7:98:DA:1F:A8:D7:34:33:8B:80:48:31:69:0F:A5:0F:40\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:jXVk\r\na=ice-pwd:TuJe0PZQX4LgQTcsrI+bbNaR\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:D1:8E:D2:87:FC:4A:08:3C:E1:57:FD:A4:3C:98:D7:98:DA:1F:A8:D7:34:33:8B:80:48:31:69:0F:A5:0F:40\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- eaa3569b-bd57-47d6-a041-cfc32b03a409\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3211743805 cname:CSkyOTIgDWkC6vNP\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:jXVk\r\na=ice-pwd:TuJe0PZQX4LgQTcsrI+bbNaR\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:D1:8E:D2:87:FC:4A:08:3C:E1:57:FD:A4:3C:98:D7:98:DA:1F:A8:D7:34:33:8B:80:48:31:69:0F:A5:0F:40\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:30:22.595 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2267991966 1 udp 2122260223 *********** 51651 typ host generation 0 ufrag jXVk network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"jXVk"}}
10:30:22.595 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1915542486 1 udp 2122194687 ************* 51652 typ host generation 0 ufrag jXVk network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"jXVk"}}
10:30:22.596 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1078201114 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag e+RF network-id 2"}}
10:30:22.629 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:539835679 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag e+RF network-id 1"}}
10:30:22.662 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1078201114 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag e+RF network-id 2"}}
10:30:22.696 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:539835679 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag e+RF network-id 1"}}
10:30:22.730 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1078201114 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag e+RF network-id 2"}}
10:30:22.764 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1591926663 1 tcp 1518280447 *********** 49152 typ host tcptype passive generation 0 ufrag e+RF network-id 1"}}
10:30:22.797 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1049356674 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag e+RF network-id 2"}}
10:30:22.831 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1591926663 1 tcp 1518280447 *********** 49153 typ host tcptype passive generation 0 ufrag e+RF network-id 1"}}
10:30:22.865 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1049356674 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag e+RF network-id 2"}}
10:30:22.898 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1591926663 1 tcp 1518280447 *********** 49154 typ host tcptype passive generation 0 ufrag e+RF network-id 1"}}
10:30:22.932 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1049356674 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag e+RF network-id 2"}}
10:30:55.749 [37mStreamer Component ->[34m {"type":"ping","time":1753842655}
10:31:55.728 [37mStreamer Component ->[34m {"type":"ping","time":1753842715}
10:32:55.787 [37mStreamer Component ->[34m {"type":"ping","time":1753842775}
10:33:55.736 [37mStreamer Component ->[34m {"type":"ping","time":1753842835}
10:34:55.764 [37mStreamer Component ->[34m {"type":"ping","time":1753842895}
10:35:55.767 [37mStreamer Component ->[34m {"type":"ping","time":1753842955}
10:36:55.744 [37mStreamer Component ->[34m {"type":"ping","time":1753843015}
10:37:55.763 [37mStreamer Component ->[34m {"type":"ping","time":1753843075}
10:38:55.782 [37mStreamer Component ->[34m {"type":"ping","time":1753843135}
10:39:55.786 [37mStreamer Component ->[34m {"type":"ping","time":1753843195}
10:40:55.782 [37mStreamer Component ->[34m {"type":"ping","time":1753843255}
10:41:55.735 [37mStreamer Component ->[34m {"type":"ping","time":1753843315}
10:42:55.777 [37mStreamer Component ->[34m {"type":"ping","time":1753843375}
10:43:55.749 [37mStreamer Component ->[34m {"type":"ping","time":1753843435}
10:44:55.816 [37mStreamer Component ->[34m {"type":"ping","time":1753843495}
10:45:28.328 Upload 半身（黄花)5x9.jpg
10:45:57.479 streamer Streamer Component disconnected: 1006 - 
10:45:57.480 unsubscribing all players on Streamer Component
10:45:57.481 player 5 connection closed: 1005 - 
10:45:57.481 [37m[players] <-[32m {"type":"playerCount","count":0}
10:46:21.341 Streamer connected: ::1
10:46:21.341 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:46:21.342 [37m::1 <-[32m {"type":"identify"}
10:46:21.837 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
10:46:21.838 Registered new streamer: Streamer Component
10:46:36.077 player 6 (::1) connected
10:46:36.078 [37m6 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:46:36.078 [37m[players] <-[32m {"type":"playerCount","count":1}
10:46:36.079 [37m6 ->[34m {"type":"listStreamers"}
10:46:36.079 [37m6 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
10:46:36.103 [37m6 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
10:46:36.103 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"6","dataChannel":true,"sfu":false,"sendOffer":true}
10:46:36.200 [37mStreamer Component -> 6[36m {"type":"offer","playerId":6,"sdp":"v=0\r\no=- 7448426524471351599 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:PoKw\r\na=ice-pwd:xpWuNKetxEWfMT7/sfklmX5C\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5F:5D:0E:F8:33:09:49:DD:BF:34:46:7C:E7:B0:48:38:DB:C0:32:84:A5:E6:96:E0:13:85:50:E9:E8:65:C2:1F\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 544184132 1823099868\r\na=ssrc:544184132 cname:Et9wOzebtBlUC2Hm\r\na=ssrc:544184132 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1823099868 cname:Et9wOzebtBlUC2Hm\r\na=ssrc:1823099868 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:PoKw\r\na=ice-pwd:xpWuNKetxEWfMT7/sfklmX5C\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5F:5D:0E:F8:33:09:49:DD:BF:34:46:7C:E7:B0:48:38:DB:C0:32:84:A5:E6:96:E0:13:85:50:E9:E8:65:C2:1F\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:4176315676 cname:Et9wOzebtBlUC2Hm\r\na=ssrc:4176315676 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:PoKw\r\na=ice-pwd:xpWuNKetxEWfMT7/sfklmX5C\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5F:5D:0E:F8:33:09:49:DD:BF:34:46:7C:E7:B0:48:38:DB:C0:32:84:A5:E6:96:E0:13:85:50:E9:E8:65:C2:1F\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:46:36.202 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3529811748 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag PoKw network-id 1"}}
10:46:36.232 [37m6 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 3584432194013148533 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:JlXE\r\na=ice-pwd:kQDWL2CHICO94rZ2xlAyV070\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CC:6A:97:44:BF:35:20:A7:AF:59:EE:E5:E0:1D:A6:87:C5:DB:26:F4:03:1C:20:AB:AF:A0:1F:29:71:F5:93:9B\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:JlXE\r\na=ice-pwd:kQDWL2CHICO94rZ2xlAyV070\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CC:6A:97:44:BF:35:20:A7:AF:59:EE:E5:E0:1D:A6:87:C5:DB:26:F4:03:1C:20:AB:AF:A0:1F:29:71:F5:93:9B\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- dc141180-9937-4e24-b1b0-5efa3fa1d5d5\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1666935507 cname:U5OIqVnDJgNFyam2\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:JlXE\r\na=ice-pwd:kQDWL2CHICO94rZ2xlAyV070\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CC:6A:97:44:BF:35:20:A7:AF:59:EE:E5:E0:1D:A6:87:C5:DB:26:F4:03:1C:20:AB:AF:A0:1F:29:71:F5:93:9B\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
10:46:36.233 [37m6 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3312154723 1 udp 2122260223 *********** 54960 typ host generation 0 ufrag JlXE network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"JlXE"}}
10:46:36.234 [37m6 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3180966869 1 udp 2122194687 ************* 54961 typ host generation 0 ufrag JlXE network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"JlXE"}}
10:46:36.234 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2725132957 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag PoKw network-id 2"}}
10:46:36.266 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3529811748 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag PoKw network-id 1"}}
10:46:36.300 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2725132957 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag PoKw network-id 2"}}
10:46:36.334 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3529811748 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag PoKw network-id 1"}}
10:46:36.367 [37mStreamer Component -> 6[36m {"type":"iceCandidate","playerId":6,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2725132957 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag PoKw network-id 2"}}
10:47:21.892 [37mStreamer Component ->[34m {"type":"ping","time":1753843641}
10:48:21.903 [37mStreamer Component ->[34m {"type":"ping","time":1753843701}
10:49:21.897 [37mStreamer Component ->[34m {"type":"ping","time":1753843761}
10:50:21.910 [37mStreamer Component ->[34m {"type":"ping","time":1753843821}
10:51:21.903 [37mStreamer Component ->[34m {"type":"ping","time":1753843881}
10:52:21.958 [37mStreamer Component ->[34m {"type":"ping","time":1753843941}
10:53:21.906 [37mStreamer Component ->[34m {"type":"ping","time":1753844001}
10:54:21.904 [37mStreamer Component ->[34m {"type":"ping","time":1753844061}
10:55:06.827 player 6 connection closed: 1001 - 
10:55:06.829 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"6"}
10:55:06.829 [37m[players] <-[32m {"type":"playerCount","count":0}
