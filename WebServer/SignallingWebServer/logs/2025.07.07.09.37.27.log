09:37:27.498 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
09:37:27.539 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
09:37:27.540 Redirecting http->https
09:37:27.546 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
09:37:27.552 WebSocket listening for Streamer connections on :8888
09:37:27.553 WebSocket listening for SFU connections on :8889
09:37:27.554 WebSocket listening for Players connections on :80
09:37:27.555 Http listening on *: 80
09:37:27.555 Https listening on *: 443
09:37:49.479 Streamer connected: ::1
09:37:49.480 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:37:49.481 [37m::1 <-[32m {"type":"identify"}
09:37:50.018 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
09:37:50.018 Registered new streamer: Streamer Component
09:38:06.103 player 1 (::1) connected
09:38:06.104 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:38:06.104 [37m[players] <-[32m {"type":"playerCount","count":1}
09:38:06.104 [37m1 ->[34m {"type":"listStreamers"}
09:38:06.105 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
09:38:06.130 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
09:38:06.130 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
09:38:06.220 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 4580621373531387649 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:3CAp\r\na=ice-pwd:Y/rxDlY6EL/GEFsgtOK4JHLU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 74:71:66:9A:8D:9A:98:50:C1:3B:36:13:0A:35:F9:FF:4A:68:6A:20:B4:0F:12:5E:A2:F2:DF:48:E7:CD:8F:4F\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2573742887 3920938544\r\na=ssrc:2573742887 cname:ldhAb5u9KdGnPDbR\r\na=ssrc:2573742887 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3920938544 cname:ldhAb5u9KdGnPDbR\r\na=ssrc:3920938544 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:3CAp\r\na=ice-pwd:Y/rxDlY6EL/GEFsgtOK4JHLU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 74:71:66:9A:8D:9A:98:50:C1:3B:36:13:0A:35:F9:FF:4A:68:6A:20:B4:0F:12:5E:A2:F2:DF:48:E7:CD:8F:4F\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3804399288 cname:ldhAb5u9KdGnPDbR\r\na=ssrc:3804399288 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:3CAp\r\na=ice-pwd:Y/rxDlY6EL/GEFsgtOK4JHLU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 74:71:66:9A:8D:9A:98:50:C1:3B:36:13:0A:35:F9:FF:4A:68:6A:20:B4:0F:12:5E:A2:F2:DF:48:E7:CD:8F:4F\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:38:06.251 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 4326247586681959663 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:IkfN\r\na=ice-pwd:dYZPufT1yuncuwMjZoTtLfVo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:48:D4:1C:D3:C2:E8:D7:A6:9B:C4:89:FF:24:A0:68:78:2B:0C:E1:1A:62:4E:BB:52:EB:03:9A:11:B9:E6:2B\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:IkfN\r\na=ice-pwd:dYZPufT1yuncuwMjZoTtLfVo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:48:D4:1C:D3:C2:E8:D7:A6:9B:C4:89:FF:24:A0:68:78:2B:0C:E1:1A:62:4E:BB:52:EB:03:9A:11:B9:E6:2B\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- f610cd29-4b3e-4f2b-8d32-6f54d10a583d\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:74199124 cname:gc0OBsr6CMsZv8Si\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:IkfN\r\na=ice-pwd:dYZPufT1yuncuwMjZoTtLfVo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 94:48:D4:1C:D3:C2:E8:D7:A6:9B:C4:89:FF:24:A0:68:78:2B:0C:E1:1A:62:4E:BB:52:EB:03:9A:11:B9:E6:2B\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:38:06.251 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2036980868 1 udp 2122260223 ************ 49788 typ host generation 0 ufrag IkfN network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"IkfN"}}
09:38:06.253 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2671802327 1 udp 2122194687 ************* 49789 typ host generation 0 ufrag IkfN network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"IkfN"}}
09:38:06.255 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:441636413 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag 3CAp network-id 1"}}
09:38:06.287 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1261094250 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag 3CAp network-id 4"}}
09:38:06.321 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:441636413 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag 3CAp network-id 1"}}
09:38:06.355 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1261094250 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag 3CAp network-id 4"}}
09:38:06.389 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:441636413 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag 3CAp network-id 1"}}
09:38:06.423 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1261094250 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag 3CAp network-id 4"}}
09:38:46.146 player 1 connection closed: 1001 - 
09:38:46.147 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
09:38:46.148 [37m[players] <-[32m {"type":"playerCount","count":0}
09:38:47.656 player 2 (::1) connected
09:38:47.657 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:38:47.657 [37m[players] <-[32m {"type":"playerCount","count":1}
09:38:47.659 [37m2 ->[34m {"type":"listStreamers"}
09:38:47.659 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
09:38:47.681 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
09:38:47.681 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
09:38:47.731 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 2331920554723007504 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:2W6V\r\na=ice-pwd:gAbpz/+gXY/6iEWLTFF2yjO0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 46:99:D8:CD:90:6D:29:A4:EE:82:6D:19:4F:80:EB:55:15:CA:3F:53:BA:AB:24:EC:D6:E1:5F:9E:D4:B1:6E:84\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1505668546 3662428741\r\na=ssrc:1505668546 cname:oShayiUoQ7WabsaH\r\na=ssrc:1505668546 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3662428741 cname:oShayiUoQ7WabsaH\r\na=ssrc:3662428741 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:2W6V\r\na=ice-pwd:gAbpz/+gXY/6iEWLTFF2yjO0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 46:99:D8:CD:90:6D:29:A4:EE:82:6D:19:4F:80:EB:55:15:CA:3F:53:BA:AB:24:EC:D6:E1:5F:9E:D4:B1:6E:84\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:4213162378 cname:oShayiUoQ7WabsaH\r\na=ssrc:4213162378 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:2W6V\r\na=ice-pwd:gAbpz/+gXY/6iEWLTFF2yjO0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 46:99:D8:CD:90:6D:29:A4:EE:82:6D:19:4F:80:EB:55:15:CA:3F:53:BA:AB:24:EC:D6:E1:5F:9E:D4:B1:6E:84\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:38:47.760 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 285420963407577359 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:y9CC\r\na=ice-pwd:crO4Ti5VSfnQ+ZtHmG10F7b+\r\na=ice-options:trickle\r\na=fingerprint:sha-256 30:5A:47:C3:C7:3D:27:E3:99:25:D0:BD:BA:A4:A0:3A:2C:D7:4C:B6:DC:20:87:B7:FD:BE:3D:FF:26:84:D1:34\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:y9CC\r\na=ice-pwd:crO4Ti5VSfnQ+ZtHmG10F7b+\r\na=ice-options:trickle\r\na=fingerprint:sha-256 30:5A:47:C3:C7:3D:27:E3:99:25:D0:BD:BA:A4:A0:3A:2C:D7:4C:B6:DC:20:87:B7:FD:BE:3D:FF:26:84:D1:34\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- c0b06f13-8b0b-4665-bd4f-35ccd30ce35a\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3335429414 cname:YLafdOr50NPr4H1T\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:y9CC\r\na=ice-pwd:crO4Ti5VSfnQ+ZtHmG10F7b+\r\na=ice-options:trickle\r\na=fingerprint:sha-256 30:5A:47:C3:C7:3D:27:E3:99:25:D0:BD:BA:A4:A0:3A:2C:D7:4C:B6:DC:20:87:B7:FD:BE:3D:FF:26:84:D1:34\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:38:47.761 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2666559758 1 udp 2122260223 ************ 53078 typ host generation 0 ufrag y9CC network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"y9CC"}}
09:38:47.761 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2027541085 1 udp 2122194687 ************* 53079 typ host generation 0 ufrag y9CC network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"y9CC"}}
09:38:47.764 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2018284681 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag 2W6V network-id 1"}}
09:38:47.798 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2366777549 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag 2W6V network-id 4"}}
09:38:47.832 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2018284681 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag 2W6V network-id 1"}}
09:38:47.866 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2366777549 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag 2W6V network-id 4"}}
09:38:47.900 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2018284681 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag 2W6V network-id 1"}}
09:38:47.934 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2366777549 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag 2W6V network-id 4"}}
09:38:47.968 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2263236637 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag 2W6V network-id 1"}}
09:38:48.002 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1941503065 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag 2W6V network-id 4"}}
09:38:48.036 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2856325382 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag 2W6V network-id 1"}}
09:38:50.144 [37mStreamer Component ->[34m {"type":"ping","time":1754530730}
09:39:50.156 [37mStreamer Component ->[34m {"type":"ping","time":1754530790}
09:40:18.120 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
09:40:18.121 unsubscribing all players on Streamer Component
09:40:18.122 player 2 connection closed: 1005 - 
09:40:18.122 [37m[players] <-[32m {"type":"playerCount","count":0}
