11:13:07.454 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
11:13:07.499 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
11:13:07.501 Redirecting http->https
11:13:07.506 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
11:13:07.512 WebSocket listening for Streamer connections on :8888
11:13:07.513 WebSocket listening for SFU connections on :8889
11:13:07.514 WebSocket listening for Players connections on :80
11:13:07.515 Http listening on *: 80
11:13:07.515 Https listening on *: 443
11:17:30.481 Streamer connected: ::1
11:17:30.482 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
11:17:30.483 [37m::1 <-[32m {"type":"identify"}
11:17:30.943 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
11:17:30.944 Registered new streamer: Streamer Component
11:18:31.063 [37mStreamer Component ->[34m {"type":"ping","time":1754536711}
11:19:31.062 [37mStreamer Component ->[34m {"type":"ping","time":1754536771}
11:20:31.041 [37mStreamer Component ->[34m {"type":"ping","time":1754536831}
11:21:31.018 [37mStreamer Component ->[34m {"type":"ping","time":1754536891}
11:22:31.035 [37mStreamer Component ->[34m {"type":"ping","time":1754536951}
11:23:31.044 [37mStreamer Component ->[34m {"type":"ping","time":1754537011}
11:24:31.054 [37mStreamer Component ->[34m {"type":"ping","time":1754537071}
11:25:31.027 [37mStreamer Component ->[34m {"type":"ping","time":1754537131}
11:26:31.023 [37mStreamer Component ->[34m {"type":"ping","time":1754537191}
11:27:31.020 [37mStreamer Component ->[34m {"type":"ping","time":1754537251}
11:28:31.030 [37mStreamer Component ->[34m {"type":"ping","time":1754537311}
11:29:31.030 [37mStreamer Component ->[34m {"type":"ping","time":1754537371}
11:30:31.010 [37mStreamer Component ->[34m {"type":"ping","time":1754537431}
11:31:23.930 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
11:31:23.930 unsubscribing all players on Streamer Component
