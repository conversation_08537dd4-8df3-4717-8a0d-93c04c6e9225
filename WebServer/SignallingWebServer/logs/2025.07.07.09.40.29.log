09:40:29.009 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
09:40:29.048 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
09:40:29.050 Redirecting http->https
09:40:29.055 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
09:40:29.061 WebSocket listening for Streamer connections on :8888
09:40:29.062 WebSocket listening for SFU connections on :8889
09:40:29.063 WebSocket listening for Players connections on :80
09:40:29.064 Http listening on *: 80
09:40:29.064 Https listening on *: 443
09:40:51.608 Streamer connected: ::1
09:40:51.609 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:40:51.610 [37m::1 <-[32m {"type":"identify"}
09:40:52.112 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
09:40:52.113 Registered new streamer: Streamer Component
09:41:10.943 player 1 (::1) connected
09:41:10.944 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:41:10.945 [37m[players] <-[32m {"type":"playerCount","count":1}
09:41:10.946 [37m1 ->[34m {"type":"listStreamers"}
09:41:10.947 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
09:41:10.968 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
09:41:10.969 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
09:41:11.044 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 6005094939402155988 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Ah6B\r\na=ice-pwd:Y9bCVR0pRGvrK+HO+6/uDGZ5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F4:49:86:32:9A:44:1A:5E:D9:35:C9:76:23:85:1E:72:15:6E:7B:7D:BD:31:94:AF:94:7E:8E:F2:B0:83:9F:40\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1504139716 2794629239\r\na=ssrc:1504139716 cname:mBHDUy+cRAPACOJM\r\na=ssrc:1504139716 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2794629239 cname:mBHDUy+cRAPACOJM\r\na=ssrc:2794629239 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Ah6B\r\na=ice-pwd:Y9bCVR0pRGvrK+HO+6/uDGZ5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F4:49:86:32:9A:44:1A:5E:D9:35:C9:76:23:85:1E:72:15:6E:7B:7D:BD:31:94:AF:94:7E:8E:F2:B0:83:9F:40\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2769420701 cname:mBHDUy+cRAPACOJM\r\na=ssrc:2769420701 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Ah6B\r\na=ice-pwd:Y9bCVR0pRGvrK+HO+6/uDGZ5\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F4:49:86:32:9A:44:1A:5E:D9:35:C9:76:23:85:1E:72:15:6E:7B:7D:BD:31:94:AF:94:7E:8E:F2:B0:83:9F:40\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:41:11.072 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 7690434148287470507 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:q1Fq\r\na=ice-pwd:nY7aPbIgvKeWubqWyxpNEP7D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CD:5E:BD:22:28:46:CF:B4:5E:7C:CC:88:68:4A:93:4E:FE:CA:DC:B4:CB:18:68:87:E2:3A:89:FB:5A:07:B6:70\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:q1Fq\r\na=ice-pwd:nY7aPbIgvKeWubqWyxpNEP7D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CD:5E:BD:22:28:46:CF:B4:5E:7C:CC:88:68:4A:93:4E:FE:CA:DC:B4:CB:18:68:87:E2:3A:89:FB:5A:07:B6:70\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- d2b9d109-4df9-4d51-91bd-f9be02aa2364\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1897471750 cname:VDCP6SeK14Bkzsjt\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:q1Fq\r\na=ice-pwd:nY7aPbIgvKeWubqWyxpNEP7D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CD:5E:BD:22:28:46:CF:B4:5E:7C:CC:88:68:4A:93:4E:FE:CA:DC:B4:CB:18:68:87:E2:3A:89:FB:5A:07:B6:70\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:41:11.073 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1644017385 1 udp 2122260223 ************ 51285 typ host generation 0 ufrag q1Fq network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"q1Fq"}}
09:41:11.073 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:722134182 1 udp 2122194687 ************* 51286 typ host generation 0 ufrag q1Fq network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"q1Fq"}}
09:41:11.078 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1562848909 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag Ah6B network-id 1"}}
09:41:11.111 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:207568346 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag Ah6B network-id 4"}}
09:41:11.145 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1562848909 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag Ah6B network-id 1"}}
09:41:11.179 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:207568346 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag Ah6B network-id 4"}}
09:41:11.213 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1562848909 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag Ah6B network-id 1"}}
09:41:11.247 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:207568346 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag Ah6B network-id 4"}}
09:41:11.281 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:602459157 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag Ah6B network-id 1"}}
09:41:11.315 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1922096962 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag Ah6B network-id 4"}}
09:41:52.215 [37mStreamer Component ->[34m {"type":"ping","time":1754530912}
09:42:52.283 [37mStreamer Component ->[34m {"type":"ping","time":1754530972}
09:43:52.267 [37mStreamer Component ->[34m {"type":"ping","time":1754531032}
09:44:52.209 [37mStreamer Component ->[34m {"type":"ping","time":1754531092}
09:45:52.211 [37mStreamer Component ->[34m {"type":"ping","time":1754531152}
09:46:52.262 [37mStreamer Component ->[34m {"type":"ping","time":1754531212}
09:47:52.214 [37mStreamer Component ->[34m {"type":"ping","time":1754531272}
09:48:52.261 [37mStreamer Component ->[34m {"type":"ping","time":1754531332}
09:49:52.243 [37mStreamer Component ->[34m {"type":"ping","time":1754531392}
09:50:52.252 [37mStreamer Component ->[34m {"type":"ping","time":1754531452}
09:51:52.265 [37mStreamer Component ->[34m {"type":"ping","time":1754531512}
09:52:52.242 [37mStreamer Component ->[34m {"type":"ping","time":1754531572}
09:53:52.255 [37mStreamer Component ->[34m {"type":"ping","time":1754531632}
09:54:52.302 [37mStreamer Component ->[34m {"type":"ping","time":1754531692}
09:55:52.241 [37mStreamer Component ->[34m {"type":"ping","time":1754531752}
09:56:52.227 [37mStreamer Component ->[34m {"type":"ping","time":1754531812}
09:57:52.211 [37mStreamer Component ->[34m {"type":"ping","time":1754531872}
09:58:52.257 [37mStreamer Component ->[34m {"type":"ping","time":1754531932}
09:59:24.618 Upload output.mp3
09:59:52.271 [37mStreamer Component ->[34m {"type":"ping","time":1754531992}
10:00:52.249 [37mStreamer Component ->[34m {"type":"ping","time":1754532052}
10:01:52.271 [37mStreamer Component ->[34m {"type":"ping","time":1754532112}
10:02:52.280 [37mStreamer Component ->[34m {"type":"ping","time":1754532172}
10:03:52.259 [37mStreamer Component ->[34m {"type":"ping","time":1754532232}
10:04:52.276 [37mStreamer Component ->[34m {"type":"ping","time":1754532292}
10:05:52.239 [37mStreamer Component ->[34m {"type":"ping","time":1754532352}
10:06:52.279 [37mStreamer Component ->[34m {"type":"ping","time":1754532412}
10:07:52.206 [37mStreamer Component ->[34m {"type":"ping","time":1754532472}
10:08:52.246 [37mStreamer Component ->[34m {"type":"ping","time":1754532532}
10:09:52.288 [37mStreamer Component ->[34m {"type":"ping","time":1754532592}
10:10:52.248 [37mStreamer Component ->[34m {"type":"ping","time":1754532652}
10:11:52.213 [37mStreamer Component ->[34m {"type":"ping","time":1754532712}
10:12:52.242 [37mStreamer Component ->[34m {"type":"ping","time":1754532772}
10:13:52.249 [37mStreamer Component ->[34m {"type":"ping","time":1754532832}
10:14:52.228 [37mStreamer Component ->[34m {"type":"ping","time":1754532892}
10:15:52.235 [37mStreamer Component ->[34m {"type":"ping","time":1754532952}
10:16:52.241 [37mStreamer Component ->[34m {"type":"ping","time":1754533012}
10:17:52.246 [37mStreamer Component ->[34m {"type":"ping","time":1754533072}
10:18:52.229 [37mStreamer Component ->[34m {"type":"ping","time":1754533132}
10:18:58.546 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
10:18:58.547 unsubscribing all players on Streamer Component
10:18:58.548 player 1 connection closed: 1005 - 
10:18:58.548 [37m[players] <-[32m {"type":"playerCount","count":0}
