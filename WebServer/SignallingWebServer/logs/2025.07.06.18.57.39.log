18:57:39.379 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
18:57:39.419 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
18:57:39.420 Redirecting http->https
18:57:39.426 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
18:57:39.432 WebSocket listening for Streamer connections on :8888
18:57:39.433 WebSocket listening for SFU connections on :8889
18:57:39.434 WebSocket listening for Players connections on :80
18:57:39.435 Http listening on *: 80
18:57:39.435 Https listening on *: 443
18:58:08.728 Streamer connected: ::1
18:58:08.729 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:58:08.730 [37m::1 <-[32m {"type":"identify"}
18:58:09.228 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:58:09.229 Registered new streamer: Streamer Component
18:58:58.332 player 1 (::1) connected
18:58:58.332 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:58:58.333 [37m[players] <-[32m {"type":"playerCount","count":1}
18:58:58.334 [37m1 ->[34m {"type":"listStreamers"}
18:58:58.335 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
18:58:58.357 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
18:58:58.357 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
18:58:58.485 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 72853663330100578 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:9kmu\r\na=ice-pwd:tdMjagUMLXH2dRKSkypAT71R\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F3:94:3B:65:2C:58:7B:8D:EF:B1:1B:A1:93:20:DD:FE:8E:37:B9:85:A0:37:CF:62:FB:E6:BF:AF:9F:3B:01:13\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 4162548169 4154953978\r\na=ssrc:4162548169 cname:a8SH/Rgrc5d+XG5m\r\na=ssrc:4162548169 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:4154953978 cname:a8SH/Rgrc5d+XG5m\r\na=ssrc:4154953978 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:9kmu\r\na=ice-pwd:tdMjagUMLXH2dRKSkypAT71R\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F3:94:3B:65:2C:58:7B:8D:EF:B1:1B:A1:93:20:DD:FE:8E:37:B9:85:A0:37:CF:62:FB:E6:BF:AF:9F:3B:01:13\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:958413565 cname:a8SH/Rgrc5d+XG5m\r\na=ssrc:958413565 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:9kmu\r\na=ice-pwd:tdMjagUMLXH2dRKSkypAT71R\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F3:94:3B:65:2C:58:7B:8D:EF:B1:1B:A1:93:20:DD:FE:8E:37:B9:85:A0:37:CF:62:FB:E6:BF:AF:9F:3B:01:13\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
18:58:58.487 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:10894365 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag 9kmu network-id 1"}}
18:58:58.518 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:657182574 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag 9kmu network-id 2"}}
18:58:58.520 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 8830688600425592387 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:aegc\r\na=ice-pwd:ZWv2kmxICO6NaBNdcqe3qV4p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 10:CA:6D:77:CD:DC:0B:07:70:85:BC:53:C8:CD:26:4F:66:0D:7F:B7:EB:D5:4B:C0:E1:F2:DA:F6:C1:97:C5:DF\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:aegc\r\na=ice-pwd:ZWv2kmxICO6NaBNdcqe3qV4p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 10:CA:6D:77:CD:DC:0B:07:70:85:BC:53:C8:CD:26:4F:66:0D:7F:B7:EB:D5:4B:C0:E1:F2:DA:F6:C1:97:C5:DF\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 82ef5d32-711d-4a49-b381-8c1f65c7eb40\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:24904506 cname:MAuXO2Ks+jjRhIe5\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:aegc\r\na=ice-pwd:ZWv2kmxICO6NaBNdcqe3qV4p\r\na=ice-options:trickle\r\na=fingerprint:sha-256 10:CA:6D:77:CD:DC:0B:07:70:85:BC:53:C8:CD:26:4F:66:0D:7F:B7:EB:D5:4B:C0:E1:F2:DA:F6:C1:97:C5:DF\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
18:58:58.522 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:708857826 1 udp 2122260223 ************ 65219 typ host generation 0 ufrag aegc network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"aegc"}}
18:58:58.522 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:515550086 1 udp 2122194687 ************* 65220 typ host generation 0 ufrag aegc network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"aegc"}}
18:58:58.552 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:10894365 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag 9kmu network-id 1"}}
18:58:58.586 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:657182574 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag 9kmu network-id 2"}}
18:58:58.620 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:10894365 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag 9kmu network-id 1"}}
18:58:58.653 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:657182574 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag 9kmu network-id 2"}}
18:58:58.687 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4262258825 1 tcp 1518280447 ************ 49152 typ host tcptype passive generation 0 ufrag 9kmu network-id 1"}}
18:58:58.721 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3649117178 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag 9kmu network-id 2"}}
18:58:58.755 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4262258825 1 tcp 1518280447 ************ 49153 typ host tcptype passive generation 0 ufrag 9kmu network-id 1"}}
18:58:58.787 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3649117178 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag 9kmu network-id 2"}}
18:58:58.821 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4262258825 1 tcp 1518280447 ************ 49154 typ host tcptype passive generation 0 ufrag 9kmu network-id 1"}}
18:58:58.855 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3649117178 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag 9kmu network-id 2"}}
18:59:09.399 [37mStreamer Component ->[34m {"type":"ping","time":1754477949}
19:00:09.367 [37mStreamer Component ->[34m {"type":"ping","time":1754478009}
19:01:09.393 [37mStreamer Component ->[34m {"type":"ping","time":1754478069}
19:02:09.385 [37mStreamer Component ->[34m {"type":"ping","time":1754478129}
19:03:09.399 [37mStreamer Component ->[34m {"type":"ping","time":1754478189}
19:03:49.482 player 1 connection closed: 1001 - 
19:03:49.483 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
19:03:49.483 [37m[players] <-[32m {"type":"playerCount","count":0}
19:03:50.371 player 2 (::1) connected
19:03:50.372 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
19:03:50.372 [37m[players] <-[32m {"type":"playerCount","count":1}
19:03:50.373 [37m2 ->[34m {"type":"listStreamers"}
19:03:50.374 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
19:03:50.397 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
19:03:50.397 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
19:03:50.438 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 6409548212676448216 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mMeM\r\na=ice-pwd:uu5t9ew8pcHh6WpvZNKaqOJo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 38:2F:7D:D9:0A:C5:84:79:AE:76:B6:D5:E6:B2:A3:E4:33:B2:4B:18:51:BB:51:9F:46:28:C0:50:4E:D1:49:1A\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1800718047 1047685981\r\na=ssrc:1800718047 cname:zuI7OF2pQmbimA9Q\r\na=ssrc:1800718047 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1047685981 cname:zuI7OF2pQmbimA9Q\r\na=ssrc:1047685981 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mMeM\r\na=ice-pwd:uu5t9ew8pcHh6WpvZNKaqOJo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 38:2F:7D:D9:0A:C5:84:79:AE:76:B6:D5:E6:B2:A3:E4:33:B2:4B:18:51:BB:51:9F:46:28:C0:50:4E:D1:49:1A\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3016620138 cname:zuI7OF2pQmbimA9Q\r\na=ssrc:3016620138 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:mMeM\r\na=ice-pwd:uu5t9ew8pcHh6WpvZNKaqOJo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 38:2F:7D:D9:0A:C5:84:79:AE:76:B6:D5:E6:B2:A3:E4:33:B2:4B:18:51:BB:51:9F:46:28:C0:50:4E:D1:49:1A\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
19:03:50.471 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:76676637 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag mMeM network-id 1"}}
19:03:50.480 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2442837307559431397 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mGXj\r\na=ice-pwd:SIWokRY8CCvqFXqq+PlguFdj\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9E:E2:BF:7F:08:F6:2D:0D:C3:2A:89:FE:C6:0B:B5:49:9B:54:F6:FD:CB:3D:9D:C7:8B:4E:D4:3B:F9:6E:5F:EF\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:mGXj\r\na=ice-pwd:SIWokRY8CCvqFXqq+PlguFdj\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9E:E2:BF:7F:08:F6:2D:0D:C3:2A:89:FE:C6:0B:B5:49:9B:54:F6:FD:CB:3D:9D:C7:8B:4E:D4:3B:F9:6E:5F:EF\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 936bdf76-eb00-446f-9ea1-b6c9b90d738d\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:754656796 cname:rV+JRHCwAw8gIw5q\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:mGXj\r\na=ice-pwd:SIWokRY8CCvqFXqq+PlguFdj\r\na=ice-options:trickle\r\na=fingerprint:sha-256 9E:E2:BF:7F:08:F6:2D:0D:C3:2A:89:FE:C6:0B:B5:49:9B:54:F6:FD:CB:3D:9D:C7:8B:4E:D4:3B:F9:6E:5F:EF\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
19:03:50.480 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3011063036 1 udp 2122260223 ************ 63872 typ host generation 0 ufrag mGXj network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"mGXj"}}
19:03:50.481 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2273560728 1 udp 2122194687 ************* 63873 typ host generation 0 ufrag mGXj network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"mGXj"}}
19:03:50.504 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:589041006 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag mMeM network-id 2"}}
19:03:50.538 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:76676637 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag mMeM network-id 1"}}
19:03:50.572 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:589041006 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag mMeM network-id 2"}}
19:03:50.606 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:76676637 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag mMeM network-id 1"}}
19:03:50.639 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:589041006 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag mMeM network-id 2"}}
19:03:50.673 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4198181513 1 tcp 1518280447 ************ 49152 typ host tcptype passive generation 0 ufrag mMeM network-id 1"}}
19:03:50.707 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3719748090 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag mMeM network-id 2"}}
19:03:50.740 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4198181513 1 tcp 1518280447 ************ 49153 typ host tcptype passive generation 0 ufrag mMeM network-id 1"}}
19:03:50.774 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3719748090 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag mMeM network-id 2"}}
19:03:50.808 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4198181513 1 tcp 1518280447 ************ 49154 typ host tcptype passive generation 0 ufrag mMeM network-id 1"}}
19:03:50.842 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3719748090 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag mMeM network-id 2"}}
19:04:09.401 [37mStreamer Component ->[34m {"type":"ping","time":1754478249}
19:05:09.356 [37mStreamer Component ->[34m {"type":"ping","time":1754478309}
19:06:09.367 [37mStreamer Component ->[34m {"type":"ping","time":1754478369}
19:07:09.403 [37mStreamer Component ->[34m {"type":"ping","time":1754478429}
19:08:06.201 player 2 connection closed: 1001 - 
19:08:06.202 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"2"}
19:08:06.202 [37m[players] <-[32m {"type":"playerCount","count":0}
19:08:07.583 player 3 (::1) connected
19:08:07.584 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
19:08:07.584 [37m[players] <-[32m {"type":"playerCount","count":1}
19:08:07.585 [37m3 ->[34m {"type":"listStreamers"}
19:08:07.586 [37m3 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
19:08:07.612 [37m3 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
19:08:07.612 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"3","dataChannel":true,"sfu":false,"sendOffer":true}
19:08:07.653 [37mStreamer Component -> 3[36m {"type":"offer","playerId":3,"sdp":"v=0\r\no=- 6613984866872572439 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:gBq3\r\na=ice-pwd:dXazUkvGC7Nk8qABI1409n/h\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3A:F4:8D:94:D2:9B:5C:41:34:49:54:8D:82:9E:F3:05:DA:AF:06:DD:E5:DB:87:6A:1B:1A:19:9C:97:03:17:B2\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3698938782 1575293445\r\na=ssrc:3698938782 cname:wbyi0SzIuOm8PQGy\r\na=ssrc:3698938782 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1575293445 cname:wbyi0SzIuOm8PQGy\r\na=ssrc:1575293445 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:gBq3\r\na=ice-pwd:dXazUkvGC7Nk8qABI1409n/h\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3A:F4:8D:94:D2:9B:5C:41:34:49:54:8D:82:9E:F3:05:DA:AF:06:DD:E5:DB:87:6A:1B:1A:19:9C:97:03:17:B2\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2487140891 cname:wbyi0SzIuOm8PQGy\r\na=ssrc:2487140891 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:gBq3\r\na=ice-pwd:dXazUkvGC7Nk8qABI1409n/h\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3A:F4:8D:94:D2:9B:5C:41:34:49:54:8D:82:9E:F3:05:DA:AF:06:DD:E5:DB:87:6A:1B:1A:19:9C:97:03:17:B2\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
19:08:07.686 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3362005952 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag gBq3 network-id 1"}}
19:08:07.688 [37m3 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 4041337717684515317 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:gEUe\r\na=ice-pwd:AB+HnmSlj2Kq2wjn5HbJ3GLU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BA:35:84:9F:59:E2:78:A5:B2:08:F2:19:29:1E:26:3D:CA:49:2E:1A:87:00:B9:63:F7:4F:94:E5:08:E8:54:ED\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:gEUe\r\na=ice-pwd:AB+HnmSlj2Kq2wjn5HbJ3GLU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BA:35:84:9F:59:E2:78:A5:B2:08:F2:19:29:1E:26:3D:CA:49:2E:1A:87:00:B9:63:F7:4F:94:E5:08:E8:54:ED\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- aad27b9b-35be-41fa-8aa0-40d9a67fed39\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2332771010 cname:Lb5BibT/BpCGdcnw\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:gEUe\r\na=ice-pwd:AB+HnmSlj2Kq2wjn5HbJ3GLU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BA:35:84:9F:59:E2:78:A5:B2:08:F2:19:29:1E:26:3D:CA:49:2E:1A:87:00:B9:63:F7:4F:94:E5:08:E8:54:ED\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
19:08:07.689 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2261571678 1 udp 2122260223 ************ 62949 typ host generation 0 ufrag gEUe network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"gEUe"}}
19:08:07.690 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:317800503 1 udp 2122194687 ************* 62950 typ host generation 0 ufrag gEUe network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"gEUe"}}
19:08:07.720 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1205271217 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag gBq3 network-id 2"}}
19:08:07.754 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3362005952 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag gBq3 network-id 1"}}
19:08:07.787 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1205271217 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag gBq3 network-id 2"}}
19:08:07.821 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3362005952 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag gBq3 network-id 1"}}
19:08:07.854 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1205271217 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag gBq3 network-id 2"}}
19:08:07.888 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3064709464 1 tcp 1518280447 ************ 49152 typ host tcptype passive generation 0 ufrag gBq3 network-id 1"}}
19:08:07.921 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:957945897 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag gBq3 network-id 2"}}
19:08:07.955 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3064709464 1 tcp 1518280447 ************ 49153 typ host tcptype passive generation 0 ufrag gBq3 network-id 1"}}
19:08:07.988 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:957945897 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag gBq3 network-id 2"}}
19:08:08.022 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3064709464 1 tcp 1518280447 ************ 49154 typ host tcptype passive generation 0 ufrag gBq3 network-id 1"}}
19:08:08.055 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:957945897 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag gBq3 network-id 2"}}
19:08:09.405 [37mStreamer Component ->[34m {"type":"ping","time":1754478489}
19:09:09.397 [37mStreamer Component ->[34m {"type":"ping","time":1754478549}
19:09:43.240 player 3 connection closed: 1001 - 
19:09:43.242 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"3"}
19:09:43.243 [37m[players] <-[32m {"type":"playerCount","count":0}
