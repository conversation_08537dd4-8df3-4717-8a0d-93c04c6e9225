10:49:28.696 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
10:49:28.736 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
10:49:28.737 Redirecting http->https
10:49:28.742 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
10:49:28.749 WebSocket listening for Streamer connections on :8888
10:49:28.750 WebSocket listening for SFU connections on :8889
10:49:28.751 WebSocket listening for Players connections on :80
10:49:28.752 Http listening on *: 80
10:49:28.752 Https listening on *: 443
10:49:47.209 Streamer connected: ::1
10:49:47.210 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:49:47.211 [37m::1 <-[32m {"type":"identify"}
10:49:47.746 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
10:49:47.747 Registered new streamer: Streamer Component
10:50:47.825 [37mStreamer Component ->[34m {"type":"ping","time":1754535047}
10:51:47.844 [37mStreamer Component ->[34m {"type":"ping","time":1754535107}
10:52:47.801 [37mStreamer Component ->[34m {"type":"ping","time":1754535167}
10:53:11.138 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
10:53:11.139 unsubscribing all players on Streamer Component
