02:29:13.005 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "*************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
02:29:13.045 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:*************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
02:29:13.047 Redirecting http->https
02:29:13.052 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
02:29:13.059 WebSocket listening for Streamer connections on :8888
02:29:13.059 WebSocket listening for SFU connections on :8889
02:29:13.060 WebSocket listening for Players connections on :80
02:29:13.061 Http listening on *: 80
02:29:13.061 Https listening on *: 443
02:29:32.527 Streamer connected: ::1
02:29:32.528 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:*************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
02:29:32.528 [37m::1 <-[32m {"type":"identify"}
02:29:33.064 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
02:29:33.065 Registered new streamer: Streamer Component
02:30:33.169 [37mStreamer Component ->[34m {"type":"ping","time":1753641033}
02:31:33.212 [37mStreamer Component ->[34m {"type":"ping","time":1753641093}
02:32:33.195 [37mStreamer Component ->[34m {"type":"ping","time":1753641153}
02:33:33.221 [37mStreamer Component ->[34m {"type":"ping","time":1753641213}
02:34:33.201 [37mStreamer Component ->[34m {"type":"ping","time":1753641273}
02:35:33.209 [37mStreamer Component ->[34m {"type":"ping","time":1753641333}
02:36:33.184 [37mStreamer Component ->[34m {"type":"ping","time":1753641393}
02:37:33.211 [37mStreamer Component ->[34m {"type":"ping","time":1753641453}
02:38:33.204 [37mStreamer Component ->[34m {"type":"ping","time":1753641513}
02:39:33.219 [37mStreamer Component ->[34m {"type":"ping","time":1753641573}
02:40:33.185 [37mStreamer Component ->[34m {"type":"ping","time":1753641633}
02:41:33.205 [37mStreamer Component ->[34m {"type":"ping","time":1753641693}
02:42:33.191 [37mStreamer Component ->[34m {"type":"ping","time":1753641753}
02:43:33.202 [37mStreamer Component ->[34m {"type":"ping","time":1753641813}
02:44:33.209 [37mStreamer Component ->[34m {"type":"ping","time":1753641873}
02:45:26.991 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
02:45:26.992 unsubscribing all players on Streamer Component
