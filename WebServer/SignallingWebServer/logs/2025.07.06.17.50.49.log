17:50:49.584 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
17:50:49.632 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
17:50:49.633 Redirecting http->https
17:50:49.639 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
17:50:49.646 WebSocket listening for Streamer connections on :8888
17:50:49.647 WebSocket listening for SFU connections on :8889
17:50:49.648 WebSocket listening for Players connections on :80
17:50:49.649 Http listening on *: 80
17:50:49.649 Https listening on *: 443
17:55:06.640 Streamer connected: ::1
17:55:06.641 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
17:55:06.642 [37m::1 <-[32m {"type":"identify"}
17:55:07.880 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
17:55:07.881 Registered new streamer: Streamer Component
17:56:07.964 [37mStreamer Component ->[34m {"type":"ping","time":1754474167}
17:57:07.997 [37mStreamer Component ->[34m {"type":"ping","time":1754474227}
17:57:55.693 player 1 (::1) connected
17:57:55.695 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
17:57:55.695 [37m[players] <-[32m {"type":"playerCount","count":1}
17:57:55.695 [37m1 ->[34m {"type":"listStreamers"}
17:57:55.696 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
17:57:55.727 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
17:57:55.728 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
17:57:55.892 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 2725263639361648127 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:PZtn\r\na=ice-pwd:G0QOevl+lk3XxSzyXnkeWd6z\r\na=ice-options:trickle\r\na=fingerprint:sha-256 58:67:E4:6A:5D:91:C1:FB:E1:D3:9C:D5:A2:A7:FB:0F:97:64:2E:8E:3C:59:86:73:41:5E:37:F2:98:0E:E3:E4\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1487873325 1435696396\r\na=ssrc:1487873325 cname:hGCxtRNAuXdp79Dk\r\na=ssrc:1487873325 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1435696396 cname:hGCxtRNAuXdp79Dk\r\na=ssrc:1435696396 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:PZtn\r\na=ice-pwd:G0QOevl+lk3XxSzyXnkeWd6z\r\na=ice-options:trickle\r\na=fingerprint:sha-256 58:67:E4:6A:5D:91:C1:FB:E1:D3:9C:D5:A2:A7:FB:0F:97:64:2E:8E:3C:59:86:73:41:5E:37:F2:98:0E:E3:E4\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2158336569 cname:hGCxtRNAuXdp79Dk\r\na=ssrc:2158336569 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:PZtn\r\na=ice-pwd:G0QOevl+lk3XxSzyXnkeWd6z\r\na=ice-options:trickle\r\na=fingerprint:sha-256 58:67:E4:6A:5D:91:C1:FB:E1:D3:9C:D5:A2:A7:FB:0F:97:64:2E:8E:3C:59:86:73:41:5E:37:F2:98:0E:E3:E4\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:57:55.893 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2342627538 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag PZtn network-id 1"}}
17:57:55.920 [37m1 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 4236332497132095658 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uHYL\r\na=ice-pwd:APybNKlWvahIMYzmfiECLE65\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BE:DC:7C:5E:D5:86:29:18:90:6B:BE:D7:0B:42:AB:D9:AF:AE:35:CA:30:E0:31:82:33:5D:19:3A:D7:74:51:74\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uHYL\r\na=ice-pwd:APybNKlWvahIMYzmfiECLE65\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BE:DC:7C:5E:D5:86:29:18:90:6B:BE:D7:0B:42:AB:D9:AF:AE:35:CA:30:E0:31:82:33:5D:19:3A:D7:74:51:74\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- d5abc999-8cf7-47a1-8a0d-f042e1d63671\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:83582305 cname:p0oLsIPO00wp4x6S\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uHYL\r\na=ice-pwd:APybNKlWvahIMYzmfiECLE65\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BE:DC:7C:5E:D5:86:29:18:90:6B:BE:D7:0B:42:AB:D9:AF:AE:35:CA:30:E0:31:82:33:5D:19:3A:D7:74:51:74\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:57:55.921 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:358803422 1 udp 2122260223 ************ 50909 typ host generation 0 ufrag uHYL network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"uHYL"}}
17:57:55.921 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2170519479 1 udp 2122194687 ************* 50910 typ host generation 0 ufrag uHYL network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"uHYL"}}
17:57:55.925 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:68383139 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag PZtn network-id 2"}}
17:57:55.958 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2342627538 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag PZtn network-id 1"}}
17:57:55.992 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:68383139 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag PZtn network-id 2"}}
17:57:56.026 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2342627538 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag PZtn network-id 1"}}
17:57:56.059 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:68383139 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag PZtn network-id 2"}}
17:57:56.093 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4117645898 1 tcp 1518280447 ************ 49152 typ host tcptype passive generation 0 ufrag PZtn network-id 1"}}
17:57:56.126 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2061275963 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag PZtn network-id 2"}}
17:57:56.160 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4117645898 1 tcp 1518280447 ************ 49153 typ host tcptype passive generation 0 ufrag PZtn network-id 1"}}
17:57:56.194 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2061275963 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag PZtn network-id 2"}}
17:57:56.227 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4117645898 1 tcp 1518280447 ************ 49154 typ host tcptype passive generation 0 ufrag PZtn network-id 1"}}
17:57:56.261 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2061275963 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag PZtn network-id 2"}}
17:58:07.945 [37mStreamer Component ->[34m {"type":"ping","time":1754474287}
17:58:34.471 player 1 connection closed: 1001 - 
17:58:34.472 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
17:58:34.472 [37m[players] <-[32m {"type":"playerCount","count":0}
17:58:35.034 player 2 (::1) connected
17:58:35.035 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
17:58:35.035 [37m[players] <-[32m {"type":"playerCount","count":1}
17:58:35.036 [37m2 ->[34m {"type":"listStreamers"}
17:58:35.037 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
17:58:35.059 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
17:58:35.059 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
17:58:35.143 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 1655007787056201675 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uVCD\r\na=ice-pwd:ycz9Q/Si8XB1TvFTee15Tzhu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3C:32:27:F3:A4:57:25:EF:CD:5B:E9:6E:34:44:40:67:83:BE:77:8C:45:26:E2:6F:FC:4E:31:D9:63:96:B8:CD\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3261936571 4250004994\r\na=ssrc:3261936571 cname:/W4oY4h9GsIvzPMK\r\na=ssrc:3261936571 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:4250004994 cname:/W4oY4h9GsIvzPMK\r\na=ssrc:4250004994 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uVCD\r\na=ice-pwd:ycz9Q/Si8XB1TvFTee15Tzhu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3C:32:27:F3:A4:57:25:EF:CD:5B:E9:6E:34:44:40:67:83:BE:77:8C:45:26:E2:6F:FC:4E:31:D9:63:96:B8:CD\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3468409674 cname:/W4oY4h9GsIvzPMK\r\na=ssrc:3468409674 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uVCD\r\na=ice-pwd:ycz9Q/Si8XB1TvFTee15Tzhu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3C:32:27:F3:A4:57:25:EF:CD:5B:E9:6E:34:44:40:67:83:BE:77:8C:45:26:E2:6F:FC:4E:31:D9:63:96:B8:CD\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:58:35.173 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2670986760623294962 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:jaEN\r\na=ice-pwd:4+Mbcg/EIn0wwSAnE4Ot+nQ6\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:E6:1F:21:D5:2B:1E:17:15:39:82:12:C1:D4:90:BF:41:ED:44:9D:4F:06:A9:34:22:F8:FF:E8:A8:FF:08:E8\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:jaEN\r\na=ice-pwd:4+Mbcg/EIn0wwSAnE4Ot+nQ6\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:E6:1F:21:D5:2B:1E:17:15:39:82:12:C1:D4:90:BF:41:ED:44:9D:4F:06:A9:34:22:F8:FF:E8:A8:FF:08:E8\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 145b7e7d-643e-4cfe-a037-d026e9d6db92\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2094175136 cname:VmkPM5tr89Ig12lM\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:jaEN\r\na=ice-pwd:4+Mbcg/EIn0wwSAnE4Ot+nQ6\r\na=ice-options:trickle\r\na=fingerprint:sha-256 39:E6:1F:21:D5:2B:1E:17:15:39:82:12:C1:D4:90:BF:41:ED:44:9D:4F:06:A9:34:22:F8:FF:E8:A8:FF:08:E8\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:58:35.174 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:292678147 1 udp 2122260223 ************ 60803 typ host generation 0 ufrag jaEN network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"jaEN"}}
17:58:35.174 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2236379754 1 udp 2122194687 ************* 60804 typ host generation 0 ufrag jaEN network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"jaEN"}}
17:58:35.176 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3378936034 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag uVCD network-id 1"}}
17:58:35.210 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4008414097 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag uVCD network-id 2"}}
17:58:35.243 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3378936034 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag uVCD network-id 1"}}
17:58:35.277 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4008414097 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag uVCD network-id 2"}}
17:58:35.311 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3378936034 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag uVCD network-id 1"}}
17:58:35.344 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4008414097 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag uVCD network-id 2"}}
17:58:35.378 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:936160374 1 tcp 1518280447 ************ 49152 typ host tcptype passive generation 0 ufrag uVCD network-id 1"}}
17:58:35.412 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:272719621 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag uVCD network-id 2"}}
17:58:35.445 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:936160374 1 tcp 1518280447 ************ 49153 typ host tcptype passive generation 0 ufrag uVCD network-id 1"}}
17:58:35.479 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:272719621 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag uVCD network-id 2"}}
17:58:35.513 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:936160374 1 tcp 1518280447 ************ 49154 typ host tcptype passive generation 0 ufrag uVCD network-id 1"}}
17:58:35.546 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:272719621 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag uVCD network-id 2"}}
17:59:07.992 [37mStreamer Component ->[34m {"type":"ping","time":1754474347}
18:00:07.995 [37mStreamer Component ->[34m {"type":"ping","time":1754474407}
18:01:07.980 [37mStreamer Component ->[34m {"type":"ping","time":1754474467}
18:02:07.969 [37mStreamer Component ->[34m {"type":"ping","time":1754474527}
18:03:07.972 [37mStreamer Component ->[34m {"type":"ping","time":1754474587}
18:04:07.964 [37mStreamer Component ->[34m {"type":"ping","time":1754474647}
18:05:07.963 [37mStreamer Component ->[34m {"type":"ping","time":1754474707}
