17:01:13.447 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
17:01:13.490 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
17:01:13.491 Redirecting http->https
17:01:13.496 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
17:01:13.503 WebSocket listening for Streamer connections on :8888
17:01:13.504 WebSocket listening for SFU connections on :8889
17:01:13.505 WebSocket listening for Players connections on :80
17:01:13.505 设置服务器端视频播放HTTP API
17:01:13.506 Http listening on *: 80
17:01:13.507 Https listening on *: 443
