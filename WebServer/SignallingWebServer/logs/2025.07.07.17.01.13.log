17:01:13.447 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
17:01:13.490 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
17:01:13.491 Redirecting http->https
17:01:13.496 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
17:01:13.503 WebSocket listening for Streamer connections on :8888
17:01:13.504 WebSocket listening for SFU connections on :8889
17:01:13.505 WebSocket listening for Players connections on :80
17:01:13.505 设置服务器端视频播放HTTP API
17:01:13.506 Http listening on *: 80
17:01:13.507 Https listening on *: 443
17:01:36.716 请求视频播放器配置文件: H:\cloud6\MomentumCloud\Renderer\VirtualHuman\Binaries\Win64\video-player-config.json
17:01:37.009 Streamer connected: ::1
17:01:37.010 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
17:01:37.010 [37m::1 <-[32m {"type":"identify"}
17:01:37.592 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
17:01:37.593 Registered new streamer: Streamer Component
17:01:46.616 player 1 (::ffff:*************) connected
17:01:46.617 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
17:01:46.618 [37m[players] <-[32m {"type":"playerCount","count":1}
17:01:46.618 [37m1 ->[34m {"type":"listStreamers"}
17:01:46.619 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
17:01:46.640 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
17:01:46.640 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
17:01:46.728 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 122543773338198322 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:9wqs\r\na=ice-pwd:k1IdbRAKRS1GAs2RibewODYk\r\na=ice-options:trickle\r\na=fingerprint:sha-256 03:33:E6:26:C4:29:36:0C:4A:8D:CA:21:06:9F:80:8E:C0:43:9D:D0:3E:8A:C9:21:B2:B1:DB:91:E5:CE:5D:A1\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 804710839 102102593\r\na=ssrc:804710839 cname:KOeZg2bgoy1rmdeO\r\na=ssrc:804710839 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:102102593 cname:KOeZg2bgoy1rmdeO\r\na=ssrc:102102593 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:9wqs\r\na=ice-pwd:k1IdbRAKRS1GAs2RibewODYk\r\na=ice-options:trickle\r\na=fingerprint:sha-256 03:33:E6:26:C4:29:36:0C:4A:8D:CA:21:06:9F:80:8E:C0:43:9D:D0:3E:8A:C9:21:B2:B1:DB:91:E5:CE:5D:A1\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3552608961 cname:KOeZg2bgoy1rmdeO\r\na=ssrc:3552608961 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:9wqs\r\na=ice-pwd:k1IdbRAKRS1GAs2RibewODYk\r\na=ice-options:trickle\r\na=fingerprint:sha-256 03:33:E6:26:C4:29:36:0C:4A:8D:CA:21:06:9F:80:8E:C0:43:9D:D0:3E:8A:C9:21:B2:B1:DB:91:E5:CE:5D:A1\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:01:46.751 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 8966873283437328903 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:M6l9\r\na=ice-pwd:GriNFszLjyM7AZNcypo8G2w2\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CF:06:59:0F:D8:FF:9A:90:0B:C2:A2:BB:1F:15:01:66:65:AC:A9:B3:3A:82:8A:26:42:C0:14:4D:B9:BB:34:92\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:M6l9\r\na=ice-pwd:GriNFszLjyM7AZNcypo8G2w2\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CF:06:59:0F:D8:FF:9A:90:0B:C2:A2:BB:1F:15:01:66:65:AC:A9:B3:3A:82:8A:26:42:C0:14:4D:B9:BB:34:92\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 16742efd-449f-49f4-99c9-22f4f22b60d1\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:817493130 cname:bcxnJ9sNJ1vcnjad\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:M6l9\r\na=ice-pwd:GriNFszLjyM7AZNcypo8G2w2\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CF:06:59:0F:D8:FF:9A:90:0B:C2:A2:BB:1F:15:01:66:65:AC:A9:B3:3A:82:8A:26:42:C0:14:4D:B9:BB:34:92\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:01:46.752 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1035990393 1 udp 2122260223 ************* 59415 typ host generation 0 ufrag M6l9 network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"M6l9"}}
17:01:46.753 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:178709245 1 udp 2122194687 ************ 59416 typ host generation 0 ufrag M6l9 network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"M6l9"}}
17:01:46.753 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3159621185 1 udp 2122129151 *************** 59417 typ host generation 0 ufrag M6l9 network-id 3","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"M6l9"}}
17:01:46.761 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3435796560 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag 9wqs network-id 1"}}
17:01:46.761 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:472738743 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag 9wqs network-id 4"}}
17:01:46.795 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3435796560 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag 9wqs network-id 1"}}
17:01:46.828 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:472738743 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag 9wqs network-id 4"}}
17:01:46.862 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3435796560 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag 9wqs network-id 1"}}
17:01:46.896 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:472738743 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag 9wqs network-id 4"}}
17:01:56.479 player 1 connection closed: 1001 - 
17:01:56.480 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"1"}
17:01:56.480 [37m[players] <-[32m {"type":"playerCount","count":0}
17:02:03.316 请求视频播放器配置文件: H:\cloud6\MomentumCloud\Renderer\VirtualHuman\Binaries\Win64\video-player-config.json
17:02:19.370 请求视频播放器配置文件: H:\cloud6\MomentumCloud\Renderer\VirtualHuman\Binaries\Win64\video-player-config.json
17:02:20.068 player 2 (::1) connected
17:02:20.069 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
17:02:20.069 [37m[players] <-[32m {"type":"playerCount","count":1}
17:02:20.070 [37m2 ->[34m {"type":"listStreamers"}
17:02:20.070 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
17:02:20.248 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
17:02:20.249 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
17:02:20.323 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 1229133743992422822 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:aTL+\r\na=ice-pwd:3FvM0RyMUZr+vLOpv20Tq9am\r\na=ice-options:trickle\r\na=fingerprint:sha-256 88:C9:76:13:BA:30:93:26:40:07:9D:6F:CB:4C:83:B3:0F:7D:AE:28:F5:FA:C1:28:49:FF:65:8F:CC:D3:77:CF\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1206663395 6776385\r\na=ssrc:1206663395 cname:7c2wNEl3L0K2cPrC\r\na=ssrc:1206663395 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:6776385 cname:7c2wNEl3L0K2cPrC\r\na=ssrc:6776385 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:aTL+\r\na=ice-pwd:3FvM0RyMUZr+vLOpv20Tq9am\r\na=ice-options:trickle\r\na=fingerprint:sha-256 88:C9:76:13:BA:30:93:26:40:07:9D:6F:CB:4C:83:B3:0F:7D:AE:28:F5:FA:C1:28:49:FF:65:8F:CC:D3:77:CF\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3150625777 cname:7c2wNEl3L0K2cPrC\r\na=ssrc:3150625777 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:aTL+\r\na=ice-pwd:3FvM0RyMUZr+vLOpv20Tq9am\r\na=ice-options:trickle\r\na=fingerprint:sha-256 88:C9:76:13:BA:30:93:26:40:07:9D:6F:CB:4C:83:B3:0F:7D:AE:28:F5:FA:C1:28:49:FF:65:8F:CC:D3:77:CF\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:02:20.355 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1621448681 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag aTL+ network-id 1"}}
17:02:20.391 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:219570296 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag aTL+ network-id 4"}}
17:02:20.428 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1621448681 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag aTL+ network-id 1"}}
17:02:20.431 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5350687929828170843 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Vv1l\r\na=ice-pwd:zz37sC5C7zSiuoIVMyR2GJIX\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EA:22:49:F3:E1:F7:AB:79:66:BC:12:66:F3:C5:40:84:69:F5:B2:D0:B5:3C:63:3E:25:2E:65:2C:23:07:0E:48\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Vv1l\r\na=ice-pwd:zz37sC5C7zSiuoIVMyR2GJIX\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EA:22:49:F3:E1:F7:AB:79:66:BC:12:66:F3:C5:40:84:69:F5:B2:D0:B5:3C:63:3E:25:2E:65:2C:23:07:0E:48\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 5d9f2429-f1dc-4f80-be95-6798c655218d\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2249414304 cname:Bq3h+NKj/wnvlkY5\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Vv1l\r\na=ice-pwd:zz37sC5C7zSiuoIVMyR2GJIX\r\na=ice-options:trickle\r\na=fingerprint:sha-256 EA:22:49:F3:E1:F7:AB:79:66:BC:12:66:F3:C5:40:84:69:F5:B2:D0:B5:3C:63:3E:25:2E:65:2C:23:07:0E:48\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
17:02:20.436 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:743143842 1 udp 2122260223 ************ 57058 typ host generation 0 ufrag Vv1l network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"Vv1l"}}
17:02:20.436 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:4024114514 1 udp 2122194687 ************* 57059 typ host generation 0 ufrag Vv1l network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"Vv1l"}}
17:02:20.457 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:219570296 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag aTL+ network-id 4"}}
17:02:20.494 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1621448681 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag aTL+ network-id 1"}}
17:02:20.525 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:219570296 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag aTL+ network-id 4"}}
17:02:20.559 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:507212516 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag aTL+ network-id 1"}}
17:02:20.592 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1938310517 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag aTL+ network-id 4"}}
17:02:20.625 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:507212516 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag aTL+ network-id 1"}}
17:02:20.658 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1938310517 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag aTL+ network-id 4"}}
17:02:20.692 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:507212516 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag aTL+ network-id 1"}}
17:02:20.726 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1938310517 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag aTL+ network-id 4"}}
17:02:20.760 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3148249000 1 udp 1686052607 ************** 49153 typ srflx raddr ************* rport 49153 generation 0 ufrag aTL+ network-id 1"}}
17:02:20.793 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3148249000 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag aTL+ network-id 1"}}
17:02:20.827 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3148249000 1 udp 1686052607 ************** 49154 typ srflx raddr ************* rport 49154 generation 0 ufrag aTL+ network-id 1"}}
17:02:33.477 player 2 connection closed: 1001 - 
17:02:33.483 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"2"}
17:02:33.483 [37m[players] <-[32m {"type":"playerCount","count":0}
17:02:33.693 请求视频播放器配置文件: H:\cloud6\MomentumCloud\Renderer\VirtualHuman\Binaries\Win64\video-player-config.json
17:02:37.687 [37mStreamer Component ->[34m {"type":"ping","time":1754557357}
17:02:39.229 请求视频播放器配置文件: H:\cloud6\MomentumCloud\Renderer\VirtualHuman\Binaries\Win64\video-player-config.json
17:03:37.682 [37mStreamer Component ->[34m {"type":"ping","time":1754557417}
17:04:37.679 [37mStreamer Component ->[34m {"type":"ping","time":1754557477}
17:05:37.725 [37mStreamer Component ->[34m {"type":"ping","time":1754557537}
17:06:37.560 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
17:06:37.561 unsubscribing all players on Streamer Component
