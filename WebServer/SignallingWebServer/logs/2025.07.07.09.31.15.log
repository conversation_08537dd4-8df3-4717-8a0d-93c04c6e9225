09:31:15.189 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
09:31:15.526 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
09:31:15.527 Redirecting http->https
09:31:15.533 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
09:31:15.626 WebSocket listening for Streamer connections on :8888
09:31:15.627 WebSocket listening for SFU connections on :8889
09:31:15.628 WebSocket listening for Players connections on :80
09:31:15.629 Http listening on *: 80
09:31:15.629 Https listening on *: 443
09:31:42.501 Streamer connected: ::1
09:31:42.502 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:31:42.503 [37m::1 <-[32m {"type":"identify"}
09:31:44.396 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
09:31:44.397 Registered new streamer: Streamer Component
09:32:25.083 player 1 (::1) connected
09:32:25.084 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
09:32:25.084 [37m[players] <-[32m {"type":"playerCount","count":1}
09:32:25.085 [37m1 ->[34m {"type":"listStreamers"}
09:32:25.085 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
09:32:25.202 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
09:32:25.202 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
09:32:25.321 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 1450775391299074455 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:k80r\r\na=ice-pwd:GfMAj7Wznb7aHgwYePOHZRny\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C0:77:B0:09:00:97:A5:A9:2B:97:11:F6:41:11:98:12:39:A3:9B:E8:CB:4E:58:24:F7:62:FD:E0:6B:8C:4B:0F\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3427121694 1745316655\r\na=ssrc:3427121694 cname:KD0sUcCA0TxknJHV\r\na=ssrc:3427121694 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1745316655 cname:KD0sUcCA0TxknJHV\r\na=ssrc:1745316655 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:k80r\r\na=ice-pwd:GfMAj7Wznb7aHgwYePOHZRny\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C0:77:B0:09:00:97:A5:A9:2B:97:11:F6:41:11:98:12:39:A3:9B:E8:CB:4E:58:24:F7:62:FD:E0:6B:8C:4B:0F\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2541950793 cname:KD0sUcCA0TxknJHV\r\na=ssrc:2541950793 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:k80r\r\na=ice-pwd:GfMAj7Wznb7aHgwYePOHZRny\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C0:77:B0:09:00:97:A5:A9:2B:97:11:F6:41:11:98:12:39:A3:9B:E8:CB:4E:58:24:F7:62:FD:E0:6B:8C:4B:0F\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:32:25.355 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3620997781 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag k80r network-id 1"}}
09:32:25.357 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 2183211015486656002 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:1sIJ\r\na=ice-pwd:XYVACHGBFkfn17wg4Wbik24c\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3E:C6:DF:6F:55:2C:42:C3:5E:78:6A:E0:62:CC:1F:17:BE:F8:4D:CD:24:BF:C2:4D:F0:46:5E:98:8F:5C:A0:3C\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:1sIJ\r\na=ice-pwd:XYVACHGBFkfn17wg4Wbik24c\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3E:C6:DF:6F:55:2C:42:C3:5E:78:6A:E0:62:CC:1F:17:BE:F8:4D:CD:24:BF:C2:4D:F0:46:5E:98:8F:5C:A0:3C\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- dd7c40db-7ba2-4433-a0c9-49dc7e8703be\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1202193185 cname:Qj3kLCDJU3k5JVyO\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:1sIJ\r\na=ice-pwd:XYVACHGBFkfn17wg4Wbik24c\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3E:C6:DF:6F:55:2C:42:C3:5E:78:6A:E0:62:CC:1F:17:BE:F8:4D:CD:24:BF:C2:4D:F0:46:5E:98:8F:5C:A0:3C\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
09:32:25.362 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2252634983 1 udp 2122260223 ************ 62247 typ host generation 0 ufrag 1sIJ network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"1sIJ"}}
09:32:25.362 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1617809460 1 udp 2122194687 ************* 62248 typ host generation 0 ufrag 1sIJ network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"1sIJ"}}
09:32:25.389 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2259425730 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag k80r network-id 4"}}
09:32:25.423 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3620997781 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag k80r network-id 1"}}
09:32:25.457 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2259425730 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag k80r network-id 4"}}
09:32:25.491 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3620997781 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag k80r network-id 1"}}
09:32:25.525 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2259425730 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag k80r network-id 4"}}
09:32:25.559 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2837178381 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag k80r network-id 1"}}
09:32:25.593 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4167301978 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag k80r network-id 4"}}
09:32:25.627 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2837178381 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag k80r network-id 1"}}
09:32:25.661 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4167301978 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag k80r network-id 4"}}
09:32:25.695 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2837178381 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag k80r network-id 1"}}
09:32:25.728 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4167301978 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag k80r network-id 4"}}
09:32:25.762 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3604248755 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag k80r network-id 1"}}
09:32:44.587 [37mStreamer Component ->[34m {"type":"ping","time":1754530364}
09:33:44.579 [37mStreamer Component ->[34m {"type":"ping","time":1754530424}
09:33:45.838 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
09:33:45.838 unsubscribing all players on Streamer Component
09:33:45.840 player 1 connection closed: 1005 - 
09:33:45.844 [37m[players] <-[32m {"type":"playerCount","count":0}
