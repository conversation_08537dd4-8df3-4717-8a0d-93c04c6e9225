11:31:48.875 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
11:31:48.915 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
11:31:48.916 Redirecting http->https
11:31:48.921 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
11:31:48.928 WebSocket listening for Streamer connections on :8888
11:31:48.929 WebSocket listening for SFU connections on :8889
11:31:48.929 WebSocket listening for Players connections on :80
11:31:48.930 Http listening on *: 80
11:31:48.931 Https listening on *: 443
11:32:11.825 Streamer connected: ::1
11:32:11.826 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
11:32:11.827 [37m::1 <-[32m {"type":"identify"}
11:32:12.322 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
11:32:12.323 Registered new streamer: Streamer Component
11:33:20.865 [37mStreamer Component ->[34m {"type":"ping","time":1754537600}
11:34:47.832 [37mStreamer Component ->[34m {"type":"ping","time":1754537687}
11:36:08.296 [37mStreamer Component ->[34m {"type":"ping","time":1754537768}
11:37:08.299 [37mStreamer Component ->[34m {"type":"ping","time":1754537828}
11:37:22.097 player 1 (::1) connected
11:37:22.099 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
11:37:22.100 [37m[players] <-[32m {"type":"playerCount","count":1}
11:37:22.102 [37m1 ->[34m {"type":"listStreamers"}
11:37:22.102 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
11:37:22.247 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
11:37:22.248 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
11:37:22.344 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 4231897634821312767 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:gzla\r\na=ice-pwd:RPZGWrXTR6+gWwDez9QEGcxS\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BA:01:62:E0:F2:14:88:37:CF:84:29:2E:A7:0F:8D:B8:E9:1A:A7:53:AE:EE:90:6C:0C:A6:38:4C:AD:22:FF:A2\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1197429622 1880324362\r\na=ssrc:1197429622 cname:UG+/ptU3SU+fHZQL\r\na=ssrc:1197429622 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1880324362 cname:UG+/ptU3SU+fHZQL\r\na=ssrc:1880324362 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:gzla\r\na=ice-pwd:RPZGWrXTR6+gWwDez9QEGcxS\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BA:01:62:E0:F2:14:88:37:CF:84:29:2E:A7:0F:8D:B8:E9:1A:A7:53:AE:EE:90:6C:0C:A6:38:4C:AD:22:FF:A2\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1311475928 cname:UG+/ptU3SU+fHZQL\r\na=ssrc:1311475928 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:gzla\r\na=ice-pwd:RPZGWrXTR6+gWwDez9QEGcxS\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BA:01:62:E0:F2:14:88:37:CF:84:29:2E:A7:0F:8D:B8:E9:1A:A7:53:AE:EE:90:6C:0C:A6:38:4C:AD:22:FF:A2\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
11:37:22.346 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4105213167 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag gzla network-id 1"}}
11:37:22.377 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2781382584 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag gzla network-id 4"}}
11:37:22.382 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 4474500174164281584 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uHkR\r\na=ice-pwd:evPJtTT+yFLY7OKX3qQpaLQh\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F7:8E:EE:9F:56:D2:26:78:46:8A:40:D2:39:F4:6B:AD:6E:B6:59:EF:36:08:14:BA:1D:11:6C:47:94:8D:FC:21\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:uHkR\r\na=ice-pwd:evPJtTT+yFLY7OKX3qQpaLQh\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F7:8E:EE:9F:56:D2:26:78:46:8A:40:D2:39:F4:6B:AD:6E:B6:59:EF:36:08:14:BA:1D:11:6C:47:94:8D:FC:21\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- b4f30043-f7ea-4ba6-ab9b-992109592f26\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3042982876 cname:CsRUos3A55EOkBIz\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:uHkR\r\na=ice-pwd:evPJtTT+yFLY7OKX3qQpaLQh\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F7:8E:EE:9F:56:D2:26:78:46:8A:40:D2:39:F4:6B:AD:6E:B6:59:EF:36:08:14:BA:1D:11:6C:47:94:8D:FC:21\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
11:37:22.385 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:480448116 1 udp 2122260223 ************ 55460 typ host generation 0 ufrag uHkR network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"uHkR"}}
11:37:22.386 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1448369211 1 udp 2122194687 ************* 55461 typ host generation 0 ufrag uHkR network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"uHkR"}}
11:37:22.410 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4105213167 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag gzla network-id 1"}}
11:37:22.444 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2781382584 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag gzla network-id 4"}}
11:37:22.478 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4105213167 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag gzla network-id 1"}}
11:37:22.511 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2781382584 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag gzla network-id 4"}}
11:37:22.545 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2323609207 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag gzla network-id 1"}}
11:37:22.579 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3674694944 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag gzla network-id 4"}}
11:38:08.290 [37mStreamer Component ->[34m {"type":"ping","time":1754537888}
11:39:09.430 [37mStreamer Component ->[34m {"type":"ping","time":1754537949}
11:40:09.394 [37mStreamer Component ->[34m {"type":"ping","time":1754538009}
11:41:09.433 [37mStreamer Component ->[34m {"type":"ping","time":1754538069}
11:42:09.366 [37mStreamer Component ->[34m {"type":"ping","time":1754538129}
11:43:09.370 [37mStreamer Component ->[34m {"type":"ping","time":1754538189}
11:44:09.403 [37mStreamer Component ->[34m {"type":"ping","time":1754538249}
11:45:09.378 [37mStreamer Component ->[34m {"type":"ping","time":1754538309}
11:46:09.392 [37mStreamer Component ->[34m {"type":"ping","time":1754538369}
11:47:09.392 [37mStreamer Component ->[34m {"type":"ping","time":1754538429}
11:48:09.381 [37mStreamer Component ->[34m {"type":"ping","time":1754538489}
11:49:09.389 [37mStreamer Component ->[34m {"type":"ping","time":1754538549}
11:50:09.395 [37mStreamer Component ->[34m {"type":"ping","time":1754538609}
11:51:09.372 [37mStreamer Component ->[34m {"type":"ping","time":1754538669}
11:52:09.388 [37mStreamer Component ->[34m {"type":"ping","time":1754538729}
11:53:09.387 [37mStreamer Component ->[34m {"type":"ping","time":1754538789}
11:54:09.410 [37mStreamer Component ->[34m {"type":"ping","time":1754538849}
11:55:09.404 [37mStreamer Component ->[34m {"type":"ping","time":1754538909}
11:56:09.392 [37mStreamer Component ->[34m {"type":"ping","time":1754538969}
11:57:13.529 streamer Streamer Component disconnected: 1006 - 
11:57:13.530 unsubscribing all players on Streamer Component
11:57:13.531 player 1 connection closed: 1005 - 
11:57:13.531 [37m[players] <-[32m {"type":"playerCount","count":0}
13:26:27.618 Streamer connected: ::1
13:26:27.619 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:26:27.620 [37m::1 <-[32m {"type":"identify"}
13:26:28.350 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
13:26:28.351 Registered new streamer: Streamer Component
13:27:43.443 [37mStreamer Component ->[34m {"type":"ping","time":1754544463}
13:27:52.417 player 2 (::1) connected
13:27:52.418 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:27:52.418 [37m[players] <-[32m {"type":"playerCount","count":1}
13:27:52.420 [37m2 ->[34m {"type":"listStreamers"}
13:27:52.420 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
13:27:52.461 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
13:27:52.462 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
13:27:52.516 player 2 connection closed: 1001 - 
13:27:52.517 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"2"}
13:27:52.518 [37m[players] <-[32m {"type":"playerCount","count":0}
13:27:52.539 No playerId specified, cannot forward message: {
  type: 'offer',
  playerId: 2,
  sdp: 'v=0\r\n' +
    'o=- 6995357682355997942 2 IN IP4 127.0.0.1\r\n' +
    's=-\r\n' +
    't=0 0\r\n' +
    'a=group:BUNDLE 0 1 2\r\n' +
    'a=extmap-allow-mixed\r\n' +
    'a=msid-semantic: WMS pixelstreaming_av_stream_id\r\n' +
    'm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\n' +
    'c=IN IP4 0.0.0.0\r\n' +
    'a=rtcp:9 IN IP4 0.0.0.0\r\n' +
    'a=ice-ufrag:bpIH\r\n' +
    'a=ice-pwd:wdchxL2n+O28QzefgFamjVfP\r\n' +
    'a=ice-options:trickle\r\n' +
    'a=fingerprint:sha-256 C2:E0:8F:0A:4F:A8:7C:8A:CC:31:35:26:A4:6D:86:D8:25:71:E5:17:B6:6E:38:1E:FA:56:FF:87:56:21:3D:59\r\n' +
    'a=setup:actpass\r\n' +
    'a=mid:0\r\n' +
    'a=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\n' +
    'a=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\n' +
    'a=extmap:3 urn:3gpp:video-orientation\r\n' +
    'a=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\n' +
    'a=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\n' +
    'a=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\n' +
    'a=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\n' +
    'a=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\n' +
    'a=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\n' +
    'a=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\n' +
    'a=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\n' +
    'a=sendonly\r\n' +
    'a=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\n' +
    'a=rtcp-mux\r\n' +
    'a=rtcp-rsize\r\n' +
    'a=rtpmap:35 AV1/90000\r\n' +
    'a=rtcp-fb:35 goog-remb\r\n' +
    'a=rtcp-fb:35 transport-cc\r\n' +
    'a=rtcp-fb:35 ccm fir\r\n' +
    'a=rtcp-fb:35 nack\r\n' +
    'a=rtcp-fb:35 nack pli\r\n' +
    'a=rtpmap:36 rtx/90000\r\n' +
    'a=fmtp:36 apt=35\r\n' +
    'a=rtpmap:96 red/90000\r\n' +
    'a=rtpmap:97 rtx/90000\r\n' +
    'a=fmtp:97 apt=96\r\n' +
    'a=rtpmap:98 ulpfec/90000\r\n' +
    'a=ssrc-group:FID 3186772991 3748779831\r\n' +
    'a=ssrc:3186772991 cname:KVyQSwbWBzWvsojA\r\n' +
    'a=ssrc:3186772991 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\n' +
    'a=ssrc:3748779831 cname:KVyQSwbWBzWvsojA\r\n' +
    'a=ssrc:3748779831 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\n' +
    'm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\n' +
    'c=IN IP4 0.0.0.0\r\n' +
    'a=rtcp:9 IN IP4 0.0.0.0\r\n' +
    'a=ice-ufrag:bpIH\r\n' +
    'a=ice-pwd:wdchxL2n+O28QzefgFamjVfP\r\n' +
    'a=ice-options:trickle\r\n' +
    'a=fingerprint:sha-256 C2:E0:8F:0A:4F:A8:7C:8A:CC:31:35:26:A4:6D:86:D8:25:71:E5:17:B6:6E:38:1E:FA:56:FF:87:56:21:3D:59\r\n' +
    'a=setup:actpass\r\n' +
    'a=mid:1\r\n' +
    'a=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\n' +
    'a=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\n' +
    'a=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\n' +
    'a=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\n' +
    'a=sendrecv\r\n' +
    'a=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\n' +
    'a=rtcp-mux\r\n' +
    'a=rtpmap:111 opus/48000/2\r\n' +
    'a=rtcp-fb:111 transport-cc\r\n' +
    'a=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\n' +
    'a=rtpmap:63 red/48000/2\r\n' +
    'a=fmtp:63 111/111\r\n' +
    'a=rtpmap:110 telephone-event/48000\r\n' +
    'a=maxptime:120\r\n' +
    'a=ptime:20\r\n' +
    'a=ssrc:1449201511 cname:KVyQSwbWBzWvsojA\r\n' +
    'a=ssrc:1449201511 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\n' +
    'm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\n' +
    'c=IN IP4 0.0.0.0\r\n' +
    'a=ice-ufrag:bpIH\r\n' +
    'a=ice-pwd:wdchxL2n+O28QzefgFamjVfP\r\n' +
    'a=ice-options:trickle\r\n' +
    'a=fingerprint:sha-256 C2:E0:8F:0A:4F:A8:7C:8A:CC:31:35:26:A4:6D:86:D8:25:71:E5:17:B6:6E:38:1E:FA:56:FF:87:56:21:3D:59\r\n' +
    'a=setup:actpass\r\n' +
    'a=mid:2\r\n' +
    'a=sctp-port:5000\r\n' +
    'a=max-message-size:262144\r\n'
}
13:27:52.570 No playerId specified, cannot forward message: { type: 'iceCandidate', playerId: 2, candidate: [Object] }
13:27:52.571 No playerId specified, cannot forward message: { type: 'iceCandidate', playerId: 2, candidate: [Object] }
13:27:52.603 No playerId specified, cannot forward message: { type: 'iceCandidate', playerId: 2, candidate: [Object] }
13:27:52.637 No playerId specified, cannot forward message: { type: 'iceCandidate', playerId: 2, candidate: [Object] }
13:27:52.670 No playerId specified, cannot forward message: { type: 'iceCandidate', playerId: 2, candidate: [Object] }
13:27:52.704 No playerId specified, cannot forward message: { type: 'iceCandidate', playerId: 2, candidate: [Object] }
13:27:53.289 player 3 (::1) connected
13:27:53.290 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:27:53.291 [37m[players] <-[32m {"type":"playerCount","count":1}
13:27:53.292 [37m3 ->[34m {"type":"listStreamers"}
13:27:53.293 [37m3 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
13:27:53.319 [37m3 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
13:27:53.320 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"3","dataChannel":true,"sfu":false,"sendOffer":true}
13:27:53.414 [37mStreamer Component -> 3[36m {"type":"offer","playerId":3,"sdp":"v=0\r\no=- 769354477661572931 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:kCAw\r\na=ice-pwd:12aMGRjXH82hkGN7UcBN79Tk\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2C:3A:62:10:14:12:41:D1:2A:24:6A:12:50:31:DF:D3:00:68:EE:5B:59:76:CB:EE:55:69:7E:21:4C:6D:F9:79\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3708738341 4294292779\r\na=ssrc:3708738341 cname:RDHQEw3tSBLDuSu5\r\na=ssrc:3708738341 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:4294292779 cname:RDHQEw3tSBLDuSu5\r\na=ssrc:4294292779 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:kCAw\r\na=ice-pwd:12aMGRjXH82hkGN7UcBN79Tk\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2C:3A:62:10:14:12:41:D1:2A:24:6A:12:50:31:DF:D3:00:68:EE:5B:59:76:CB:EE:55:69:7E:21:4C:6D:F9:79\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3831170162 cname:RDHQEw3tSBLDuSu5\r\na=ssrc:3831170162 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:kCAw\r\na=ice-pwd:12aMGRjXH82hkGN7UcBN79Tk\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2C:3A:62:10:14:12:41:D1:2A:24:6A:12:50:31:DF:D3:00:68:EE:5B:59:76:CB:EE:55:69:7E:21:4C:6D:F9:79\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:27:53.448 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1990309534 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag kCAw network-id 1"}}
13:27:53.450 [37m3 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 442340523800541238 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:/AT+\r\na=ice-pwd:TTz5vWhH0MVhdRhrtc8/q4lQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 85:F6:1F:D8:6A:BF:3C:EE:BB:D5:19:AC:AD:25:BA:31:8B:67:AF:37:7F:49:95:01:4C:9C:BF:B1:0F:33:73:2D\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:/AT+\r\na=ice-pwd:TTz5vWhH0MVhdRhrtc8/q4lQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 85:F6:1F:D8:6A:BF:3C:EE:BB:D5:19:AC:AD:25:BA:31:8B:67:AF:37:7F:49:95:01:4C:9C:BF:B1:0F:33:73:2D\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 11064f6f-9218-49dc-a8b1-1aba842ef680\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1336678766 cname:Mhqcfnhpab5fbG9d\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:/AT+\r\na=ice-pwd:TTz5vWhH0MVhdRhrtc8/q4lQ\r\na=ice-options:trickle\r\na=fingerprint:sha-256 85:F6:1F:D8:6A:BF:3C:EE:BB:D5:19:AC:AD:25:BA:31:8B:67:AF:37:7F:49:95:01:4C:9C:BF:B1:0F:33:73:2D\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:27:53.450 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1690546172 1 udp 2122260223 ************ 54776 typ host generation 0 ufrag /AT+ network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"/AT+"}}
13:27:53.451 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:775224755 1 udp 2122194687 ************* 54777 typ host generation 0 ufrag /AT+ network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"/AT+"}}
13:27:53.481 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:668575177 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag kCAw network-id 4"}}
13:27:53.515 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1990309534 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag kCAw network-id 1"}}
13:27:53.548 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:668575177 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag kCAw network-id 4"}}
13:27:53.582 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1990309534 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag kCAw network-id 1"}}
13:27:53.616 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:668575177 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag kCAw network-id 4"}}
13:27:53.650 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:141448198 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag kCAw network-id 1"}}
13:27:53.684 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1494632273 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag kCAw network-id 4"}}
13:27:53.717 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:141448198 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag kCAw network-id 1"}}
13:27:53.751 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1494632273 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag kCAw network-id 4"}}
13:27:53.785 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:141448198 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag kCAw network-id 1"}}
13:27:53.818 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1494632273 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag kCAw network-id 4"}}
13:28:43.446 [37mStreamer Component ->[34m {"type":"ping","time":1754544523}
13:29:43.472 [37mStreamer Component ->[34m {"type":"ping","time":1754544583}
13:30:33.826 player 4 (::1) connected
13:30:33.827 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:30:33.827 [37m[players] <-[32m {"type":"playerCount","count":2}
13:30:33.828 [37m4 ->[34m {"type":"listStreamers"}
13:30:33.828 [37m4 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
13:30:33.856 [37m4 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
13:30:33.856 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"4","dataChannel":true,"sfu":false,"sendOffer":true}
13:30:33.893 [37mStreamer Component -> 4[36m {"type":"offer","playerId":4,"sdp":"v=0\r\no=- 468158889672206332 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Q7kU\r\na=ice-pwd:jA66tSQVIOf0YCasnKuYe8im\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3B:C3:CB:22:32:43:00:FA:76:15:34:21:CC:0D:8B:86:45:6B:41:AE:10:C3:FD:09:D0:64:B3:56:04:9C:50:BF\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 78681182 3061677717\r\na=ssrc:78681182 cname:8h7IeEYwA/dwMRhx\r\na=ssrc:78681182 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3061677717 cname:8h7IeEYwA/dwMRhx\r\na=ssrc:3061677717 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Q7kU\r\na=ice-pwd:jA66tSQVIOf0YCasnKuYe8im\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3B:C3:CB:22:32:43:00:FA:76:15:34:21:CC:0D:8B:86:45:6B:41:AE:10:C3:FD:09:D0:64:B3:56:04:9C:50:BF\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1151338313 cname:8h7IeEYwA/dwMRhx\r\na=ssrc:1151338313 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Q7kU\r\na=ice-pwd:jA66tSQVIOf0YCasnKuYe8im\r\na=ice-options:trickle\r\na=fingerprint:sha-256 3B:C3:CB:22:32:43:00:FA:76:15:34:21:CC:0D:8B:86:45:6B:41:AE:10:C3:FD:09:D0:64:B3:56:04:9C:50:BF\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:30:33.926 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:905469841 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag Q7kU network-id 1"}}
13:30:33.936 [37m4 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 8159024107693269376 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:/29X\r\na=ice-pwd:ykcCTzkuO2NTaoMf0L8PWW+x\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6F:2B:FF:88:99:10:7C:34:2D:EA:18:07:8A:28:B5:96:5F:9D:8C:86:16:9B:E0:79:FA:FC:AA:0C:BF:2B:06:E9\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:/29X\r\na=ice-pwd:ykcCTzkuO2NTaoMf0L8PWW+x\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6F:2B:FF:88:99:10:7C:34:2D:EA:18:07:8A:28:B5:96:5F:9D:8C:86:16:9B:E0:79:FA:FC:AA:0C:BF:2B:06:E9\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- ea70f358-874b-4fb2-a0e8-8cf1ea838149\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2346021906 cname:ILXcBgkftSnLw8ie\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:/29X\r\na=ice-pwd:ykcCTzkuO2NTaoMf0L8PWW+x\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6F:2B:FF:88:99:10:7C:34:2D:EA:18:07:8A:28:B5:96:5F:9D:8C:86:16:9B:E0:79:FA:FC:AA:0C:BF:2B:06:E9\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:30:33.937 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2733044197 1 udp 2122260223 ************ 58988 typ host generation 0 ufrag /29X network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"/29X"}}
13:30:33.937 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3893470122 1 udp 2122194687 ************* 58989 typ host generation 0 ufrag /29X network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"/29X"}}
13:30:33.960 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3232162773 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag Q7kU network-id 4"}}
13:30:33.993 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:905469841 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag Q7kU network-id 1"}}
13:30:34.027 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3232162773 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag Q7kU network-id 4"}}
13:30:34.061 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:905469841 1 udp 2122260223 ************* 49155 typ host generation 0 ufrag Q7kU network-id 1"}}
13:30:34.094 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3232162773 1 udp 2122194687 ************ 49155 typ host generation 0 ufrag Q7kU network-id 4"}}
13:30:34.128 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3411184389 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag Q7kU network-id 1"}}
13:30:34.161 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1040987969 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag Q7kU network-id 4"}}
13:30:43.490 [37mStreamer Component ->[34m {"type":"ping","time":1754544643}
13:31:43.451 [37mStreamer Component ->[34m {"type":"ping","time":1754544703}
13:32:43.439 [37mStreamer Component ->[34m {"type":"ping","time":1754544763}
13:33:43.428 [37mStreamer Component ->[34m {"type":"ping","time":1754544823}
13:34:43.446 [37mStreamer Component ->[34m {"type":"ping","time":1754544883}
13:35:43.419 [37mStreamer Component ->[34m {"type":"ping","time":1754544943}
13:36:43.468 [37mStreamer Component ->[34m {"type":"ping","time":1754545003}
13:37:43.460 [37mStreamer Component ->[34m {"type":"ping","time":1754545063}
13:38:43.429 [37mStreamer Component ->[34m {"type":"ping","time":1754545123}
13:39:43.460 [37mStreamer Component ->[34m {"type":"ping","time":1754545183}
13:40:43.459 [37mStreamer Component ->[34m {"type":"ping","time":1754545243}
13:41:37.226 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
13:41:37.227 unsubscribing all players on Streamer Component
13:41:37.228 player 3 connection closed: 1005 - 
13:41:37.229 [37m[players] <-[32m {"type":"playerCount","count":1}
13:41:37.232 player 4 connection closed: 1005 - 
13:41:37.232 [37m[players] <-[32m {"type":"playerCount","count":0}
