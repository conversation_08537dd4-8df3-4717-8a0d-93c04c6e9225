15:34:45.263 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
15:34:45.302 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
15:34:45.303 Redirecting http->https
15:34:45.308 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
15:34:45.315 WebSocket listening for Streamer connections on :8888
15:34:45.316 WebSocket listening for SFU connections on :8889
15:34:45.316 WebSocket listening for Players connections on :80
15:34:45.317 Http listening on *: 80
15:34:45.318 Https listening on *: 443
15:42:05.774 Streamer connected: ::1
15:42:05.774 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:42:05.775 [37m::1 <-[32m {"type":"identify"}
15:42:06.276 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:42:06.276 Registered new streamer: Streamer Component
15:42:54.939 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
15:42:54.940 unsubscribing all players on Streamer Component
