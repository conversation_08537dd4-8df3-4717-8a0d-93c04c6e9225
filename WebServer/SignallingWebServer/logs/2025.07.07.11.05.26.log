11:05:26.261 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
11:05:26.301 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
11:05:26.302 Redirecting http->https
11:05:26.307 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
11:05:26.314 WebSocket listening for Streamer connections on :8888
11:05:26.315 WebSocket listening for SFU connections on :8889
11:05:26.315 WebSocket listening for Players connections on :80
11:05:26.316 Http listening on *: 80
11:05:26.316 Https listening on *: 443
11:09:24.788 Streamer connected: ::1
11:09:24.789 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
11:09:24.790 [37m::1 <-[32m {"type":"identify"}
11:09:25.283 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
11:09:25.284 Registered new streamer: Streamer Component
11:10:31.105 [37mStreamer Component ->[34m {"type":"ping","time":1754536231}
11:11:31.125 [37mStreamer Component ->[34m {"type":"ping","time":1754536291}
11:12:31.084 [37mStreamer Component ->[34m {"type":"ping","time":1754536351}
11:12:55.977 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
11:12:55.978 unsubscribing all players on Streamer Component
