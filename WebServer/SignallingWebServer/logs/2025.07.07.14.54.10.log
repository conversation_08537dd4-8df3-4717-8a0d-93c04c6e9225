14:54:10.332 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
14:54:10.409 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
14:54:10.412 Redirecting http->https
14:54:10.422 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
14:54:10.433 WebSocket listening for Streamer connections on :8888
14:54:10.435 WebSocket listening for SFU connections on :8889
14:54:10.436 WebSocket listening for Players connections on :80
14:54:10.437 Http listening on *: 80
14:54:10.438 Https listening on *: 443
14:55:22.114 Streamer connected: ::1
14:55:22.115 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
14:55:22.116 [37m::1 <-[32m {"type":"identify"}
14:55:22.568 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
14:55:22.569 Registered new streamer: Streamer Component
14:55:49.509 player 1 (::1) connected
14:55:49.510 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
14:55:49.510 [37m[players] <-[32m {"type":"playerCount","count":1}
14:55:49.512 [37m1 ->[34m {"type":"listStreamers"}
14:55:49.512 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
14:55:49.541 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
14:55:49.542 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
14:55:49.637 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 8689996733232116979 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:rKyq\r\na=ice-pwd:JHFLYSTZ+GEGIH6lyxp1iSFK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BC:FA:AC:E1:59:B4:E6:6B:03:F5:02:4E:7E:DB:2B:23:68:4A:D3:6C:66:8A:EB:F6:EC:DE:47:C6:AB:2E:1C:3F\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3988485689 882560001\r\na=ssrc:3988485689 cname:7q57xWud6PpuQBmt\r\na=ssrc:3988485689 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:882560001 cname:7q57xWud6PpuQBmt\r\na=ssrc:882560001 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:rKyq\r\na=ice-pwd:JHFLYSTZ+GEGIH6lyxp1iSFK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BC:FA:AC:E1:59:B4:E6:6B:03:F5:02:4E:7E:DB:2B:23:68:4A:D3:6C:66:8A:EB:F6:EC:DE:47:C6:AB:2E:1C:3F\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:969164248 cname:7q57xWud6PpuQBmt\r\na=ssrc:969164248 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:rKyq\r\na=ice-pwd:JHFLYSTZ+GEGIH6lyxp1iSFK\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BC:FA:AC:E1:59:B4:E6:6B:03:F5:02:4E:7E:DB:2B:23:68:4A:D3:6C:66:8A:EB:F6:EC:DE:47:C6:AB:2E:1C:3F\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:55:49.639 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3888037335 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag rKyq network-id 1"}}
14:55:49.670 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3066474112 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag rKyq network-id 4"}}
14:55:49.678 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 4213197027011466155 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:1eHU\r\na=ice-pwd:DViOKlEaH5hgAZ/JwgWZ3ssP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5C:C4:E0:1F:D2:43:6E:47:10:8E:D4:01:3D:87:1B:EC:9B:26:BE:34:33:34:01:57:82:25:5F:21:2D:1D:17:23\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:1eHU\r\na=ice-pwd:DViOKlEaH5hgAZ/JwgWZ3ssP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5C:C4:E0:1F:D2:43:6E:47:10:8E:D4:01:3D:87:1B:EC:9B:26:BE:34:33:34:01:57:82:25:5F:21:2D:1D:17:23\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- d31a704d-573b-47a5-81b1-5b12a94cae68\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2397243349 cname:qqCe2Fhy/CG/dWoF\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:1eHU\r\na=ice-pwd:DViOKlEaH5hgAZ/JwgWZ3ssP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5C:C4:E0:1F:D2:43:6E:47:10:8E:D4:01:3D:87:1B:EC:9B:26:BE:34:33:34:01:57:82:25:5F:21:2D:1D:17:23\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:55:49.679 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2321648896 1 udp 2122260223 ************ 60526 typ host generation 0 ufrag 1eHU network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"1eHU"}}
14:55:49.680 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3231113039 1 udp 2122194687 ************* 60527 typ host generation 0 ufrag 1eHU network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"1eHU"}}
14:55:49.703 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3888037335 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag rKyq network-id 1"}}
14:55:49.737 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3066474112 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag rKyq network-id 4"}}
14:55:49.770 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3888037335 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag rKyq network-id 1"}}
14:55:49.804 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3066474112 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag rKyq network-id 4"}}
14:55:49.838 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2574344015 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag rKyq network-id 1"}}
14:55:49.871 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3356052504 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag rKyq network-id 4"}}
14:56:22.716 [37mStreamer Component ->[34m {"type":"ping","time":1754549782}
14:57:22.677 [37mStreamer Component ->[34m {"type":"ping","time":1754549842}
