10:19:12.118 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
10:19:12.160 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
10:19:12.161 Redirecting http->https
10:19:12.167 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
10:19:12.173 WebSocket listening for Streamer connections on :8888
10:19:12.174 WebSocket listening for SFU connections on :8889
10:19:12.175 WebSocket listening for Players connections on :80
10:19:12.176 Http listening on *: 80
10:19:12.176 Https listening on *: 443
10:21:38.252 Streamer connected: ::1
10:21:38.253 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:21:38.254 [37m::1 <-[32m {"type":"identify"}
10:21:38.790 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
10:21:38.790 Registered new streamer: Streamer Component
10:22:20.351 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
10:22:20.352 unsubscribing all players on Streamer Component
