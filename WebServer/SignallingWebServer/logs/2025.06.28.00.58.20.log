00:58:20.249 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
00:58:20.302 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
00:58:20.303 Redirecting http->https
00:58:20.309 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
00:58:20.317 WebSocket listening for Streamer connections on :8888
00:58:20.318 WebSocket listening for SFU connections on :8889
00:58:20.319 WebSocket listening for Players connections on :80
00:58:20.320 Http listening on *: 80
00:58:20.320 Https listening on *: 443
00:58:20.958 Streamer connected: ::1
00:58:20.959 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
00:58:20.960 [37m::1 <-[32m {"type":"identify"}
00:58:21.044 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
00:58:21.045 Registered new streamer: Streamer Component
00:59:21.013 [37mStreamer Component ->[34m {"type":"ping","time":1753635560}
00:59:58.762 player 1 (::1) connected
00:59:58.763 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
00:59:58.763 [37m[players] <-[32m {"type":"playerCount","count":1}
00:59:58.765 [37m1 ->[34m {"type":"listStreamers"}
00:59:58.766 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
00:59:58.787 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
00:59:58.788 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
00:59:58.866 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 8859828525861411837 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:0FMm\r\na=ice-pwd:fLrbSpEtT/tJobTYjhy5xIUU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 40:C3:09:11:35:C2:2C:EB:89:AA:C3:82:14:FB:60:57:15:91:71:89:F2:FD:96:BD:8C:5F:C8:7B:D3:48:8E:C3\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3116747362 661180662\r\na=ssrc:3116747362 cname:taJeYKp8ijhIqTbR\r\na=ssrc:3116747362 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:661180662 cname:taJeYKp8ijhIqTbR\r\na=ssrc:661180662 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:0FMm\r\na=ice-pwd:fLrbSpEtT/tJobTYjhy5xIUU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 40:C3:09:11:35:C2:2C:EB:89:AA:C3:82:14:FB:60:57:15:91:71:89:F2:FD:96:BD:8C:5F:C8:7B:D3:48:8E:C3\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:905905571 cname:taJeYKp8ijhIqTbR\r\na=ssrc:905905571 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:0FMm\r\na=ice-pwd:fLrbSpEtT/tJobTYjhy5xIUU\r\na=ice-options:trickle\r\na=fingerprint:sha-256 40:C3:09:11:35:C2:2C:EB:89:AA:C3:82:14:FB:60:57:15:91:71:89:F2:FD:96:BD:8C:5F:C8:7B:D3:48:8E:C3\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
00:59:58.899 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 1227059055857496304 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:8KAj\r\na=ice-pwd:Drt4atOGzOjSVn4fpyvarrxl\r\na=ice-options:trickle\r\na=fingerprint:sha-256 57:B0:89:8C:1E:4E:BE:35:CF:73:2D:5B:6D:32:E1:BA:FE:7F:60:79:B6:36:04:E3:6E:F3:F7:5C:C7:89:5A:DF\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:8KAj\r\na=ice-pwd:Drt4atOGzOjSVn4fpyvarrxl\r\na=ice-options:trickle\r\na=fingerprint:sha-256 57:B0:89:8C:1E:4E:BE:35:CF:73:2D:5B:6D:32:E1:BA:FE:7F:60:79:B6:36:04:E3:6E:F3:F7:5C:C7:89:5A:DF\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 0df084e3-c7c0-46c0-8218-c7124d332ef3\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2238193983 cname:JfXQ63QWEVwkYtPW\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:8KAj\r\na=ice-pwd:Drt4atOGzOjSVn4fpyvarrxl\r\na=ice-options:trickle\r\na=fingerprint:sha-256 57:B0:89:8C:1E:4E:BE:35:CF:73:2D:5B:6D:32:E1:BA:FE:7F:60:79:B6:36:04:E3:6E:F3:F7:5C:C7:89:5A:DF\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
00:59:58.900 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2842630018 1 udp 2122260223 *********** 58697 typ host generation 0 ufrag 8KAj network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"8KAj"}}
00:59:58.901 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:125187812 1 udp 2122194687 ************* 58698 typ host generation 0 ufrag 8KAj network-id 3","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"8KAj"}}
00:59:58.902 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1491213010 1 udp 2122129151 ************* 58699 typ host generation 0 ufrag 8KAj network-id 1 network-cost 10","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"8KAj"}}
00:59:58.902 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1196970088 1 udp 2122260223 *********** 49152 typ host generation 0 ufrag 0FMm network-id 1"}}
00:59:58.933 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3957316395 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag 0FMm network-id 2"}}
00:59:58.967 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:607836830 1 udp 2122129151 ************* 49152 typ host generation 0 ufrag 0FMm network-id 3 network-cost 10"}}
00:59:59.001 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1196970088 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag 0FMm network-id 1"}}
00:59:59.035 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3957316395 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag 0FMm network-id 2"}}
00:59:59.069 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:607836830 1 udp 2122129151 ************* 49153 typ host generation 0 ufrag 0FMm network-id 3 network-cost 10"}}
00:59:59.103 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1196970088 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag 0FMm network-id 1"}}
00:59:59.137 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3957316395 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag 0FMm network-id 2"}}
00:59:59.171 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:607836830 1 udp 2122129151 ************* 49154 typ host generation 0 ufrag 0FMm network-id 3 network-cost 10"}}
01:00:11.926 player 2 (::1) connected
01:00:11.927 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:00:11.927 [37m[players] <-[32m {"type":"playerCount","count":2}
01:00:11.927 [37m2 ->[34m {"type":"listStreamers"}
01:00:11.928 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
01:00:11.952 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
01:00:11.952 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
01:00:12.050 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 504470832049850705 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:OT+M\r\na=ice-pwd:axVHvPrgFcobO+kqp/NzgSlL\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AB:2A:2D:8B:D2:3A:8C:5A:5D:20:F2:9A:D5:C5:EC:6F:0A:39:D0:3D:E5:2C:FB:32:87:06:6C:66:73:00:40:42\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3022263272 1478531632\r\na=ssrc:3022263272 cname:V2b681dY942lvNSk\r\na=ssrc:3022263272 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1478531632 cname:V2b681dY942lvNSk\r\na=ssrc:1478531632 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:OT+M\r\na=ice-pwd:axVHvPrgFcobO+kqp/NzgSlL\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AB:2A:2D:8B:D2:3A:8C:5A:5D:20:F2:9A:D5:C5:EC:6F:0A:39:D0:3D:E5:2C:FB:32:87:06:6C:66:73:00:40:42\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:4275519181 cname:V2b681dY942lvNSk\r\na=ssrc:4275519181 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:OT+M\r\na=ice-pwd:axVHvPrgFcobO+kqp/NzgSlL\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AB:2A:2D:8B:D2:3A:8C:5A:5D:20:F2:9A:D5:C5:EC:6F:0A:39:D0:3D:E5:2C:FB:32:87:06:6C:66:73:00:40:42\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:00:12.078 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5997162967710248939 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:YdO2\r\na=ice-pwd:grVnF75c0zfFvY7uApbDc/9N\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:7E:F5:5D:0C:D6:45:40:E4:75:B7:DD:A4:A8:C1:D6:CC:21:22:2A:71:41:12:F5:69:B9:C9:5A:AE:65:D6:17\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:YdO2\r\na=ice-pwd:grVnF75c0zfFvY7uApbDc/9N\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:7E:F5:5D:0C:D6:45:40:E4:75:B7:DD:A4:A8:C1:D6:CC:21:22:2A:71:41:12:F5:69:B9:C9:5A:AE:65:D6:17\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 63d8ca1c-d224-4950-9f61-badf0b1fdd1b\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2724924364 cname:d+Pu1GK3xd5Add53\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:YdO2\r\na=ice-pwd:grVnF75c0zfFvY7uApbDc/9N\r\na=ice-options:trickle\r\na=fingerprint:sha-256 AC:7E:F5:5D:0C:D6:45:40:E4:75:B7:DD:A4:A8:C1:D6:CC:21:22:2A:71:41:12:F5:69:B9:C9:5A:AE:65:D6:17\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:00:12.080 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3546641554 1 udp 2113937151 496b27d3-7bd4-44a3-8342-5e11bb55d90c.local 52258 typ host generation 0 ufrag YdO2 network-cost 999","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"YdO2"}}
01:00:12.083 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:718896322 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag OT+M network-id 1"}}
01:00:12.117 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2254370689 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag OT+M network-id 2"}}
01:00:12.153 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1237055028 1 udp 2122129151 ************* 49153 typ host generation 0 ufrag OT+M network-id 3 network-cost 10"}}
01:00:12.185 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:718896322 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag OT+M network-id 1"}}
01:00:12.219 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2254370689 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag OT+M network-id 2"}}
01:00:12.253 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1237055028 1 udp 2122129151 ************* 49154 typ host generation 0 ufrag OT+M network-id 3 network-cost 10"}}
01:00:12.287 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:718896322 1 udp 2122260223 *********** 49155 typ host generation 0 ufrag OT+M network-id 1"}}
01:00:12.321 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2254370689 1 udp 2122194687 ************* 49155 typ host generation 0 ufrag OT+M network-id 2"}}
01:00:12.355 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1237055028 1 udp 2122129151 ************* 49155 typ host generation 0 ufrag OT+M network-id 3 network-cost 10"}}
01:00:13.224 player 2 connection closed: 1001 - 
01:00:13.227 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"2"}
01:00:13.227 [37m[players] <-[32m {"type":"playerCount","count":1}
01:00:15.337 player 3 (::1) connected
01:00:15.338 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:00:15.338 [37m[players] <-[32m {"type":"playerCount","count":2}
01:00:15.339 [37m3 ->[34m {"type":"listStreamers"}
01:00:15.339 [37m3 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
01:00:15.362 [37m3 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
01:00:15.362 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"3","dataChannel":true,"sfu":false,"sendOffer":true}
01:00:15.412 [37mStreamer Component -> 3[36m {"type":"offer","playerId":3,"sdp":"v=0\r\no=- 3795233645262643208 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:qFU+\r\na=ice-pwd:jXS65CAgduqjlgew2jERpD5D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 55:02:78:63:D9:26:96:70:AB:26:97:BF:6E:28:59:42:30:57:DB:A9:5F:46:42:94:D8:0B:6F:58:CC:44:74:FA\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3037745005 769126245\r\na=ssrc:3037745005 cname:eV7zMuSIVVq1YuAT\r\na=ssrc:3037745005 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:769126245 cname:eV7zMuSIVVq1YuAT\r\na=ssrc:769126245 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:qFU+\r\na=ice-pwd:jXS65CAgduqjlgew2jERpD5D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 55:02:78:63:D9:26:96:70:AB:26:97:BF:6E:28:59:42:30:57:DB:A9:5F:46:42:94:D8:0B:6F:58:CC:44:74:FA\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1754151790 cname:eV7zMuSIVVq1YuAT\r\na=ssrc:1754151790 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:qFU+\r\na=ice-pwd:jXS65CAgduqjlgew2jERpD5D\r\na=ice-options:trickle\r\na=fingerprint:sha-256 55:02:78:63:D9:26:96:70:AB:26:97:BF:6E:28:59:42:30:57:DB:A9:5F:46:42:94:D8:0B:6F:58:CC:44:74:FA\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:00:15.443 [37m3 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5982496203662689774 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:5zyh\r\na=ice-pwd:msBRkTk8oa0qr7oXkD2VOtKu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4E:BA:07:77:CA:A6:3E:12:B9:7A:82:25:44:6E:91:A9:15:74:9B:1D:3E:D3:56:FF:90:E5:D1:9E:FE:6A:01:B2\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:5zyh\r\na=ice-pwd:msBRkTk8oa0qr7oXkD2VOtKu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4E:BA:07:77:CA:A6:3E:12:B9:7A:82:25:44:6E:91:A9:15:74:9B:1D:3E:D3:56:FF:90:E5:D1:9E:FE:6A:01:B2\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 0d87bbe2-af99-46ef-9b65-bb96648172fc\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3667406288 cname:At8V+9GfE1u92AwG\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:5zyh\r\na=ice-pwd:msBRkTk8oa0qr7oXkD2VOtKu\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4E:BA:07:77:CA:A6:3E:12:B9:7A:82:25:44:6E:91:A9:15:74:9B:1D:3E:D3:56:FF:90:E5:D1:9E:FE:6A:01:B2\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
01:00:15.445 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2465972549 1 udp 2113937151 fd722e42-0bbf-4e1d-91c4-28dee5132fc0.local 63691 typ host generation 0 ufrag 5zyh network-cost 999","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"5zyh"}}
01:00:15.446 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4162001028 1 udp 2122260223 *********** 49153 typ host generation 0 ufrag qFU+ network-id 1"}}
01:00:15.480 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3277921199 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag qFU+ network-id 2"}}
01:00:15.514 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2633316249 1 udp 2122129151 ************* 49153 typ host generation 0 ufrag qFU+ network-id 3 network-cost 10"}}
01:00:15.549 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4162001028 1 udp 2122260223 *********** 49154 typ host generation 0 ufrag qFU+ network-id 1"}}
01:00:15.582 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3277921199 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag qFU+ network-id 2"}}
01:00:15.615 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2633316249 1 udp 2122129151 ************* 49154 typ host generation 0 ufrag qFU+ network-id 3 network-cost 10"}}
01:00:15.649 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4162001028 1 udp 2122260223 *********** 49155 typ host generation 0 ufrag qFU+ network-id 1"}}
01:00:15.683 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3277921199 1 udp 2122194687 ************* 49155 typ host generation 0 ufrag qFU+ network-id 2"}}
01:00:15.717 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2633316249 1 udp 2122129151 ************* 49155 typ host generation 0 ufrag qFU+ network-id 3 network-cost 10"}}
01:00:21.018 [37mStreamer Component ->[34m {"type":"ping","time":1753635621}
01:01:21.019 [37mStreamer Component ->[34m {"type":"ping","time":1753635681}
01:02:20.986 [37mStreamer Component ->[34m {"type":"ping","time":1753635740}
01:03:20.996 [37mStreamer Component ->[34m {"type":"ping","time":1753635800}
01:04:21.011 [37mStreamer Component ->[34m {"type":"ping","time":1753635861}
01:05:21.016 [37mStreamer Component ->[34m {"type":"ping","time":1753635920}
01:06:21.001 [37mStreamer Component ->[34m {"type":"ping","time":1753635980}
01:07:21.012 [37mStreamer Component ->[34m {"type":"ping","time":1753636041}
01:08:21.000 [37mStreamer Component ->[34m {"type":"ping","time":1753636100}
01:09:20.999 [37mStreamer Component ->[34m {"type":"ping","time":1753636160}
01:10:21.016 [37mStreamer Component ->[34m {"type":"ping","time":1753636220}
01:11:21.019 [37mStreamer Component ->[34m {"type":"ping","time":1753636281}
01:12:21.023 [37mStreamer Component ->[34m {"type":"ping","time":1753636341}
01:13:21.039 [37mStreamer Component ->[34m {"type":"ping","time":1753636401}
01:14:21.003 [37mStreamer Component ->[34m {"type":"ping","time":1753636460}
01:15:21.039 [37mStreamer Component ->[34m {"type":"ping","time":1753636521}
01:16:21.010 [37mStreamer Component ->[34m {"type":"ping","time":1753636581}
01:17:20.988 [37mStreamer Component ->[34m {"type":"ping","time":1753636640}
01:18:21.028 [37mStreamer Component ->[34m {"type":"ping","time":1753636700}
01:19:21.001 [37mStreamer Component ->[34m {"type":"ping","time":1753636760}
01:20:21.016 [37mStreamer Component ->[34m {"type":"ping","time":1753636821}
01:21:20.991 [37mStreamer Component ->[34m {"type":"ping","time":1753636880}
01:22:21.011 [37mStreamer Component ->[34m {"type":"ping","time":1753636940}
01:23:21.003 [37mStreamer Component ->[34m {"type":"ping","time":1753637001}
01:24:21.036 [37mStreamer Component ->[34m {"type":"ping","time":1753637061}
01:25:21.000 [37mStreamer Component ->[34m {"type":"ping","time":1753637120}
01:26:21.005 [37mStreamer Component ->[34m {"type":"ping","time":1753637181}
01:27:21.043 [37mStreamer Component ->[34m {"type":"ping","time":1753637241}
01:28:21.000 [37mStreamer Component ->[34m {"type":"ping","time":1753637300}
01:29:21.025 [37mStreamer Component ->[34m {"type":"ping","time":1753637361}
01:29:54.879 player 4 (::1) connected
01:29:54.881 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
01:29:54.881 [37m[players] <-[32m {"type":"playerCount","count":3}
01:30:21.018 [37mStreamer Component ->[34m {"type":"ping","time":1753637421}
01:31:21.035 [37mStreamer Component ->[34m {"type":"ping","time":1753637481}
01:32:20.988 [37mStreamer Component ->[34m {"type":"ping","time":1753637540}
