10:37:53.292 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
10:37:53.332 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
10:37:53.333 Redirecting http->https
10:37:53.339 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
10:37:53.345 WebSocket listening for Streamer connections on :8888
10:37:53.346 WebSocket listening for SFU connections on :8889
10:37:53.346 WebSocket listening for Players connections on :80
10:37:53.347 Http listening on *: 80
10:37:53.348 Https listening on *: 443
10:38:13.524 Streamer connected: ::1
10:38:13.525 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
10:38:13.525 [37m::1 <-[32m {"type":"identify"}
10:38:14.027 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
10:38:14.027 Registered new streamer: Streamer Component
10:39:19.432 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
10:39:19.433 unsubscribing all players on Streamer Component
