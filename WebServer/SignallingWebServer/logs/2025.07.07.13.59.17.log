13:59:17.677 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
13:59:17.717 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
13:59:17.719 Redirecting http->https
13:59:17.723 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
13:59:17.730 WebSocket listening for Streamer connections on :8888
13:59:17.731 WebSocket listening for SFU connections on :8889
13:59:17.732 WebSocket listening for Players connections on :80
13:59:17.733 Http listening on *: 80
13:59:17.733 Https listening on *: 443
14:00:47.010 Streamer connected: ::1
14:00:47.011 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
14:00:47.012 [37m::1 <-[32m {"type":"identify"}
14:00:47.504 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
14:00:47.505 Registered new streamer: Streamer Component
14:01:24.166 player 1 (::1) connected
14:01:24.168 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
14:01:24.168 [37m[players] <-[32m {"type":"playerCount","count":1}
14:01:24.187 player 1 connection closed: 1001 - 
14:01:24.187 [37m[players] <-[32m {"type":"playerCount","count":0}
14:01:24.877 player 2 (::1) connected
14:01:24.878 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
14:01:24.879 [37m[players] <-[32m {"type":"playerCount","count":1}
14:01:24.879 [37m2 ->[34m {"type":"listStreamers"}
14:01:24.880 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
14:01:24.918 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
14:01:24.919 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
14:01:25.032 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 2370411507782573616 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:occn\r\na=ice-pwd:kCJXvjsRHSozRk9qaJW4KiSi\r\na=ice-options:trickle\r\na=fingerprint:sha-256 0A:B1:ED:1D:8F:B9:55:6E:59:F3:63:18:CF:90:21:12:65:48:E3:39:1E:8A:12:B7:86:1C:C3:94:C6:7D:24:96\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3758046038 4214222838\r\na=ssrc:3758046038 cname:blWWgzqLFjRbYcxM\r\na=ssrc:3758046038 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:4214222838 cname:blWWgzqLFjRbYcxM\r\na=ssrc:4214222838 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:occn\r\na=ice-pwd:kCJXvjsRHSozRk9qaJW4KiSi\r\na=ice-options:trickle\r\na=fingerprint:sha-256 0A:B1:ED:1D:8F:B9:55:6E:59:F3:63:18:CF:90:21:12:65:48:E3:39:1E:8A:12:B7:86:1C:C3:94:C6:7D:24:96\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:4021420856 cname:blWWgzqLFjRbYcxM\r\na=ssrc:4021420856 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:occn\r\na=ice-pwd:kCJXvjsRHSozRk9qaJW4KiSi\r\na=ice-options:trickle\r\na=fingerprint:sha-256 0A:B1:ED:1D:8F:B9:55:6E:59:F3:63:18:CF:90:21:12:65:48:E3:39:1E:8A:12:B7:86:1C:C3:94:C6:7D:24:96\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:01:25.035 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3163873638 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag occn network-id 1"}}
14:01:25.065 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3991728689 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag occn network-id 4"}}
14:01:25.092 [37m2 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 7454844263804517982 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:tgpM\r\na=ice-pwd:XTBu7xYpXrjUdr0iNnZXB5mO\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C4:96:6A:36:A0:7E:C7:E4:32:49:5B:CC:C0:3D:63:2F:D2:9B:46:D8:87:2F:51:60:9E:62:35:D2:C1:04:57:BC\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:tgpM\r\na=ice-pwd:XTBu7xYpXrjUdr0iNnZXB5mO\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C4:96:6A:36:A0:7E:C7:E4:32:49:5B:CC:C0:3D:63:2F:D2:9B:46:D8:87:2F:51:60:9E:62:35:D2:C1:04:57:BC\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- b212a76c-f6a5-4558-b4fa-f8ed9b9b6c9f\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3222251074 cname:GrUXGQr8fsKlErdt\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:tgpM\r\na=ice-pwd:XTBu7xYpXrjUdr0iNnZXB5mO\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C4:96:6A:36:A0:7E:C7:E4:32:49:5B:CC:C0:3D:63:2F:D2:9B:46:D8:87:2F:51:60:9E:62:35:D2:C1:04:57:BC\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:01:25.093 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3030823503 1 udp 2122260223 ************ 55460 typ host generation 0 ufrag tgpM network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"tgpM"}}
14:01:25.094 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1385109788 1 udp 2122194687 ************* 55461 typ host generation 0 ufrag tgpM network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"tgpM"}}
14:01:25.100 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3163873638 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag occn network-id 1"}}
14:01:25.133 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3991728689 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag occn network-id 4"}}
14:01:25.166 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3163873638 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag occn network-id 1"}}
14:01:25.200 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3991728689 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag occn network-id 4"}}
14:01:25.234 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3260744702 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag occn network-id 1"}}
14:01:25.267 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2468548777 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag occn network-id 4"}}
14:01:25.301 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3260744702 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag occn network-id 1"}}
14:01:25.334 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2468548777 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag occn network-id 4"}}
14:01:25.368 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3260744702 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag occn network-id 1"}}
14:01:25.402 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2468548777 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag occn network-id 4"}}
14:01:48.254 [37mStreamer Component ->[34m {"type":"ping","time":1754546508}
14:02:48.282 [37mStreamer Component ->[34m {"type":"ping","time":1754546568}
14:03:48.295 [37mStreamer Component ->[34m {"type":"ping","time":1754546628}
14:04:48.228 [37mStreamer Component ->[34m {"type":"ping","time":1754546688}
14:05:48.278 [37mStreamer Component ->[34m {"type":"ping","time":1754546748}
14:06:48.257 [37mStreamer Component ->[34m {"type":"ping","time":1754546808}
14:07:48.268 [37mStreamer Component ->[34m {"type":"ping","time":1754546868}
14:08:48.257 [37mStreamer Component ->[34m {"type":"ping","time":1754546928}
14:09:48.237 [37mStreamer Component ->[34m {"type":"ping","time":1754546988}
14:10:39.722 player 2 connection closed: 1001 - 
14:10:39.723 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"2"}
14:10:39.727 [37m[players] <-[32m {"type":"playerCount","count":0}
14:10:40.620 player 3 (::1) connected
14:10:40.621 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
14:10:40.621 [37m[players] <-[32m {"type":"playerCount","count":1}
14:10:40.623 [37m3 ->[34m {"type":"listStreamers"}
14:10:40.623 [37m3 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
14:10:40.653 [37m3 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
14:10:40.654 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"3","dataChannel":true,"sfu":false,"sendOffer":true}
14:10:40.703 [37mStreamer Component -> 3[36m {"type":"offer","playerId":3,"sdp":"v=0\r\no=- 1821098009868923358 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Ggav\r\na=ice-pwd:PYhVc1EYU4Vyj9vx4y4Uhr7v\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5A:04:B7:D0:D5:EF:07:1B:95:A0:A7:F0:2B:F4:6C:D3:B3:45:0B:48:CB:55:4E:43:6F:3E:23:FE:67:59:07:1F\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3423511091 204970937\r\na=ssrc:3423511091 cname:guqCBVWwH1dpzw5G\r\na=ssrc:3423511091 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:204970937 cname:guqCBVWwH1dpzw5G\r\na=ssrc:204970937 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Ggav\r\na=ice-pwd:PYhVc1EYU4Vyj9vx4y4Uhr7v\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5A:04:B7:D0:D5:EF:07:1B:95:A0:A7:F0:2B:F4:6C:D3:B3:45:0B:48:CB:55:4E:43:6F:3E:23:FE:67:59:07:1F\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:390840888 cname:guqCBVWwH1dpzw5G\r\na=ssrc:390840888 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Ggav\r\na=ice-pwd:PYhVc1EYU4Vyj9vx4y4Uhr7v\r\na=ice-options:trickle\r\na=fingerprint:sha-256 5A:04:B7:D0:D5:EF:07:1B:95:A0:A7:F0:2B:F4:6C:D3:B3:45:0B:48:CB:55:4E:43:6F:3E:23:FE:67:59:07:1F\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:10:40.737 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:47488247 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag Ggav network-id 1"}}
14:10:40.748 [37m3 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 6588807066014162846 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:QlNj\r\na=ice-pwd:1FLgI4z2H68Go0UonTK//ssx\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:01:1F:87:F7:00:14:DC:DA:CA:85:0B:41:AF:6B:8C:2F:04:F7:50:C1:84:08:B7:A6:81:54:91:23:1F:C0:69\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:QlNj\r\na=ice-pwd:1FLgI4z2H68Go0UonTK//ssx\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:01:1F:87:F7:00:14:DC:DA:CA:85:0B:41:AF:6B:8C:2F:04:F7:50:C1:84:08:B7:A6:81:54:91:23:1F:C0:69\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- cba75736-8e01-440f-a735-ca8324b85d11\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:730244923 cname:5fcnoPI63A7NZ6wy\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:QlNj\r\na=ice-pwd:1FLgI4z2H68Go0UonTK//ssx\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:01:1F:87:F7:00:14:DC:DA:CA:85:0B:41:AF:6B:8C:2F:04:F7:50:C1:84:08:B7:A6:81:54:91:23:1F:C0:69\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:10:40.751 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:730695278 1 udp 2122260223 ************ 65285 typ host generation 0 ufrag QlNj network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"QlNj"}}
14:10:40.752 [37m3 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3450088765 1 udp 2122194687 ************* 65286 typ host generation 0 ufrag QlNj network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"QlNj"}}
14:10:40.770 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4153024691 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag Ggav network-id 4"}}
14:10:40.804 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:47488247 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag Ggav network-id 1"}}
14:10:40.838 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4153024691 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag Ggav network-id 4"}}
14:10:40.871 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:47488247 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag Ggav network-id 1"}}
14:10:40.905 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4153024691 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag Ggav network-id 4"}}
14:10:40.939 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:4236134499 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag Ggav network-id 1"}}
14:10:40.973 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:153154599 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag Ggav network-id 4"}}
14:10:41.006 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:4236134499 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag Ggav network-id 1"}}
14:10:41.040 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:153154599 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag Ggav network-id 4"}}
14:10:41.074 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:4236134499 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag Ggav network-id 1"}}
14:10:41.107 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:153154599 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag Ggav network-id 4"}}
14:10:41.140 [37mStreamer Component -> 3[36m {"type":"iceCandidate","playerId":3,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3503823224 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag Ggav network-id 1"}}
14:10:48.269 [37mStreamer Component ->[34m {"type":"ping","time":1754547048}
14:11:48.241 [37mStreamer Component ->[34m {"type":"ping","time":1754547108}
14:12:48.242 [37mStreamer Component ->[34m {"type":"ping","time":1754547168}
14:13:48.264 [37mStreamer Component ->[34m {"type":"ping","time":1754547228}
14:14:48.278 [37mStreamer Component ->[34m {"type":"ping","time":1754547288}
14:15:37.753 player 3 connection closed: 1001 - 
14:15:37.755 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"3"}
14:15:37.755 [37m[players] <-[32m {"type":"playerCount","count":0}
14:15:38.480 player 4 (::1) connected
14:15:38.481 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
14:15:38.482 [37m[players] <-[32m {"type":"playerCount","count":1}
14:15:38.483 [37m4 ->[34m {"type":"listStreamers"}
14:15:38.483 [37m4 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
14:15:38.513 [37m4 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
14:15:38.514 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"4","dataChannel":true,"sfu":false,"sendOffer":true}
14:15:38.601 [37mStreamer Component -> 4[36m {"type":"offer","playerId":4,"sdp":"v=0\r\no=- 6291985973363944320 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Y9xd\r\na=ice-pwd:6GwbNRUuBJtW5gdgQfv5LPvY\r\na=ice-options:trickle\r\na=fingerprint:sha-256 95:F9:70:67:6A:34:53:B0:07:01:22:75:AE:0D:EE:50:C9:6F:71:3E:9C:71:0C:D3:11:79:89:23:50:FC:BD:D7\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1001776196 3895153586\r\na=ssrc:1001776196 cname:FSbwmkNUkZ9+o/jI\r\na=ssrc:1001776196 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3895153586 cname:FSbwmkNUkZ9+o/jI\r\na=ssrc:3895153586 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Y9xd\r\na=ice-pwd:6GwbNRUuBJtW5gdgQfv5LPvY\r\na=ice-options:trickle\r\na=fingerprint:sha-256 95:F9:70:67:6A:34:53:B0:07:01:22:75:AE:0D:EE:50:C9:6F:71:3E:9C:71:0C:D3:11:79:89:23:50:FC:BD:D7\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1557595818 cname:FSbwmkNUkZ9+o/jI\r\na=ssrc:1557595818 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Y9xd\r\na=ice-pwd:6GwbNRUuBJtW5gdgQfv5LPvY\r\na=ice-options:trickle\r\na=fingerprint:sha-256 95:F9:70:67:6A:34:53:B0:07:01:22:75:AE:0D:EE:50:C9:6F:71:3E:9C:71:0C:D3:11:79:89:23:50:FC:BD:D7\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:15:38.635 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2133080567 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag Y9xd network-id 1"}}
14:15:38.645 [37m4 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 5602009409047103164 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:FNhJ\r\na=ice-pwd:zVv2HTZwB/AZRplbXJ04MkyI\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C7:D1:8D:04:8D:39:F7:75:DA:5F:8E:46:18:53:82:AD:3D:C6:0B:91:EC:1E:D4:35:F1:A3:F7:F5:F2:77:DC:C8\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:FNhJ\r\na=ice-pwd:zVv2HTZwB/AZRplbXJ04MkyI\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C7:D1:8D:04:8D:39:F7:75:DA:5F:8E:46:18:53:82:AD:3D:C6:0B:91:EC:1E:D4:35:F1:A3:F7:F5:F2:77:DC:C8\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 37de2409-85e4-4af3-be42-6f0ac6a9e1d5\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2819056395 cname:cRtnqhpnadeS75Nt\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:FNhJ\r\na=ice-pwd:zVv2HTZwB/AZRplbXJ04MkyI\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C7:D1:8D:04:8D:39:F7:75:DA:5F:8E:46:18:53:82:AD:3D:C6:0B:91:EC:1E:D4:35:F1:A3:F7:F5:F2:77:DC:C8\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:15:38.647 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:711008115 1 udp 2122260223 ************ 59518 typ host generation 0 ufrag FNhJ network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"FNhJ"}}
14:15:38.647 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3427316768 1 udp 2122194687 ************* 59519 typ host generation 0 ufrag FNhJ network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"FNhJ"}}
14:15:38.669 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:777792160 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag Y9xd network-id 4"}}
14:15:38.703 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2133080567 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag Y9xd network-id 1"}}
14:15:38.737 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:777792160 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag Y9xd network-id 4"}}
14:15:38.770 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2133080567 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag Y9xd network-id 1"}}
14:15:38.804 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:777792160 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag Y9xd network-id 4"}}
14:15:38.838 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:32234351 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag Y9xd network-id 1"}}
14:15:38.872 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1351862328 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag Y9xd network-id 4"}}
14:15:38.906 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:32234351 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag Y9xd network-id 1"}}
14:15:38.939 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1351862328 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag Y9xd network-id 4"}}
14:15:38.973 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:32234351 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag Y9xd network-id 1"}}
14:15:39.006 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1351862328 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag Y9xd network-id 4"}}
14:15:39.040 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2116307921 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag Y9xd network-id 1"}}
14:15:48.283 [37mStreamer Component ->[34m {"type":"ping","time":1754547348}
14:16:48.283 [37mStreamer Component ->[34m {"type":"ping","time":1754547408}
14:16:57.401 player 4 connection closed: 1001 - 
14:16:57.403 [37mStreamer Component <-[32m {"type":"playerDisconnected","playerId":"4"}
14:16:57.406 [37m[players] <-[32m {"type":"playerCount","count":0}
14:17:00.277 player 5 (::1) connected
14:17:00.278 [37m5 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
14:17:00.278 [37m[players] <-[32m {"type":"playerCount","count":1}
14:17:00.280 [37m5 ->[34m {"type":"listStreamers"}
14:17:00.281 [37m5 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
14:17:00.325 [37m5 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
14:17:00.326 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"5","dataChannel":true,"sfu":false,"sendOffer":true}
14:17:00.369 [37mStreamer Component -> 5[36m {"type":"offer","playerId":5,"sdp":"v=0\r\no=- 4607568320069619824 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:ofAU\r\na=ice-pwd:oT0t9TxlfBg16u0yl/07a/I3\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CA:14:06:F1:34:CF:A4:27:6B:FD:1F:15:5A:4B:FF:3E:C4:C2:14:A9:DD:5E:49:AD:7B:42:F5:43:FF:0E:C1:F3\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2489935519 968815717\r\na=ssrc:2489935519 cname:RSB417yafvUPmPxb\r\na=ssrc:2489935519 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:968815717 cname:RSB417yafvUPmPxb\r\na=ssrc:968815717 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:ofAU\r\na=ice-pwd:oT0t9TxlfBg16u0yl/07a/I3\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CA:14:06:F1:34:CF:A4:27:6B:FD:1F:15:5A:4B:FF:3E:C4:C2:14:A9:DD:5E:49:AD:7B:42:F5:43:FF:0E:C1:F3\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3144395846 cname:RSB417yafvUPmPxb\r\na=ssrc:3144395846 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:ofAU\r\na=ice-pwd:oT0t9TxlfBg16u0yl/07a/I3\r\na=ice-options:trickle\r\na=fingerprint:sha-256 CA:14:06:F1:34:CF:A4:27:6B:FD:1F:15:5A:4B:FF:3E:C4:C2:14:A9:DD:5E:49:AD:7B:42:F5:43:FF:0E:C1:F3\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:17:00.402 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3994576435 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag ofAU network-id 1"}}
14:17:00.437 [37m5 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 1721093634985408170 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:fZZt\r\na=ice-pwd:BzLBR1QMe2HPi90twYLHd2kg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:50:43:05:8C:69:33:D2:DD:38:ED:A6:BE:7D:A0:C9:DF:66:39:0B:84:5A:CD:F1:14:80:93:EC:76:C5:C3:53\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:fZZt\r\na=ice-pwd:BzLBR1QMe2HPi90twYLHd2kg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:50:43:05:8C:69:33:D2:DD:38:ED:A6:BE:7D:A0:C9:DF:66:39:0B:84:5A:CD:F1:14:80:93:EC:76:C5:C3:53\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- f7383dcc-07ab-4e07-a2cc-d9896a869cb9\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1443200252 cname:qOYb/xw7xldoSOms\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:fZZt\r\na=ice-pwd:BzLBR1QMe2HPi90twYLHd2kg\r\na=ice-options:trickle\r\na=fingerprint:sha-256 36:50:43:05:8C:69:33:D2:DD:38:ED:A6:BE:7D:A0:C9:DF:66:39:0B:84:5A:CD:F1:14:80:93:EC:76:C5:C3:53\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
14:17:00.438 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:457628279 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag ofAU network-id 4"}}
14:17:00.439 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3321456278 1 udp 2122260223 ************ 58778 typ host generation 0 ufrag fZZt network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"fZZt"}}
14:17:00.439 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2400072921 1 udp 2122194687 ************* 58779 typ host generation 0 ufrag fZZt network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"fZZt"}}
14:17:00.470 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3994576435 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag ofAU network-id 1"}}
14:17:00.503 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:457628279 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag ofAU network-id 4"}}
14:17:00.537 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3994576435 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag ofAU network-id 1"}}
14:17:00.570 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:457628279 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag ofAU network-id 4"}}
14:17:00.604 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:280146599 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag ofAU network-id 1"}}
14:17:00.638 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3857452771 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag ofAU network-id 4"}}
14:17:00.672 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:280146599 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag ofAU network-id 1"}}
14:17:00.705 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3857452771 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag ofAU network-id 4"}}
14:17:00.739 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:280146599 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag ofAU network-id 1"}}
14:17:00.773 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3857452771 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag ofAU network-id 4"}}
14:17:00.807 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1008001980 1 udp 1686052607 ************** 49154 typ srflx raddr ************* rport 49154 generation 0 ufrag ofAU network-id 1"}}
14:17:48.279 [37mStreamer Component ->[34m {"type":"ping","time":1754547468}
14:18:48.240 [37mStreamer Component ->[34m {"type":"ping","time":1754547528}
14:19:48.249 [37mStreamer Component ->[34m {"type":"ping","time":1754547588}
14:20:48.291 [37mStreamer Component ->[34m {"type":"ping","time":1754547648}
14:21:48.234 [37mStreamer Component ->[34m {"type":"ping","time":1754547708}
14:22:48.242 [37mStreamer Component ->[34m {"type":"ping","time":1754547768}
14:23:48.243 [37mStreamer Component ->[34m {"type":"ping","time":1754547828}
14:24:48.268 [37mStreamer Component ->[34m {"type":"ping","time":1754547888}
14:25:49.840 streamer Streamer Component disconnected: 1006 - 
14:25:49.841 unsubscribing all players on Streamer Component
14:25:49.842 player 5 connection closed: 1005 - 
14:25:49.842 [37m[players] <-[32m {"type":"playerCount","count":0}
