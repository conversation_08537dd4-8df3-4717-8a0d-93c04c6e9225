16:36:01.874 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
16:36:01.921 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
16:36:01.922 Redirecting http->https
16:36:01.928 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
16:36:01.935 WebSocket listening for Streamer connections on :8888
16:36:01.936 WebSocket listening for SFU connections on :8889
16:36:01.936 WebSocket listening for Players connections on :80
16:36:01.937 设置服务器端视频播放HTTP API
16:36:01.938 Http listening on *: 80
16:36:01.938 Https listening on *: 443
16:37:09.668 Streamer connected: ::1
16:37:09.668 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
16:37:09.670 [37m::1 <-[32m {"type":"identify"}
16:37:10.274 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
16:37:10.275 Registered new streamer: Streamer Component
16:37:23.869 player 1 (::ffff:*************) connected
16:37:23.870 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
16:37:23.870 [37m[players] <-[32m {"type":"playerCount","count":1}
16:37:23.875 [37m1 ->[34m {"type":"listStreamers"}
16:37:23.876 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
16:37:23.892 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
16:37:23.893 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
16:37:23.983 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 6281204469900884935 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:P12W\r\na=ice-pwd:leSGfCnVTMG6u8G3+lFaeG2B\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:4F:9A:6E:54:40:40:B7:F2:27:C4:F8:9E:D4:0F:C9:1E:6C:9B:E8:83:8A:D1:88:8C:5F:8D:D0:F2:08:8D:10\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 169133883 1948636187\r\na=ssrc:169133883 cname:LQdcsypapqkbHKAQ\r\na=ssrc:169133883 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:1948636187 cname:LQdcsypapqkbHKAQ\r\na=ssrc:1948636187 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:P12W\r\na=ice-pwd:leSGfCnVTMG6u8G3+lFaeG2B\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:4F:9A:6E:54:40:40:B7:F2:27:C4:F8:9E:D4:0F:C9:1E:6C:9B:E8:83:8A:D1:88:8C:5F:8D:D0:F2:08:8D:10\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2112965513 cname:LQdcsypapqkbHKAQ\r\na=ssrc:2112965513 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:P12W\r\na=ice-pwd:leSGfCnVTMG6u8G3+lFaeG2B\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:4F:9A:6E:54:40:40:B7:F2:27:C4:F8:9E:D4:0F:C9:1E:6C:9B:E8:83:8A:D1:88:8C:5F:8D:D0:F2:08:8D:10\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
16:37:23.984 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:834106015 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag P12W network-id 1"}}
16:37:24.006 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 7412013518104520357 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:wFbC\r\na=ice-pwd:BZUrKTBt6SDZm3IMXJ6YJIj8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8E:9A:E2:01:DE:D1:B9:AD:E5:A0:30:B1:81:6F:22:23:33:8F:82:46:52:20:EF:5F:43:14:C1:30:E5:63:11:ED\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:wFbC\r\na=ice-pwd:BZUrKTBt6SDZm3IMXJ6YJIj8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8E:9A:E2:01:DE:D1:B9:AD:E5:A0:30:B1:81:6F:22:23:33:8F:82:46:52:20:EF:5F:43:14:C1:30:E5:63:11:ED\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 59f52dee-9921-4fac-9e44-a9ac514dcc40\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:607592494 cname:sjQJYSGVY/Q4EJeD\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:wFbC\r\na=ice-pwd:BZUrKTBt6SDZm3IMXJ6YJIj8\r\na=ice-options:trickle\r\na=fingerprint:sha-256 8E:9A:E2:01:DE:D1:B9:AD:E5:A0:30:B1:81:6F:22:23:33:8F:82:46:52:20:EF:5F:43:14:C1:30:E5:63:11:ED\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
16:37:24.006 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:504432773 1 udp 2122260223 ************* 59533 typ host generation 0 ufrag wFbC network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"wFbC"}}
16:37:24.007 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:688398081 1 udp 2122194687 ************ 59534 typ host generation 0 ufrag wFbC network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"wFbC"}}
16:37:24.007 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2684158909 1 udp 2122129151 *************** 59535 typ host generation 0 ufrag wFbC network-id 3","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"wFbC"}}
16:37:24.016 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3067241082 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag P12W network-id 4"}}
16:37:24.063 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:834106015 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag P12W network-id 1"}}
16:37:24.089 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3067241082 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag P12W network-id 4"}}
16:37:24.125 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:834106015 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag P12W network-id 1"}}
16:37:24.153 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3067241082 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag P12W network-id 4"}}
16:37:39.212 收到服务器端视频播放请求: undefined
16:37:39.214 处理服务器端视频播放请求失败: TypeError: Cannot destructure property 'videoPath' of 'req.body' as it is undefined.
    at H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\cirrus.js:1421:12
    at Layer.handle [as handle_request] (H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\express\lib\router\layer.js:95:5)
    at next (H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\express\lib\router\layer.js:95:5)
    at H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\express\lib\router\index.js:346:12)
    at next (H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\serve-static\index.js:75:16)
    at Layer.handle [as handle_request] (H:\cloud6\MomentumCloud\WebServer\SignallingWebServer\node_modules\express\lib\router\layer.js:95:5)
16:38:10.346 [37mStreamer Component ->[34m {"type":"ping","time":1754555890}
16:38:47.787 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
16:38:47.788 unsubscribing all players on Streamer Component
16:38:47.789 player 1 connection closed: 1005 - 
16:38:47.789 [37m[players] <-[32m {"type":"playerCount","count":0}
