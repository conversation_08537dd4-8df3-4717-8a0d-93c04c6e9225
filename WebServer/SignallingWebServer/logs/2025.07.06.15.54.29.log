15:54:29.075 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "**************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
15:54:29.114 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:**************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
15:54:29.116 Redirecting http->https
15:54:29.121 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
15:54:29.128 WebSocket listening for Streamer connections on :8888
15:54:29.128 WebSocket listening for SFU connections on :8889
15:54:29.129 WebSocket listening for Players connections on :80
15:54:29.130 Http listening on *: 80
15:54:29.130 Https listening on *: 443
15:56:06.087 Streamer connected: ::1
15:56:06.088 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:**************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:56:06.089 [37m::1 <-[32m {"type":"identify"}
15:56:06.549 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:56:06.550 Registered new streamer: Streamer Component
15:56:48.227 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
15:56:48.228 unsubscribing all players on Streamer Component
