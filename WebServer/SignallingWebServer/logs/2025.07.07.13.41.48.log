13:41:48.408 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "***************",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
13:41:48.454 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:***************:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
13:41:48.456 Redirecting http->https
13:41:48.462 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
13:41:48.470 WebSocket listening for Streamer connections on :8888
13:41:48.471 WebSocket listening for SFU connections on :8889
13:41:48.471 WebSocket listening for Players connections on :80
13:41:48.473 Http listening on *: 80
13:41:48.473 Https listening on *: 443
13:42:42.692 Streamer connected: ::1
13:42:42.693 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:42:42.694 [37m::1 <-[32m {"type":"identify"}
13:42:43.195 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
13:42:43.196 Registered new streamer: Streamer Component
13:43:47.471 [37mStreamer Component ->[34m {"type":"ping","time":1754545427}
13:44:17.431 player 1 (::1) connected
13:44:17.432 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:44:17.432 [37m[players] <-[32m {"type":"playerCount","count":1}
13:44:17.434 [37m1 ->[34m {"type":"listStreamers"}
13:44:17.435 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
13:44:17.464 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
13:44:17.465 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
13:44:17.562 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 7727370181834206804 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Ulo8\r\na=ice-pwd:0OQNMM8MwCjd8nlldifM0i+t\r\na=ice-options:trickle\r\na=fingerprint:sha-256 07:FB:7C:3E:BC:9C:DB:EB:C0:E3:04:E6:1D:11:D3:F8:0E:4F:2A:8A:8E:A3:78:71:EB:C9:F0:18:3C:96:BF:F7\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 4115303632 3398151806\r\na=ssrc:4115303632 cname:b8D9qNiG4E0eSXbm\r\na=ssrc:4115303632 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3398151806 cname:b8D9qNiG4E0eSXbm\r\na=ssrc:3398151806 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:Ulo8\r\na=ice-pwd:0OQNMM8MwCjd8nlldifM0i+t\r\na=ice-options:trickle\r\na=fingerprint:sha-256 07:FB:7C:3E:BC:9C:DB:EB:C0:E3:04:E6:1D:11:D3:F8:0E:4F:2A:8A:8E:A3:78:71:EB:C9:F0:18:3C:96:BF:F7\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:392295907 cname:b8D9qNiG4E0eSXbm\r\na=ssrc:392295907 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:Ulo8\r\na=ice-pwd:0OQNMM8MwCjd8nlldifM0i+t\r\na=ice-options:trickle\r\na=fingerprint:sha-256 07:FB:7C:3E:BC:9C:DB:EB:C0:E3:04:E6:1D:11:D3:F8:0E:4F:2A:8A:8E:A3:78:71:EB:C9:F0:18:3C:96:BF:F7\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:44:17.564 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3360605941 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag Ulo8 network-id 1"}}
13:44:17.595 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2570507682 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag Ulo8 network-id 4"}}
13:44:17.607 [37m1 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 8382594831133615887 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:NWiQ\r\na=ice-pwd:N7USIgK8kFCchUrSjj8W5+gP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4E:50:AE:B6:DF:E7:71:FD:07:FF:EB:57:19:27:00:25:0D:2B:E6:BF:97:6E:94:D5:52:08:4F:48:A1:43:30:20\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:NWiQ\r\na=ice-pwd:N7USIgK8kFCchUrSjj8W5+gP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4E:50:AE:B6:DF:E7:71:FD:07:FF:EB:57:19:27:00:25:0D:2B:E6:BF:97:6E:94:D5:52:08:4F:48:A1:43:30:20\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- d17dfc92-81ae-40a4-be47-53ac261c2321\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3990149792 cname:WBSKFIajQGAG2fkT\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:NWiQ\r\na=ice-pwd:N7USIgK8kFCchUrSjj8W5+gP\r\na=ice-options:trickle\r\na=fingerprint:sha-256 4E:50:AE:B6:DF:E7:71:FD:07:FF:EB:57:19:27:00:25:0D:2B:E6:BF:97:6E:94:D5:52:08:4F:48:A1:43:30:20\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:44:17.609 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3259189374 1 udp 2122260223 ************ 57522 typ host generation 0 ufrag NWiQ network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"NWiQ"}}
13:44:17.609 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2293504561 1 udp 2122194687 ************* 57523 typ host generation 0 ufrag NWiQ network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"NWiQ"}}
13:44:17.629 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3360605941 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag Ulo8 network-id 1"}}
13:44:17.662 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2570507682 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag Ulo8 network-id 4"}}
13:44:17.696 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3360605941 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag Ulo8 network-id 1"}}
13:44:17.730 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2570507682 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag Ulo8 network-id 4"}}
13:44:17.764 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3061916781 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag Ulo8 network-id 1"}}
13:44:17.798 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3891869498 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag Ulo8 network-id 4"}}
13:44:17.831 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3061916781 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag Ulo8 network-id 1"}}
13:44:17.865 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3891869498 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag Ulo8 network-id 4"}}
13:44:17.899 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3061916781 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag Ulo8 network-id 1"}}
13:44:17.932 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3891869498 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag Ulo8 network-id 4"}}
13:44:17.966 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3377378515 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag Ulo8 network-id 1"}}
13:44:21.833 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
13:44:21.834 unsubscribing all players on Streamer Component
13:44:21.835 player 1 connection closed: 1005 - 
13:44:21.835 [37m[players] <-[32m {"type":"playerCount","count":0}
13:45:05.268 Streamer connected: ::1
13:45:05.269 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:45:05.269 [37m::1 <-[32m {"type":"identify"}
13:45:05.765 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
13:45:05.765 Registered new streamer: Streamer Component
13:45:43.438 player 2 (::1) connected
13:45:43.439 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:***************:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:45:43.448 [37m[players] <-[32m {"type":"playerCount","count":1}
13:45:43.537 [37m2 ->[34m {"type":"listStreamers"}
13:45:43.540 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
13:45:43.599 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
13:45:43.604 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
13:45:43.732 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 8846906170295813242 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:65UA\r\na=ice-pwd:KeiUypDg/bEtMbZK6pRbfkF0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F0:E1:78:B0:19:D6:A7:66:68:E2:15:0C:1B:79:43:DE:28:4A:31:A9:D7:52:9C:56:41:58:CC:1A:B1:6E:46:9A\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3427820628 3736492335\r\na=ssrc:3427820628 cname:Y25OvQSjEphXS8Ld\r\na=ssrc:3427820628 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3736492335 cname:Y25OvQSjEphXS8Ld\r\na=ssrc:3736492335 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:65UA\r\na=ice-pwd:KeiUypDg/bEtMbZK6pRbfkF0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F0:E1:78:B0:19:D6:A7:66:68:E2:15:0C:1B:79:43:DE:28:4A:31:A9:D7:52:9C:56:41:58:CC:1A:B1:6E:46:9A\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:300917799 cname:Y25OvQSjEphXS8Ld\r\na=ssrc:300917799 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:65UA\r\na=ice-pwd:KeiUypDg/bEtMbZK6pRbfkF0\r\na=ice-options:trickle\r\na=fingerprint:sha-256 F0:E1:78:B0:19:D6:A7:66:68:E2:15:0C:1B:79:43:DE:28:4A:31:A9:D7:52:9C:56:41:58:CC:1A:B1:6E:46:9A\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:45:43.767 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3160829846 1 udp 2122260223 ************* 49152 typ host generation 0 ufrag 65UA network-id 1"}}
13:45:43.804 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1228461010 1 udp 2122194687 ************ 49152 typ host generation 0 ufrag 65UA network-id 4"}}
13:45:43.825 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3160829846 1 udp 2122260223 ************* 49153 typ host generation 0 ufrag 65UA network-id 1"}}
13:45:43.851 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1228461010 1 udp 2122194687 ************ 49153 typ host generation 0 ufrag 65UA network-id 4"}}
13:45:43.864 [37m2 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 5979355379076518388 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:u1cK\r\na=ice-pwd:AzJV6q4qvw+QViF4tgrkK53f\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2E:11:D0:E8:90:4F:C9:3C:24:79:5E:83:FB:36:95:81:19:85:27:7F:01:A5:31:22:DB:9E:B9:DA:76:C8:AB:9E\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:u1cK\r\na=ice-pwd:AzJV6q4qvw+QViF4tgrkK53f\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2E:11:D0:E8:90:4F:C9:3C:24:79:5E:83:FB:36:95:81:19:85:27:7F:01:A5:31:22:DB:9E:B9:DA:76:C8:AB:9E\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 20d5e69f-8dd4-4ba6-81de-d2038ba2a391\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1529699983 cname:UMjhwauGqZxt6oxv\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:u1cK\r\na=ice-pwd:AzJV6q4qvw+QViF4tgrkK53f\r\na=ice-options:trickle\r\na=fingerprint:sha-256 2E:11:D0:E8:90:4F:C9:3C:24:79:5E:83:FB:36:95:81:19:85:27:7F:01:A5:31:22:DB:9E:B9:DA:76:C8:AB:9E\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:45:43.872 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:658366211 1 udp 2122260223 ************ 53223 typ host generation 0 ufrag u1cK network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"u1cK"}}
13:45:43.874 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1841990988 1 udp 2122194687 ************* 53224 typ host generation 0 ufrag u1cK network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"u1cK"}}
13:45:43.882 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3160829846 1 udp 2122260223 ************* 49154 typ host generation 0 ufrag 65UA network-id 1"}}
13:45:43.889 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1228461010 1 udp 2122194687 ************ 49154 typ host generation 0 ufrag 65UA network-id 4"}}
13:45:43.923 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1120713474 1 tcp 1518280447 ************* 49152 typ host tcptype passive generation 0 ufrag 65UA network-id 1"}}
13:45:43.957 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3079800646 1 tcp 1518214911 ************ 49152 typ host tcptype passive generation 0 ufrag 65UA network-id 4"}}
13:45:43.992 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1120713474 1 tcp 1518280447 ************* 49153 typ host tcptype passive generation 0 ufrag 65UA network-id 1"}}
13:45:44.023 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3079800646 1 tcp 1518214911 ************ 49153 typ host tcptype passive generation 0 ufrag 65UA network-id 4"}}
13:45:44.056 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1120713474 1 tcp 1518280447 ************* 49154 typ host tcptype passive generation 0 ufrag 65UA network-id 1"}}
13:45:44.090 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3079800646 1 tcp 1518214911 ************ 49154 typ host tcptype passive generation 0 ufrag 65UA network-id 4"}}
13:45:44.124 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1852500505 1 udp 1686052607 ************** 49153 typ srflx raddr ************* rport 49153 generation 0 ufrag 65UA network-id 1"}}
13:45:44.169 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1852500505 1 udp 1686052607 ************** 49152 typ srflx raddr ************* rport 49152 generation 0 ufrag 65UA network-id 1"}}
13:45:44.201 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1852500505 1 udp 1686052607 ************** 49154 typ srflx raddr ************* rport 49154 generation 0 ufrag 65UA network-id 1"}}
13:46:09.590 [37mStreamer Component ->[34m {"type":"ping","time":1754545569}
13:47:13.917 [37mStreamer Component ->[34m {"type":"ping","time":1754545633}
13:48:16.688 [37mStreamer Component ->[34m {"type":"ping","time":1754545696}
13:49:16.681 [37mStreamer Component ->[34m {"type":"ping","time":1754545756}
13:50:16.674 [37mStreamer Component ->[34m {"type":"ping","time":1754545816}
13:51:17.904 [37mStreamer Component ->[34m {"type":"ping","time":1754545877}
13:52:19.076 [37mStreamer Component ->[34m {"type":"ping","time":1754545939}
13:53:19.069 [37mStreamer Component ->[34m {"type":"ping","time":1754545999}
13:54:19.080 [37mStreamer Component ->[34m {"type":"ping","time":1754546059}
13:55:19.102 [37mStreamer Component ->[34m {"type":"ping","time":1754546119}
13:55:27.462 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
13:55:27.462 unsubscribing all players on Streamer Component
13:55:27.463 player 2 connection closed: 1005 - 
13:55:27.463 [37m[players] <-[32m {"type":"playerCount","count":0}
