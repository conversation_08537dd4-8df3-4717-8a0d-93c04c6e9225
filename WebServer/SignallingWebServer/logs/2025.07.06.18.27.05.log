18:27:05.301 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
18:27:05.342 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
18:27:05.343 Redirecting http->https
18:27:05.349 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
18:27:05.356 WebSocket listening for Streamer connections on :8888
18:27:05.357 WebSocket listening for SFU connections on :8889
18:27:05.358 WebSocket listening for Players connections on :80
18:27:05.359 Http listening on *: 80
18:27:05.359 Https listening on *: 443
18:27:34.016 Streamer connected: ::1
18:27:34.017 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:27:34.018 [37m::1 <-[32m {"type":"identify"}
18:27:34.517 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:27:34.518 Registered new streamer: Streamer Component
18:27:47.310 player 1 (::1) connected
18:27:47.311 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:27:47.311 [37m[players] <-[32m {"type":"playerCount","count":1}
18:27:47.313 [37m1 ->[34m {"type":"listStreamers"}
18:27:47.313 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
18:27:47.335 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
18:27:47.336 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
18:27:47.440 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 1322598712209862994 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:aK8l\r\na=ice-pwd:2bT+9l/xz3Ody0bnkOAHj7Sc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 98:33:87:50:72:AF:E0:6C:EF:6D:1E:9F:95:C8:01:C4:FD:4C:3F:B1:69:80:CD:41:C9:1B:9B:00:14:BB:32:00\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3727968682 854923293\r\na=ssrc:3727968682 cname:8XjtddxkxlMV9Tlv\r\na=ssrc:3727968682 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:854923293 cname:8XjtddxkxlMV9Tlv\r\na=ssrc:854923293 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:aK8l\r\na=ice-pwd:2bT+9l/xz3Ody0bnkOAHj7Sc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 98:33:87:50:72:AF:E0:6C:EF:6D:1E:9F:95:C8:01:C4:FD:4C:3F:B1:69:80:CD:41:C9:1B:9B:00:14:BB:32:00\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:2443954208 cname:8XjtddxkxlMV9Tlv\r\na=ssrc:2443954208 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:aK8l\r\na=ice-pwd:2bT+9l/xz3Ody0bnkOAHj7Sc\r\na=ice-options:trickle\r\na=fingerprint:sha-256 98:33:87:50:72:AF:E0:6C:EF:6D:1E:9F:95:C8:01:C4:FD:4C:3F:B1:69:80:CD:41:C9:1B:9B:00:14:BB:32:00\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
18:27:47.442 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3451853680 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag aK8l network-id 1"}}
18:27:47.473 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3929204739 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag aK8l network-id 2"}}
18:27:47.476 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 4716551421681326799 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:ac+J\r\na=ice-pwd:U2cIHJLd4aFejrv03usTZAgY\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1B:45:A6:F0:C0:60:09:F7:B4:EB:8A:8E:CC:F6:36:12:44:68:94:5F:4B:E1:0A:DB:E8:D5:8F:A5:A7:30:72:05\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:ac+J\r\na=ice-pwd:U2cIHJLd4aFejrv03usTZAgY\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1B:45:A6:F0:C0:60:09:F7:B4:EB:8A:8E:CC:F6:36:12:44:68:94:5F:4B:E1:0A:DB:E8:D5:8F:A5:A7:30:72:05\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 4e0daaf9-39b1-4aa9-bdc2-aa3fe5a099ab\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:1053907503 cname:sID3+k41img8ZNFP\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:ac+J\r\na=ice-pwd:U2cIHJLd4aFejrv03usTZAgY\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1B:45:A6:F0:C0:60:09:F7:B4:EB:8A:8E:CC:F6:36:12:44:68:94:5F:4B:E1:0A:DB:E8:D5:8F:A5:A7:30:72:05\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
18:27:47.477 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3458756586 1 udp 2122260223 ************ 49271 typ host generation 0 ufrag ac+J network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"ac+J"}}
18:27:47.478 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:1511389059 1 udp 2122194687 ************* 49272 typ host generation 0 ufrag ac+J network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"ac+J"}}
18:27:47.507 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3451853680 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag aK8l network-id 1"}}
18:27:47.540 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3929204739 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag aK8l network-id 2"}}
18:27:47.574 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3451853680 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag aK8l network-id 1"}}
18:27:47.608 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3929204739 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag aK8l network-id 2"}}
18:27:47.641 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:857067492 1 tcp 1518280447 ************ 49152 typ host tcptype passive generation 0 ufrag aK8l network-id 1"}}
18:27:47.675 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:345521303 1 tcp 1518214911 ************* 49152 typ host tcptype passive generation 0 ufrag aK8l network-id 2"}}
18:27:47.708 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:857067492 1 tcp 1518280447 ************ 49153 typ host tcptype passive generation 0 ufrag aK8l network-id 1"}}
18:27:47.741 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:345521303 1 tcp 1518214911 ************* 49153 typ host tcptype passive generation 0 ufrag aK8l network-id 2"}}
18:27:47.775 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:857067492 1 tcp 1518280447 ************ 49154 typ host tcptype passive generation 0 ufrag aK8l network-id 1"}}
18:27:47.809 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:345521303 1 tcp 1518214911 ************* 49154 typ host tcptype passive generation 0 ufrag aK8l network-id 2"}}
18:28:34.622 [37mStreamer Component ->[34m {"type":"ping","time":1754476114}
18:29:34.648 [37mStreamer Component ->[34m {"type":"ping","time":1754476174}
18:30:34.607 [37mStreamer Component ->[34m {"type":"ping","time":1754476234}
18:31:34.650 [37mStreamer Component ->[34m {"type":"ping","time":1754476294}
18:31:40.536 streamer Streamer Component disconnected: 1006 - 
18:31:40.538 unsubscribing all players on Streamer Component
18:31:40.543 player 1 connection closed: 1005 - 
18:31:40.544 [37m[players] <-[32m {"type":"playerCount","count":0}
18:31:42.650 Streamer connected: ::1
18:31:42.651 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:31:42.651 [37m::1 <-[32m {"type":"identify"}
18:31:42.781 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:31:42.782 Registered new streamer: Streamer Component
18:32:42.735 [37mStreamer Component ->[34m {"type":"ping","time":1754476362}
18:33:42.685 [37mStreamer Component ->[34m {"type":"ping","time":1754476422}
18:34:42.715 [37mStreamer Component ->[34m {"type":"ping","time":1754476482}
18:34:49.497 streamer Streamer Component disconnected: 1006 - 
18:34:49.498 unsubscribing all players on Streamer Component
18:34:51.538 Streamer connected: ::1
18:34:51.539 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:34:51.540 [37m::1 <-[32m {"type":"identify"}
18:34:51.672 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:34:51.672 Registered new streamer: Streamer Component
18:35:51.579 [37mStreamer Component ->[34m {"type":"ping","time":1754476551}
18:36:51.575 [37mStreamer Component ->[34m {"type":"ping","time":1754476611}
18:37:23.907 streamer Streamer Component disconnected: 1006 - 
18:37:23.907 unsubscribing all players on Streamer Component
18:37:25.965 Streamer connected: ::1
18:37:25.967 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:37:25.967 [37m::1 <-[32m {"type":"identify"}
18:37:26.097 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:37:26.098 Registered new streamer: Streamer Component
18:38:25.973 [37mStreamer Component ->[34m {"type":"ping","time":1754476705}
18:38:36.625 streamer Streamer Component disconnected: 1006 - 
18:38:36.626 unsubscribing all players on Streamer Component
18:38:39.712 Streamer connected: ::1
18:38:39.713 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
18:38:39.713 [37m::1 <-[32m {"type":"identify"}
18:38:39.810 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
18:38:39.811 Registered new streamer: Streamer Component
18:39:39.747 [37mStreamer Component ->[34m {"type":"ping","time":1754476779}
18:40:39.786 [37mStreamer Component ->[34m {"type":"ping","time":1754476839}
18:41:39.758 [37mStreamer Component ->[34m {"type":"ping","time":1754476899}
18:42:39.743 [37mStreamer Component ->[34m {"type":"ping","time":1754476959}
18:43:39.770 [37mStreamer Component ->[34m {"type":"ping","time":1754477019}
18:44:39.772 [37mStreamer Component ->[34m {"type":"ping","time":1754477079}
18:45:39.769 [37mStreamer Component ->[34m {"type":"ping","time":1754477139}
18:46:39.784 [37mStreamer Component ->[34m {"type":"ping","time":1754477199}
18:47:39.749 [37mStreamer Component ->[34m {"type":"ping","time":1754477259}
18:48:39.787 [37mStreamer Component ->[34m {"type":"ping","time":1754477319}
18:49:39.758 [37mStreamer Component ->[34m {"type":"ping","time":1754477379}
18:50:39.759 [37mStreamer Component ->[34m {"type":"ping","time":1754477439}
18:51:39.745 [37mStreamer Component ->[34m {"type":"ping","time":1754477499}
18:52:34.975 streamer Streamer Component disconnected: 1006 - 
18:52:34.976 unsubscribing all players on Streamer Component
