13:06:16.138 Config: {
	"UseFrontend": false,
	"UseMatchmaker": false,
	"UseHTTPS": true,
	"HTTPSCertFile": "./certificates/client-cert.pem",
	"HTTPSKeyFile": "./certificates/client-key.pem",
	"LogToFile": true,
	"LogVerbose": true,
	"HomepageFile": "viewer.html",
	"AdditionalRoutes": {},
	"EnableWebserver": true,
	"MatchmakerAddress": "",
	"MatchmakerPort": 9999,
	"PublicIp": "127.0.0.1",
	"HttpPort": 80,
	"HttpsPort": 443,
	"StreamerPort": 8888,
	"SFUPort": 8889,
	"MaxPlayerCount": -1,
	"DisableSSLCert": true,
	"peerConnectionOptions": "{ \"iceServers\": [{\"urls\": [\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"], \"username\": \"PixelStreamingUser\", \"credential\": \"AnotherTURNintheroad\"}] }"
}
13:06:16.751 peerConnectionOptions = {"iceServers":[{"urls":["stun:stun.l.google.com:19302","turn:127.0.0.1:19303"],"username":"PixelStreamingUser","credential":"AnotherTURNintheroad"}]}
13:06:16.752 Redirecting http->https
13:06:16.787 Running Cirrus - The Pixel Streaming reference implementation signalling server for Unreal Engine 5.4.
13:06:16.908 WebSocket listening for Streamer connections on :8888
13:06:16.909 WebSocket listening for SFU connections on :8889
13:06:16.910 WebSocket listening for Players connections on :80
13:06:16.911 Http listening on *: 80
13:06:16.912 Https listening on *: 443
13:17:39.528 Streamer connected: ::1
13:17:39.529 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:17:39.530 [37m::1 <-[32m {"type":"identify"}
13:17:40.923 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
13:17:40.923 Registered new streamer: Streamer Component
13:18:43.829 [37mStreamer Component ->[34m {"type":"ping","time":1754457523}
13:19:27.195 player 1 (::1) connected
13:19:27.195 [37m1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:19:27.195 [37m[players] <-[32m {"type":"playerCount","count":1}
13:19:27.196 [37m1 ->[34m {"type":"listStreamers"}
13:19:27.196 [37m1 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
13:19:27.297 [37m1 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
13:19:27.298 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"1","dataChannel":true,"sfu":false,"sendOffer":true}
13:19:27.413 [37mStreamer Component -> 1[36m {"type":"offer","playerId":1,"sdp":"v=0\r\no=- 7821235412181509627 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:dkC2\r\na=ice-pwd:xtwoGCM4DIbyE3y5GosetLzB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1C:5E:69:AF:A8:0B:D8:E3:A6:25:CA:B5:D5:DC:50:43:0E:DD:F0:B8:BF:60:F0:59:BA:9C:78:2A:F7:26:AC:E1\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 1805282093 2904669768\r\na=ssrc:1805282093 cname:nlKRT2LQmUC/ENau\r\na=ssrc:1805282093 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:2904669768 cname:nlKRT2LQmUC/ENau\r\na=ssrc:2904669768 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:dkC2\r\na=ice-pwd:xtwoGCM4DIbyE3y5GosetLzB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1C:5E:69:AF:A8:0B:D8:E3:A6:25:CA:B5:D5:DC:50:43:0E:DD:F0:B8:BF:60:F0:59:BA:9C:78:2A:F7:26:AC:E1\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:4037143779 cname:nlKRT2LQmUC/ENau\r\na=ssrc:4037143779 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:dkC2\r\na=ice-pwd:xtwoGCM4DIbyE3y5GosetLzB\r\na=ice-options:trickle\r\na=fingerprint:sha-256 1C:5E:69:AF:A8:0B:D8:E3:A6:25:CA:B5:D5:DC:50:43:0E:DD:F0:B8:BF:60:F0:59:BA:9C:78:2A:F7:26:AC:E1\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:19:27.443 [37m1 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 8523793030539812418 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:swTH\r\na=ice-pwd:o2QAN4aHKd3oixCiOvlRv7Ub\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6B:17:7E:16:B0:35:A4:1C:20:86:72:8A:EB:87:04:CE:D9:A6:23:03:1B:35:83:C3:60:C6:B7:F2:EC:63:BE:6E\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:swTH\r\na=ice-pwd:o2QAN4aHKd3oixCiOvlRv7Ub\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6B:17:7E:16:B0:35:A4:1C:20:86:72:8A:EB:87:04:CE:D9:A6:23:03:1B:35:83:C3:60:C6:B7:F2:EC:63:BE:6E\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- f6fdf4ed-9af5-45ac-a964-03238b26cb4f\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:3795338509 cname:Mgf5aB54E0SNxYUJ\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:swTH\r\na=ice-pwd:o2QAN4aHKd3oixCiOvlRv7Ub\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6B:17:7E:16:B0:35:A4:1C:20:86:72:8A:EB:87:04:CE:D9:A6:23:03:1B:35:83:C3:60:C6:B7:F2:EC:63:BE:6E\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:19:27.446 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:296601205 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag dkC2 network-id 1"}}
13:19:27.448 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:817052663 1 udp 2122260223 ************ 64017 typ host generation 0 ufrag swTH network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"swTH"}}
13:19:27.448 [37m1 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2760811422 1 udp 2122194687 ************* 64018 typ host generation 0 ufrag swTH network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"swTH"}}
13:19:27.480 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:908080390 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag dkC2 network-id 2"}}
13:19:27.514 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:296601205 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag dkC2 network-id 1"}}
13:19:27.548 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:908080390 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag dkC2 network-id 2"}}
13:19:27.582 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:296601205 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag dkC2 network-id 1"}}
13:19:27.616 [37mStreamer Component -> 1[36m {"type":"iceCandidate","playerId":1,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:908080390 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag dkC2 network-id 2"}}
13:19:43.791 [37mStreamer Component ->[34m {"type":"ping","time":1754457583}
13:20:43.836 [37mStreamer Component ->[34m {"type":"ping","time":1754457643}
13:21:43.836 [37mStreamer Component ->[34m {"type":"ping","time":1754457703}
13:22:43.838 [37mStreamer Component ->[34m {"type":"ping","time":1754457763}
13:23:43.848 [37mStreamer Component ->[34m {"type":"ping","time":1754457823}
13:24:43.797 [37mStreamer Component ->[34m {"type":"ping","time":1754457883}
13:25:43.815 [37mStreamer Component ->[34m {"type":"ping","time":1754457943}
13:26:43.792 [37mStreamer Component ->[34m {"type":"ping","time":1754458003}
13:27:43.847 [37mStreamer Component ->[34m {"type":"ping","time":1754458063}
13:28:43.803 [37mStreamer Component ->[34m {"type":"ping","time":1754458123}
13:29:43.815 [37mStreamer Component ->[34m {"type":"ping","time":1754458183}
13:30:43.812 [37mStreamer Component ->[34m {"type":"ping","time":1754458243}
13:31:43.810 [37mStreamer Component ->[34m {"type":"ping","time":1754458303}
13:32:43.843 [37mStreamer Component ->[34m {"type":"ping","time":1754458363}
13:33:43.812 [37mStreamer Component ->[34m {"type":"ping","time":1754458423}
13:34:43.848 [37mStreamer Component ->[34m {"type":"ping","time":1754458483}
13:35:43.821 [37mStreamer Component ->[34m {"type":"ping","time":1754458543}
13:36:43.793 [37mStreamer Component ->[34m {"type":"ping","time":1754458603}
13:37:43.793 [37mStreamer Component ->[34m {"type":"ping","time":1754458663}
13:38:43.822 [37mStreamer Component ->[34m {"type":"ping","time":1754458723}
13:39:43.794 [37mStreamer Component ->[34m {"type":"ping","time":1754458783}
13:40:43.829 [37mStreamer Component ->[34m {"type":"ping","time":1754458843}
13:41:43.806 [37mStreamer Component ->[34m {"type":"ping","time":1754458903}
13:42:43.780 [37mStreamer Component ->[34m {"type":"ping","time":1754458963}
13:43:43.790 [37mStreamer Component ->[34m {"type":"ping","time":1754459023}
13:44:43.832 [37mStreamer Component ->[34m {"type":"ping","time":1754459083}
13:45:43.835 [37mStreamer Component ->[34m {"type":"ping","time":1754459143}
13:46:43.844 [37mStreamer Component ->[34m {"type":"ping","time":1754459203}
13:47:43.777 [37mStreamer Component ->[34m {"type":"ping","time":1754459263}
13:48:43.825 [37mStreamer Component ->[34m {"type":"ping","time":1754459323}
13:49:43.825 [37mStreamer Component ->[34m {"type":"ping","time":1754459383}
13:50:43.782 [37mStreamer Component ->[34m {"type":"ping","time":1754459443}
13:51:43.814 [37mStreamer Component ->[34m {"type":"ping","time":1754459503}
13:52:37.820 streamer Streamer Component disconnected: 1006 - 
13:52:37.820 unsubscribing all players on Streamer Component
13:52:37.822 player 1 connection closed: 1005 - 
13:52:37.825 [37m[players] <-[32m {"type":"playerCount","count":0}
13:55:11.392 Streamer connected: ::1
13:55:11.392 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:55:11.393 [37m::1 <-[32m {"type":"identify"}
13:55:11.896 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
13:55:11.897 Registered new streamer: Streamer Component
13:56:11.908 player 2 (::1) connected
13:56:11.910 [37m2 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
13:56:11.911 [37m[players] <-[32m {"type":"playerCount","count":1}
13:56:11.913 [37m2 ->[34m {"type":"listStreamers"}
13:56:11.915 [37m2 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
13:56:11.941 [37m2 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
13:56:11.942 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"2","dataChannel":true,"sfu":false,"sendOffer":true}
13:56:11.966 [37mStreamer Component ->[34m {"type":"ping","time":1754459771}
13:56:12.035 [37mStreamer Component -> 2[36m {"type":"offer","playerId":2,"sdp":"v=0\r\no=- 3976629819260340976 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:4tpn\r\na=ice-pwd:LvcqDhthYid3d0JG3z43p33w\r\na=ice-options:trickle\r\na=fingerprint:sha-256 89:E7:78:90:FC:57:AD:E7:39:CE:F9:07:6D:5C:E5:88:30:F6:FB:7F:9E:45:80:9D:EE:CA:DE:57:8A:2F:C4:FF\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 3130932795 4047660779\r\na=ssrc:3130932795 cname:Jcc216M50qmyDZ+G\r\na=ssrc:3130932795 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:4047660779 cname:Jcc216M50qmyDZ+G\r\na=ssrc:4047660779 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:4tpn\r\na=ice-pwd:LvcqDhthYid3d0JG3z43p33w\r\na=ice-options:trickle\r\na=fingerprint:sha-256 89:E7:78:90:FC:57:AD:E7:39:CE:F9:07:6D:5C:E5:88:30:F6:FB:7F:9E:45:80:9D:EE:CA:DE:57:8A:2F:C4:FF\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:1464164486 cname:Jcc216M50qmyDZ+G\r\na=ssrc:1464164486 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:4tpn\r\na=ice-pwd:LvcqDhthYid3d0JG3z43p33w\r\na=ice-options:trickle\r\na=fingerprint:sha-256 89:E7:78:90:FC:57:AD:E7:39:CE:F9:07:6D:5C:E5:88:30:F6:FB:7F:9E:45:80:9D:EE:CA:DE:57:8A:2F:C4:FF\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:56:12.065 [37m2 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 2992694891827306690 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:qL4M\r\na=ice-pwd:OIk1/IKgvAsYBetnw6sC584/\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BC:1E:4B:98:CA:2F:42:E1:70:CC:54:2A:76:E1:35:E4:88:86:61:9B:F3:11:D8:EA:DB:E8:74:0C:D8:66:CC:36\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:qL4M\r\na=ice-pwd:OIk1/IKgvAsYBetnw6sC584/\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BC:1E:4B:98:CA:2F:42:E1:70:CC:54:2A:76:E1:35:E4:88:86:61:9B:F3:11:D8:EA:DB:E8:74:0C:D8:66:CC:36\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- a3fc1350-94ae-45fb-bbd9-5d46ce01201a\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2050574182 cname:qrP0HDnMervLW0sU\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:qL4M\r\na=ice-pwd:OIk1/IKgvAsYBetnw6sC584/\r\na=ice-options:trickle\r\na=fingerprint:sha-256 BC:1E:4B:98:CA:2F:42:E1:70:CC:54:2A:76:E1:35:E4:88:86:61:9B:F3:11:D8:EA:DB:E8:74:0C:D8:66:CC:36\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
13:56:12.066 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3081506679 1 udp 2122260223 ************ 51688 typ host generation 0 ufrag qL4M network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"qL4M"}}
13:56:12.066 [37m2 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:2203511571 1 udp 2122194687 ************* 51689 typ host generation 0 ufrag qL4M network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"qL4M"}}
13:56:12.067 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2723620079 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag 4tpn network-id 1"}}
13:56:12.101 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:2245711772 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag 4tpn network-id 2"}}
13:56:12.135 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2723620079 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag 4tpn network-id 1"}}
13:56:12.169 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:2245711772 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag 4tpn network-id 2"}}
13:56:12.203 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2723620079 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag 4tpn network-id 1"}}
13:56:12.237 [37mStreamer Component -> 2[36m {"type":"iceCandidate","playerId":2,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:2245711772 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag 4tpn network-id 2"}}
13:57:11.930 [37mStreamer Component ->[34m {"type":"ping","time":1754459831}
13:58:11.936 [37mStreamer Component ->[34m {"type":"ping","time":1754459891}
13:59:11.969 [37mStreamer Component ->[34m {"type":"ping","time":1754459951}
14:00:11.967 [37mStreamer Component ->[34m {"type":"ping","time":1754460011}
14:01:11.961 [37mStreamer Component ->[34m {"type":"ping","time":1754460071}
14:02:11.957 [37mStreamer Component ->[34m {"type":"ping","time":1754460131}
14:03:11.955 [37mStreamer Component ->[34m {"type":"ping","time":1754460191}
14:04:11.976 [37mStreamer Component ->[34m {"type":"ping","time":1754460251}
14:05:11.939 [37mStreamer Component ->[34m {"type":"ping","time":1754460311}
14:06:11.951 [37mStreamer Component ->[34m {"type":"ping","time":1754460371}
14:07:11.955 [37mStreamer Component ->[34m {"type":"ping","time":1754460431}
14:08:11.947 [37mStreamer Component ->[34m {"type":"ping","time":1754460491}
14:09:11.945 [37mStreamer Component ->[34m {"type":"ping","time":1754460551}
14:10:11.974 [37mStreamer Component ->[34m {"type":"ping","time":1754460611}
14:11:11.943 [37mStreamer Component ->[34m {"type":"ping","time":1754460671}
14:12:11.937 [37mStreamer Component ->[34m {"type":"ping","time":1754460731}
14:13:11.933 [37mStreamer Component ->[34m {"type":"ping","time":1754460791}
14:14:11.960 [37mStreamer Component ->[34m {"type":"ping","time":1754460851}
14:15:11.926 [37mStreamer Component ->[34m {"type":"ping","time":1754460911}
14:16:11.956 [37mStreamer Component ->[34m {"type":"ping","time":1754460971}
14:17:11.952 [37mStreamer Component ->[34m {"type":"ping","time":1754461031}
14:18:11.947 [37mStreamer Component ->[34m {"type":"ping","time":1754461091}
14:19:11.940 [37mStreamer Component ->[34m {"type":"ping","time":1754461151}
14:20:11.959 [37mStreamer Component ->[34m {"type":"ping","time":1754461211}
14:21:11.958 [37mStreamer Component ->[34m {"type":"ping","time":1754461271}
14:22:11.962 [37mStreamer Component ->[34m {"type":"ping","time":1754461331}
14:23:11.953 [37mStreamer Component ->[34m {"type":"ping","time":1754461391}
14:24:11.956 [37mStreamer Component ->[34m {"type":"ping","time":1754461451}
14:25:11.958 [37mStreamer Component ->[34m {"type":"ping","time":1754461511}
14:26:11.955 [37mStreamer Component ->[34m {"type":"ping","time":1754461571}
14:27:11.954 [37mStreamer Component ->[34m {"type":"ping","time":1754461631}
14:28:11.953 [37mStreamer Component ->[34m {"type":"ping","time":1754461691}
14:29:11.925 [37mStreamer Component ->[34m {"type":"ping","time":1754461751}
14:30:11.957 [37mStreamer Component ->[34m {"type":"ping","time":1754461811}
14:31:11.963 [37mStreamer Component ->[34m {"type":"ping","time":1754461871}
14:32:11.971 [37mStreamer Component ->[34m {"type":"ping","time":1754461931}
14:33:11.935 [37mStreamer Component ->[34m {"type":"ping","time":1754461991}
14:34:11.965 [37mStreamer Component ->[34m {"type":"ping","time":1754462051}
14:35:11.935 [37mStreamer Component ->[34m {"type":"ping","time":1754462111}
14:36:11.920 [37mStreamer Component ->[34m {"type":"ping","time":1754462171}
14:37:11.949 [37mStreamer Component ->[34m {"type":"ping","time":1754462231}
14:38:11.953 [37mStreamer Component ->[34m {"type":"ping","time":1754462291}
14:39:11.949 [37mStreamer Component ->[34m {"type":"ping","time":1754462351}
14:40:11.919 [37mStreamer Component ->[34m {"type":"ping","time":1754462411}
14:41:11.926 [37mStreamer Component ->[34m {"type":"ping","time":1754462471}
14:42:11.925 [37mStreamer Component ->[34m {"type":"ping","time":1754462531}
14:43:11.930 [37mStreamer Component ->[34m {"type":"ping","time":1754462591}
14:44:11.930 [37mStreamer Component ->[34m {"type":"ping","time":1754462651}
14:45:11.966 [37mStreamer Component ->[34m {"type":"ping","time":1754462711}
14:46:11.925 [37mStreamer Component ->[34m {"type":"ping","time":1754462771}
14:47:11.955 [37mStreamer Component ->[34m {"type":"ping","time":1754462831}
14:48:11.955 [37mStreamer Component ->[34m {"type":"ping","time":1754462891}
14:49:11.961 [37mStreamer Component ->[34m {"type":"ping","time":1754462951}
14:50:12.386 [37mStreamer Component ->[34m {"type":"ping","time":1754463012}
14:51:12.401 [37mStreamer Component ->[34m {"type":"ping","time":1754463072}
14:52:12.404 [37mStreamer Component ->[34m {"type":"ping","time":1754463132}
14:53:12.382 [37mStreamer Component ->[34m {"type":"ping","time":1754463192}
14:54:12.375 [37mStreamer Component ->[34m {"type":"ping","time":1754463252}
14:55:12.372 [37mStreamer Component ->[34m {"type":"ping","time":1754463312}
14:56:12.388 [37mStreamer Component ->[34m {"type":"ping","time":1754463372}
14:57:12.394 [37mStreamer Component ->[34m {"type":"ping","time":1754463432}
14:58:12.403 [37mStreamer Component ->[34m {"type":"ping","time":1754463492}
14:59:12.398 [37mStreamer Component ->[34m {"type":"ping","time":1754463552}
15:00:16.073 streamer Streamer Component disconnected: 1006 - 
15:00:16.074 unsubscribing all players on Streamer Component
15:00:16.075 player 2 connection closed: 1005 - 
15:00:16.075 [37m[players] <-[32m {"type":"playerCount","count":0}
15:15:12.515 player 3 (::1) connected
15:15:12.516 [37m3 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:15:12.516 [37m[players] <-[32m {"type":"playerCount","count":1}
15:15:12.516 [37m3 ->[34m {"type":"listStreamers"}
15:15:12.517 [37m3 <-[32m {"type":"streamerList","ids":[]}
15:16:27.152 Streamer connected: ::1
15:16:27.153 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:16:27.153 [37m::1 <-[32m {"type":"identify"}
15:16:27.647 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:16:27.648 Registered new streamer: Streamer Component
15:16:53.300 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
15:16:53.300 unsubscribing all players on Streamer Component
15:18:10.901 Streamer connected: ::1
15:18:10.902 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:18:10.903 [37m::1 <-[32m {"type":"identify"}
15:18:11.430 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:18:11.431 Registered new streamer: Streamer Component
15:19:11.432 [37mStreamer Component ->[34m {"type":"ping","time":1754464751}
15:19:21.948 streamer Streamer Component disconnected: 1006 - 
15:19:21.949 unsubscribing all players on Streamer Component
15:21:11.163 Streamer connected: ::1
15:21:11.164 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:21:11.164 [37m::1 <-[32m {"type":"identify"}
15:21:11.657 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:21:11.658 Registered new streamer: Streamer Component
15:21:52.931 player 3 connection closed: 1005 - 
15:21:52.932 [37m[players] <-[32m {"type":"playerCount","count":0}
15:21:55.795 player 4 (::1) connected
15:21:55.796 [37m4 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:21:55.796 [37m[players] <-[32m {"type":"playerCount","count":1}
15:21:55.796 [37m4 ->[34m {"type":"listStreamers"}
15:21:55.797 [37m4 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
15:21:55.824 [37m4 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
15:21:55.825 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"4","dataChannel":true,"sfu":false,"sendOffer":true}
15:21:55.927 [37mStreamer Component -> 4[36m {"type":"offer","playerId":4,"sdp":"v=0\r\no=- 6091531310658181946 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:kYEH\r\na=ice-pwd:6Wvy33J5wedMRaAs3+73hwiV\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6A:66:C2:0C:E8:30:39:58:B6:FF:21:B2:62:D6:45:2B:7F:CF:F3:43:D2:B8:AB:73:16:EF:94:7B:71:6B:E6:37\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 778155573 3772021759\r\na=ssrc:778155573 cname:JY8b/z6JlWSnsseo\r\na=ssrc:778155573 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3772021759 cname:JY8b/z6JlWSnsseo\r\na=ssrc:3772021759 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:kYEH\r\na=ice-pwd:6Wvy33J5wedMRaAs3+73hwiV\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6A:66:C2:0C:E8:30:39:58:B6:FF:21:B2:62:D6:45:2B:7F:CF:F3:43:D2:B8:AB:73:16:EF:94:7B:71:6B:E6:37\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:3752055081 cname:JY8b/z6JlWSnsseo\r\na=ssrc:3752055081 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:kYEH\r\na=ice-pwd:6Wvy33J5wedMRaAs3+73hwiV\r\na=ice-options:trickle\r\na=fingerprint:sha-256 6A:66:C2:0C:E8:30:39:58:B6:FF:21:B2:62:D6:45:2B:7F:CF:F3:43:D2:B8:AB:73:16:EF:94:7B:71:6B:E6:37\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:21:55.956 [37m4 -> Streamer Component[36m {"type":"answer","sdp":"v=0\r\no=- 6051326974959025000 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:cqIo\r\na=ice-pwd:1LLsbUPQ4hHUaBT6X9ef0dXz\r\na=ice-options:trickle\r\na=fingerprint:sha-256 51:73:75:98:FD:B2:08:95:EA:4A:65:AF:BD:0B:01:F7:13:9B:5E:7C:97:B9:3E:77:1D:78:8E:92:49:8C:B5:EA\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:cqIo\r\na=ice-pwd:1LLsbUPQ4hHUaBT6X9ef0dXz\r\na=ice-options:trickle\r\na=fingerprint:sha-256 51:73:75:98:FD:B2:08:95:EA:4A:65:AF:BD:0B:01:F7:13:9B:5E:7C:97:B9:3E:77:1D:78:8E:92:49:8C:B5:EA\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- f1766d08-c738-4d6e-9448-47a7e4c18869\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:2878343081 cname:lDt09SHlc3CRjXsx\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:cqIo\r\na=ice-pwd:1LLsbUPQ4hHUaBT6X9ef0dXz\r\na=ice-options:trickle\r\na=fingerprint:sha-256 51:73:75:98:FD:B2:08:95:EA:4A:65:AF:BD:0B:01:F7:13:9B:5E:7C:97:B9:3E:77:1D:78:8E:92:49:8C:B5:EA\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:21:55.957 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:754729703 1 udp 2122260223 ************ 56297 typ host generation 0 ufrag cqIo network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"cqIo"}}
15:21:55.958 [37m4 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:403089027 1 udp 2122194687 ************* 56298 typ host generation 0 ufrag cqIo network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"cqIo"}}
15:21:55.960 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3960067931 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag kYEH network-id 1"}}
15:21:55.994 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3414434856 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag kYEH network-id 2"}}
15:21:56.028 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3960067931 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag kYEH network-id 1"}}
15:21:56.062 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3414434856 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag kYEH network-id 2"}}
15:21:56.096 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3960067931 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag kYEH network-id 1"}}
15:21:56.130 [37mStreamer Component -> 4[36m {"type":"iceCandidate","playerId":4,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3414434856 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag kYEH network-id 2"}}
15:22:11.757 [37mStreamer Component ->[34m {"type":"ping","time":1754464931}
15:23:11.783 [37mStreamer Component ->[34m {"type":"ping","time":1754464991}
15:23:19.285 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
15:23:19.285 unsubscribing all players on Streamer Component
15:23:19.286 player 4 connection closed: 1005 - 
15:23:19.287 [37m[players] <-[32m {"type":"playerCount","count":0}
15:23:43.986 Streamer connected: ::1
15:23:43.987 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:23:43.987 [37m::1 <-[32m {"type":"identify"}
15:23:44.491 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:23:44.492 Registered new streamer: Streamer Component
15:24:00.320 player 5 (::1) connected
15:24:00.321 [37m5 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:24:00.322 [37m[players] <-[32m {"type":"playerCount","count":1}
15:24:00.323 [37m5 ->[34m {"type":"listStreamers"}
15:24:00.323 [37m5 <-[32m {"type":"streamerList","ids":["Streamer Component"]}
15:24:00.350 [37m5 ->[34m {"type":"subscribe","streamerId":"Streamer Component"}
15:24:00.350 [37mStreamer Component <-[32m {"type":"playerConnected","playerId":"5","dataChannel":true,"sfu":false,"sendOffer":true}
15:24:00.492 [37mStreamer Component -> 5[36m {"type":"offer","playerId":5,"sdp":"v=0\r\no=- 253529764443677464 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS pixelstreaming_av_stream_id\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:shcx\r\na=ice-pwd:ePKzs2wIb5EF+Q6vPw7d1IOo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:A6:1B:24:0B:91:80:68:81:E1:6E:2D:A4:38:E1:68:F0:50:3F:03:6B:F0:89:A5:E2:06:E1:BF:BF:19:5D:8A\r\na=setup:actpass\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=sendonly\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\na=ssrc-group:FID 2599123650 3204061308\r\na=ssrc:2599123650 cname:dra4iyHLTLmwzQcM\r\na=ssrc:2599123650 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\na=ssrc:3204061308 cname:dra4iyHLTLmwzQcM\r\na=ssrc:3204061308 msid:pixelstreaming_av_stream_id pixelstreaming_video_track_label\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:shcx\r\na=ice-pwd:ePKzs2wIb5EF+Q6vPw7d1IOo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:A6:1B:24:0B:91:80:68:81:E1:6E:2D:A4:38:E1:68:F0:50:3F:03:6B:F0:89:A5:E2:06:E1:BF:BF:19:5D:8A\r\na=setup:actpass\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;maxplaybackrate=48000;minptime=3;sprop-stereo=1;stereo=1;usedtx=0;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=maxptime:120\r\na=ptime:20\r\na=ssrc:138495926 cname:dra4iyHLTLmwzQcM\r\na=ssrc:138495926 msid:pixelstreaming_av_stream_id pixelstreaming_audio_track_label\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:shcx\r\na=ice-pwd:ePKzs2wIb5EF+Q6vPw7d1IOo\r\na=ice-options:trickle\r\na=fingerprint:sha-256 C8:A6:1B:24:0B:91:80:68:81:E1:6E:2D:A4:38:E1:68:F0:50:3F:03:6B:F0:89:A5:E2:06:E1:BF:BF:19:5D:8A\r\na=setup:actpass\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:24:00.521 [37m5 -> Streamer Component[36m {"type":"answer","minBitrate":100000,"maxBitrate":100000000,"sdp":"v=0\r\no=- 761423181350192190 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\na=group:BUNDLE 0 1 2\r\na=extmap-allow-mixed\r\na=msid-semantic: WMS\r\nm=video 9 UDP/TLS/RTP/SAVPF 35 36 96 97 98\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:aezz\r\na=ice-pwd:hj3z87W2UPIYtTNqPCjXoH5O\r\na=ice-options:trickle\r\na=fingerprint:sha-256 82:2E:03:8F:F2:28:FB:F2:ED:AC:41:6E:BB:47:6B:8D:E5:5D:C8:75:13:7B:88:F6:75:12:BF:3D:F1:39:38:FD\r\na=setup:active\r\na=mid:0\r\na=extmap:1 urn:ietf:params:rtp-hdrext:toffset\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:3 urn:3gpp:video-orientation\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:5 http://www.webrtc.org/experiments/rtp-hdrext/playout-delay\r\na=extmap:6 http://www.webrtc.org/experiments/rtp-hdrext/video-content-type\r\na=extmap:7 http://www.webrtc.org/experiments/rtp-hdrext/video-timing\r\na=extmap:8 http://www.webrtc.org/experiments/rtp-hdrext/color-space\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=extmap:10 urn:ietf:params:rtp-hdrext:sdes:rtp-stream-id\r\na=extmap:11 urn:ietf:params:rtp-hdrext:sdes:repaired-rtp-stream-id\r\na=recvonly\r\na=rtcp-mux\r\na=rtcp-rsize\r\na=rtpmap:35 AV1/90000\r\na=rtcp-fb:35 goog-remb\r\na=rtcp-fb:35 transport-cc\r\na=rtcp-fb:35 ccm fir\r\na=rtcp-fb:35 nack\r\na=rtcp-fb:35 nack pli\r\na=fmtp:35 level-idx=5;profile=0;tier=0\r\na=rtpmap:36 rtx/90000\r\na=fmtp:36 apt=35\r\na=rtpmap:96 red/90000\r\na=rtpmap:97 rtx/90000\r\na=fmtp:97 apt=96\r\na=rtpmap:98 ulpfec/90000\r\nm=audio 9 UDP/TLS/RTP/SAVPF 111 63 110\r\nc=IN IP4 0.0.0.0\r\na=rtcp:9 IN IP4 0.0.0.0\r\na=ice-ufrag:aezz\r\na=ice-pwd:hj3z87W2UPIYtTNqPCjXoH5O\r\na=ice-options:trickle\r\na=fingerprint:sha-256 82:2E:03:8F:F2:28:FB:F2:ED:AC:41:6E:BB:47:6B:8D:E5:5D:C8:75:13:7B:88:F6:75:12:BF:3D:F1:39:38:FD\r\na=setup:active\r\na=mid:1\r\na=extmap:14 urn:ietf:params:rtp-hdrext:ssrc-audio-level\r\na=extmap:2 http://www.webrtc.org/experiments/rtp-hdrext/abs-send-time\r\na=extmap:4 http://www.ietf.org/id/draft-holmer-rmcat-transport-wide-cc-extensions-01\r\na=extmap:9 urn:ietf:params:rtp-hdrext:sdes:mid\r\na=sendrecv\r\na=msid:- 7d6f61fd-211c-4ba5-a664-28f9180b4b72\r\na=rtcp-mux\r\na=rtpmap:111 opus/48000/2\r\na=rtcp-fb:111 transport-cc\r\na=fmtp:111 maxaveragebitrate=510000;minptime=10;sprop-maxcapturerate=48000;stereo=1;useinbandfec=1\r\na=rtpmap:63 red/48000/2\r\na=fmtp:63 111/111\r\na=rtpmap:110 telephone-event/48000\r\na=ssrc:752366392 cname:DCyaPOM1vpitc1qR\r\nm=application 9 UDP/DTLS/SCTP webrtc-datachannel\r\nc=IN IP4 0.0.0.0\r\na=ice-ufrag:aezz\r\na=ice-pwd:hj3z87W2UPIYtTNqPCjXoH5O\r\na=ice-options:trickle\r\na=fingerprint:sha-256 82:2E:03:8F:F2:28:FB:F2:ED:AC:41:6E:BB:47:6B:8D:E5:5D:C8:75:13:7B:88:F6:75:12:BF:3D:F1:39:38:FD\r\na=setup:active\r\na=mid:2\r\na=sctp-port:5000\r\na=max-message-size:262144\r\n"}
15:24:00.523 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3495496990 1 udp 2122260223 ************ 50761 typ host generation 0 ufrag aezz network-id 1","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"aezz"}}
15:24:00.524 [37m5 -> Streamer Component[36m {"type":"iceCandidate","candidate":{"candidate":"candidate:3835947386 1 udp 2122194687 ************* 50762 typ host generation 0 ufrag aezz network-id 2","sdpMid":"0","sdpMLineIndex":0,"usernameFragment":"aezz"}}
15:24:00.525 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:3870599609 1 udp 2122260223 ************ 49152 typ host generation 0 ufrag shcx network-id 1"}}
15:24:00.559 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"0","sdpMLineIndex":0,"candidate":"candidate:1762029768 1 udp 2122194687 ************* 49152 typ host generation 0 ufrag shcx network-id 2"}}
15:24:00.593 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:3870599609 1 udp 2122260223 ************ 49153 typ host generation 0 ufrag shcx network-id 1"}}
15:24:00.627 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"1","sdpMLineIndex":1,"candidate":"candidate:1762029768 1 udp 2122194687 ************* 49153 typ host generation 0 ufrag shcx network-id 2"}}
15:24:00.661 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:3870599609 1 udp 2122260223 ************ 49154 typ host generation 0 ufrag shcx network-id 1"}}
15:24:00.695 [37mStreamer Component -> 5[36m {"type":"iceCandidate","playerId":5,"candidate":{"sdpMid":"2","sdpMLineIndex":2,"candidate":"candidate:1762029768 1 udp 2122194687 ************* 49154 typ host generation 0 ufrag shcx network-id 2"}}
15:24:44.555 [37mStreamer Component ->[34m {"type":"ping","time":1754465084}
15:25:44.591 [37mStreamer Component ->[34m {"type":"ping","time":1754465144}
15:26:44.528 [37mStreamer Component ->[34m {"type":"ping","time":1754465204}
15:27:44.538 [37mStreamer Component ->[34m {"type":"ping","time":1754465264}
15:28:44.586 [37mStreamer Component ->[34m {"type":"ping","time":1754465324}
15:29:44.584 [37mStreamer Component ->[34m {"type":"ping","time":1754465384}
15:30:04.347 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
15:30:04.348 unsubscribing all players on Streamer Component
15:30:04.348 player 5 connection closed: 1005 - 
15:30:04.351 [37m[players] <-[32m {"type":"playerCount","count":0}
15:32:01.986 Streamer connected: ::1
15:32:01.987 [37m::1 <-[32m "{\"type\":\"config\",\"peerConnectionOptions\":{\"iceServers\":[{\"urls\":[\"stun:stun.l.google.com:19302\",\"turn:127.0.0.1:19303\"],\"username\":\"PixelStreamingUser\",\"credential\":\"AnotherTURNintheroad\"}]}}"
15:32:01.987 [37m::1 <-[32m {"type":"identify"}
15:32:02.514 [37m::1 ->[34m {"type":"endpointId","id":"Streamer Component"}
15:32:02.515 Registered new streamer: Streamer Component
15:32:20.370 streamer Streamer Component disconnected: 1000 - Streamed application is shutting down
15:32:20.371 unsubscribing all players on Streamer Component
