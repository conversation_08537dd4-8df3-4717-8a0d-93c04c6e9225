const { app, BrowserWindow, screen, ipcMain } = require('electron');
const path = require('path');
const WebSocket = require('ws');
const fs = require('fs');

// 强制允许有声自动播放 - 这是关键！
app.commandLine.appendSwitch('autoplay-policy', 'no-user-gesture-required');
app.commandLine.appendSwitch('disable-features', 'MediaSessionAPI');

// 完全禁用GPU，使用软件渲染
app.commandLine.appendSwitch('disable-gpu');
app.commandLine.appendSwitch('disable-gpu-compositing');
app.commandLine.appendSwitch('disable-gpu-sandbox');
app.commandLine.appendSwitch('disable-software-rasterizer');
app.commandLine.appendSwitch('disable-gpu-process-crash-limit');
app.disableHardwareAcceleration();

let mainWindow = null;
let videoWindow = null;
let wsServer = null;

// 加载视频播放器配置
function loadVideoPlayerConfig() {
    try {
        const configPath = path.resolve(__dirname, '../../Renderer/VirtualHuman/Binaries/Win64/video-player-config.json');
        console.log('尝试加载配置文件:', configPath);

        if (fs.existsSync(configPath)) {
            const configData = fs.readFileSync(configPath, 'utf8');
            const config = JSON.parse(configData);
            console.log('配置文件加载成功:', config.videoWindow);
            return config.videoWindow;
        } else {
            console.log('配置文件不存在，使用默认配置');
        }
    } catch (error) {
        console.error('加载配置文件失败:', error);
    }

    // 默认配置
    return {
        size: {
            width: 1200,
            height: 675
        },
        defaultPosition: {
            offsetFromRight: 0,
            offsetFromTop: 0
        }
    };
}

// 创建WebSocket服务器监听来自主应用的消息
function createWebSocketServer() {
    wsServer = new WebSocket.Server({ port: 8765 });
    
    wsServer.on('connection', (ws) => {
        console.log('主应用已连接到视频播放器');
        
        ws.on('message', (message) => {
            try {
                const data = JSON.parse(message);
                console.log('收到消息:', data);
                
                if (data.type === 'PLAY_VIDEO') {
                    playVideoOnSecondScreen(data.videoPath);
                }
            } catch (error) {
                console.error('解析消息失败:', error);
            }
        });
        
        ws.on('close', () => {
            console.log('主应用断开连接');
        });
    });
    
    console.log('WebSocket Server Started on Port 8765');
}

// 在第二屏幕播放视频
function playVideoOnSecondScreen(videoPath) {
    console.log('Playing video on second screen:', videoPath);

    // 构建完整的视频文件路径 - 修正为SignallingWebServer目录
    const fullVideoPath = path.resolve(__dirname, '../SignallingWebServer/public', videoPath);
    console.log('Full video path:', fullVideoPath);

    // 检查文件是否存在
    const fs = require('fs');
    let actualVideoPath = fullVideoPath;

    if (!fs.existsSync(fullVideoPath)) {
        console.error('Video file not found:', fullVideoPath);
        console.log('Using test video instead...');
        // 使用在线测试视频
        actualVideoPath = 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4';
    }
    
    // 获取所有显示器
    const displays = screen.getAllDisplays();
    const primaryDisplay = screen.getPrimaryDisplay();
    console.log('Detected displays:', displays.length);
    console.log('Primary display:', primaryDisplay.bounds);

    // 显示所有显示器的详细信息
    displays.forEach((display, index) => {
        console.log(`Display ${index}:`, {
            bounds: display.bounds,
            workArea: display.workArea,
            scaleFactor: display.scaleFactor,
            isPrimary: display.bounds.x === primaryDisplay.bounds.x && display.bounds.y === primaryDisplay.bounds.y
        });
    });

    let targetDisplay = primaryDisplay; // 默认主屏幕

    // 如果有多个显示器，找到非主显示器
    if (displays.length > 1) {
        // 找到第一个非主显示器（正确的逻辑）
        targetDisplay = displays.find(display =>
            display.bounds.x !== primaryDisplay.bounds.x ||
            display.bounds.y !== primaryDisplay.bounds.y
        );

        if (targetDisplay) {
            console.log('Found secondary display:', targetDisplay.bounds);
        } else {
            // 如果找不到，使用第一个显示器（通常是非主显示器）
            targetDisplay = displays[0];
            console.log('Using first display as secondary:', targetDisplay.bounds);
        }
    } else {
        console.log('Only one display, using primary display');
    }
    
    // 关闭已存在的视频窗口
    if (videoWindow && !videoWindow.isDestroyed()) {
        videoWindow.close();
    }
    
    // 加载配置文件
    const config = loadVideoPlayerConfig();

    // 计算视频窗口位置（目标显示器的右上角）
    // 使用workArea而不是bounds，workArea考虑了任务栏等
    const displayArea = targetDisplay.workArea;

    // 使用配置文件中的窗口尺寸
    let windowWidth = config.size.width;
    let windowHeight = config.size.height;

    // 使用配置文件中的边距设置
    const rightMargin = config.defaultPosition.offsetFromRight - windowWidth;  // 计算实际边距
    const topMargin = config.defaultPosition.offsetFromTop;

    console.log('Using config file window sizing:');
    console.log('- Config width:', config.size.width);
    console.log('- Config height:', config.size.height);
    console.log('- Final window width:', windowWidth);
    console.log('- Final window height:', windowHeight);
    console.log('- Right margin:', rightMargin);
    console.log('- Top margin:', topMargin);

    // 确保使用目标显示器的坐标系，根据配置计算位置
    const x = targetDisplay.bounds.x + targetDisplay.bounds.width - config.defaultPosition.offsetFromRight;
    const y = targetDisplay.bounds.y + topMargin;

    console.log('Window position calculation:');
    console.log('- Target display bounds:', targetDisplay.bounds);
    console.log('- Window size:', windowWidth, 'x', windowHeight);
    console.log('- Right margin:', rightMargin, 'Top margin:', topMargin);
    console.log('- Calculated position:', x, ',', y);
    console.log('- Window right edge will be at:', x + windowWidth);
    console.log('- Display right edge is at:', targetDisplay.bounds.x + targetDisplay.bounds.width);
    console.log('- Perfect fit:', (x + windowWidth) === (targetDisplay.bounds.x + targetDisplay.bounds.width));
    
    // 创建视频窗口
    videoWindow = new BrowserWindow({
        width: windowWidth,
        height: windowHeight,
        x: x,
        y: y,
        frame: false, // 无边框
        alwaysOnTop: true, // 置顶
        resizable: false,
        show: false, // 先隐藏，加载完成后显示
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false, // 允许加载本地视频文件
            enableRemoteModule: true,
            backgroundThrottling: false // 防止后台限制
        }
    });
    
    // 加载视频播放页面，使用实际路径
    const videoUrl = `file://${__dirname}/video_player.html?video=${encodeURIComponent(actualVideoPath)}`;
    videoWindow.loadURL(videoUrl);
    console.log('Loading video URL:', videoUrl);

    // 页面加载完成后显示窗口
    videoWindow.webContents.once('did-finish-load', () => {
        console.log('视频页面加载完成，显示窗口');
        videoWindow.show();
    });

    // 处理页面加载错误
    videoWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        console.error('页面加载失败:', errorCode, errorDescription);
    });

    // 开发模式下打开开发者工具
    if (process.argv.includes('--dev')) {
        videoWindow.webContents.openDevTools();
    }
    
    // 窗口关闭时清理
    videoWindow.on('closed', () => {
        videoWindow = null;
        console.log('视频窗口已关闭');
    });
    
    console.log(`视频窗口已在位置 (${x}, ${y}) 打开`);
}

// 应用准备就绪
app.whenReady().then(() => {
    console.log('Electron App Started');
    
    // 创建主窗口（隐藏的控制窗口）
    mainWindow = new BrowserWindow({
        width: 400,
        height: 300,
        show: false, // 隐藏主窗口
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false
        }
    });
    
    // 创建WebSocket服务器
    createWebSocketServer();
    
    console.log('Video Player Ready, Waiting for Commands...');
});

// 所有窗口关闭时退出应用
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        if (wsServer) {
            wsServer.close();
        }
        app.quit();
    }
});

// macOS特殊处理
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        // 重新创建主窗口
    }
});

// IPC消息处理
ipcMain.on('video-ended', () => {
    console.log('视频播放完毕');

    // 通知所有连接的客户端视频已结束
    if (wsServer && wsServer.clients) {
        wsServer.clients.forEach(client => {
            if (client.readyState === 1) { // WebSocket.OPEN
                try {
                    client.send(JSON.stringify({
                        type: 'VIDEO_ENDED',
                        timestamp: Date.now()
                    }));
                    console.log('已发送视频结束通知给客户端');
                } catch (error) {
                    console.error('发送视频结束通知失败:', error);
                }
            }
        });
    }

    if (videoWindow && !videoWindow.isDestroyed()) {
        setTimeout(() => {
            videoWindow.close();
        }, 3000);
    }
});

ipcMain.on('close-video', () => {
    console.log('手动关闭视频');

    // 通知所有连接的客户端视频已结束（手动关闭）
    if (wsServer && wsServer.clients) {
        wsServer.clients.forEach(client => {
            if (client.readyState === 1) { // WebSocket.OPEN
                try {
                    client.send(JSON.stringify({
                        type: 'VIDEO_ENDED',
                        reason: 'manual_close',
                        timestamp: Date.now()
                    }));
                    console.log('已发送视频手动关闭通知给客户端');
                } catch (error) {
                    console.error('发送视频关闭通知失败:', error);
                }
            }
        });
    }

    if (videoWindow && !videoWindow.isDestroyed()) {
        videoWindow.close();
    }
});
