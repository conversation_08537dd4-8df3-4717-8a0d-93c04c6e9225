# 🎬 视频播放器配置文档

## 📋 概述

本配置系统允许您通过修改JSON配置文件来自定义视频播放器的外观和行为，无需修改代码。

## 📁 文件位置

- **配置文件**: `Renderer/VirtualHuman/Binaries/Win64/video-player-config.json`
- **相对于项目根目录**: `../../Binaries/Win64/video-player-config.json`

## 🎨 配置项说明

### 视频播放器样式 (`videoPlayer`)

#### 嵌入式容器 (`embeddedContainer`)
用于在页面内显示的小型视频播放器：

```json
{
  "position": "fixed",        // 定位方式：fixed(固定), absolute(绝对), relative(相对)
  "top": "20px",             // 距离顶部距离
  "right": "20px",           // 距离右侧距离
  "zIndex": 1000,            // 层级（数值越大越在上层）
  "width": "320px",          // 容器宽度
  "height": "240px",         // 容器高度
  "background": "rgba(0,0,0,0.8)",  // 背景色（支持透明度）
  "borderRadius": "8px",     // 圆角大小
  "padding": "10px",         // 内边距
  "boxShadow": "0 4px 8px rgba(0,0,0,0.3)"  // 阴影效果
}
```

#### 浮动容器 (`floatingContainer`)
用于全屏或大尺寸视频播放：

```json
{
  "position": "fixed",
  "top": "20px",
  "right": "20px", 
  "width": "400px",          // 更大的宽度
  "height": "300px",         // 更大的高度
  "zIndex": 10000,           // 更高的层级
  "background": "#000",      // 纯黑背景
  "borderRadius": "8px",
  "boxShadow": "0 8px 32px rgba(0, 0, 0, 0.5)",  // 更强的阴影
  "overflow": "hidden",      // 隐藏溢出内容
  "border": "2px solid #333" // 边框
}
```

#### 视频元素 (`video`)
视频本身的样式：

```json
{
  "width": "100%",           // 宽度占满容器
  "height": "100%",          // 高度占满容器
  "objectFit": "contain"     // 保持比例适应容器
}
```

#### 关闭按钮 (`closeButton`)
右上角的关闭按钮：

```json
{
  "position": "absolute",    // 绝对定位
  "top": "5px",             // 距离容器顶部
  "right": "5px",           // 距离容器右侧
  "width": "30px",          // 按钮宽度
  "height": "30px",         // 按钮高度
  "background": "rgba(0, 0, 0, 0.7)",  // 半透明黑色背景
  "color": "white",         // 文字颜色
  "border": "none",         // 无边框
  "borderRadius": "50%",    // 圆形按钮
  "cursor": "pointer",      // 鼠标悬停显示手型
  "fontSize": "18px",       // 字体大小
  "zIndex": 10001          // 确保在最上层
}
```

#### 标题栏 (`titleBar`)
视频播放器的标题栏：

```json
{
  "display": "flex",        // 弹性布局
  "justifyContent": "space-between",  // 两端对齐
  "alignItems": "center",   // 垂直居中
  "marginBottom": "8px"     // 底部间距
}
```

#### 标题文字 (`title`)
标题栏中的文字样式：

```json
{
  "color": "white",         // 白色文字
  "fontSize": "12px",       // 字体大小
  "fontWeight": "bold",     // 粗体
  "margin": "0"            // 无外边距
}
```

### 视频窗口配置 (`videoWindow`)

#### 默认位置 (`defaultPosition`)
新窗口打开时的位置：

```json
{
  "offsetFromRight": 820,   // 距离屏幕右侧的像素数
  "offsetFromTop": 50      // 距离屏幕顶部的像素数
}
```

#### 窗口大小 (`size`)
新窗口的尺寸：

```json
{
  "width": 800,            // 窗口宽度（像素）
  "height": 600           // 窗口高度（像素）
}
```

#### 窗口特性 (`features`)
浏览器窗口的特性字符串：

```json
{
  "features": "width=800,height=600,scrollbars=no,resizable=yes,status=no,location=no,toolbar=no,menubar=no"
}
```

## 🔧 使用方法

### 1. 修改配置
直接编辑 `video-player-config.json` 文件，修改您需要的参数。

### 2. 重新加载
配置会在下次创建视频播放器时自动加载，无需重启应用。

### 3. 测试配置
可以使用测试页面 `test-video-config.html` 来预览配置效果。

## 📝 常见配置示例

### 左上角显示
```json
{
  "top": "20px",
  "left": "20px",    // 注意：需要同时移除 "right" 属性
  "right": "auto"
}
```

### 底部居中显示
```json
{
  "position": "fixed",
  "bottom": "20px",
  "left": "50%",
  "transform": "translateX(-50%)",
  "top": "auto",
  "right": "auto"
}
```

### 更大的播放器
```json
{
  "width": "500px",
  "height": "375px"
}
```

### 透明背景
```json
{
  "background": "rgba(0,0,0,0.5)"  // 50% 透明度
}
```

## ⚠️ 注意事项

1. **CSS属性名**: 配置中使用驼峰命名法（如 `borderRadius`），系统会自动转换为CSS格式（`border-radius`）
2. **单位**: 数值需要包含单位（如 `"20px"`, `"100%"`）
3. **颜色**: 支持所有CSS颜色格式（十六进制、RGB、RGBA等）
4. **备份**: 修改配置前建议备份原文件
5. **语法**: 确保JSON格式正确，注意逗号和引号

## 🐛 故障排除

### 配置不生效
1. 检查JSON语法是否正确
2. 确认文件路径是否正确
3. 查看浏览器控制台是否有错误信息

### 播放器显示异常
1. 检查CSS属性值是否有效
2. 确认z-index值是否足够大
3. 验证位置属性是否冲突

### 无法加载配置文件
1. 确认文件存在且可读
2. 检查相对路径是否正确
3. 验证服务器是否正常运行

## 📞 技术支持

如有问题，请检查：
1. 浏览器开发者工具的控制台输出
2. 网络请求是否成功
3. 配置文件的JSON格式是否正确
